---

## user/saves.coffee

### GET ':feature'
- **user 字段使用：**
  - `user._id`：用于判断是否在某个Group。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## user/wesite.coffee

### GET ''
- **user 字段使用：**
  - `user._id`：作为 wesite 页面的 uid。

### POST 'userInfo'
- **user 字段使用：**
  - `user._id`：查找 profile。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## user/watch.coffee

### POST 'watching'
- **user 字段使用：**
  - `user._id`：获取关注列表。

### dataMethods.favCmtys
- **user 字段使用：**
  - `user._id`：获取关注小区。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## user/chat.coffee

### GET 'list'、GET 'w/:id'、GET 'u/:uid'
- **user 字段使用：**
  - 仅用于登录校验和下游传递，无直接属性使用。
  - 但在 getSingleChat 等下游函数中会用到 `user._id`、`user.eml`。

```js
/**
 * @property {string} _id 用户唯一ID
 * @property {string} eml 用户邮箱
 */
```

---

## user/userFile.coffee

### POST 'upOne'、'upOneSuccess'、'deleteFiles'、'uploadSuccess'、'uploadFail'、GET 'userFiles.json'、POST 'rmSign'、GET 'insert'
- **user 字段使用：**
  - `user._id`：作为文件归属。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## user/settings.coffee

### GET/POST/VIEW 多处
- **user 字段使用：**
  - `user._id`：用于 profile、通知、订阅、编辑等。
  - `user.eml`：用于邮箱相关操作。
  - `user.avt`：头像。
  - `user.stars`：星级。
  - `user.roles`：角色。
  - `user.pn`：推送token。
  - `user.nm`、`user.fn`、`user.ln`、`user.sgn`、`user.mbl`、`user.locale`、`user.wx`、`user.wxuid`、`user.wxAppOID`、`user.wxMobOID`、`user.facebookId`、`user.googleId`、`user.appleId`、`user.qrcd`、`user.grpqrcd`、`user.nm_en`、`user.nm_zh`、`user.cpny_en`、`user.cpny_zh`、`user.cpny_pstn`、`user.tel`、`user.fax`、`user.cpny_wb`、`user.addr`、`user.web`、`user.qq`、`user.wurl`、`user.fburl`、`user.twturl`、`user.wpHost`、`user.wpUsername`、`user.wpPwd`、`user.wpSecret` 等。

```js
/**
 * @property {string} _id 用户唯一ID
 * @property {string} eml 邮箱
 * @property {string} avt 头像
 * @property {number} stars 星级
 * @property {array} roles 角色
 * @property {string} pn 推送token
 * @property {string} nm 昵称
 * @property {string} fn 名
 * @property {string} ln 姓
 * @property {string} sgn 签名
 * @property {string} mbl 手机号
 * @property {string} locale 语言
 * @property {string} wx 微信ID
 * @property {string} wxuid 微信unionid
 * @property {string} wxAppOID 微信App openid
 * @property {string} wxMobOID 微信Mob openid
 * @property {string} facebookId Facebook ID
 * @property {string} googleId Google ID
 * @property {string} appleId Apple ID
 * @property {string} qrcd 微信二维码
 * @property {string} grpqrcd 微信公众号二维码
 * @property {string} nm_en 英文名
 * @property {string} nm_zh 中文名
 * @property {string} cpny_en 公司英文名
 * @property {string} cpny_zh 公司中文名
 * @property {string} cpny_pstn 公司职位
 * @property {string} tel 电话
 * @property {string} fax 传真
 * @property {string} cpny_wb 公司网址
 * @property {string} addr 公司地址
 * @property {string} web 个人网站
 * @property {string} qq QQ
 * @property {string} wurl 微博
 * @property {string} fburl Facebook
 * @property {string} twturl Twitter
 * @property {string} wpHost WordPress Host
 * @property {string} wpUsername WordPress 用户名
 * @property {string} wpPwd WordPress 密码
 * @property {string} wpSecret WordPress 密钥
 */
```

---

## prop/propClaim.coffee

### GET ''、POST 'edit'、'delete'、'detail'、'list'、'map'
- **user 字段使用：**
  - `user._id`：作为用户唯一标识，参与 group 校验、claim 操作等。
  - `user.eml`：部分日志输出。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 * @property {string} eml 用户邮箱
 */
```

---

## prop/resources.coffee

### POST 'delete'、'addSaveSearch'、'updateSavedSearch'、'recentFavProps' 等
- **user 字段使用：**
  - `user._id`：作为用户唯一标识，增删查改收藏、搜索、房源等。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## prop/mapSearchProp.coffee

### dataMethods.savedSearches、show_map
- **user 字段使用：**
  - `user._id`：用于查找用户收藏、地图展示等。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## adRelated/cpmAds.coffee

### GET/POST 'currentAD'、'manage'、'edit'、'addCpmBalance'、'changeStatus'
- **user 字段使用：**
  - `user._id`：记录操作人ID（如广告历史、加余额等）。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## 0_common/home.coffee

### _getJumpUrl、APP 'app'、APP 'rauth' 等
- **user 字段使用：**
  - 仅用于登录校验和下游传递，无直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## 0_common/l10n.coffee

### GET/POST/POST 'ajax'
- **user 字段使用：**
  - `user._id`：用于权限校验。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## 0_common/transaction.coffee

### addTransaction、POST 'add'、POST 'all'
- **user 字段使用：**
  - `user._id`：作为操作人ID。
  - `user.eml`：作为操作人邮箱。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 * @property {string} eml 用户邮箱
 */
```

---

## auth/provider.coffee

### GET 'authorize'
- **user 字段使用：**
  - `user._id`：用于生成授权码。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## auth/index.coffee

### POST 'apple'、GET ':name'、POST 'bind'
- **user 字段使用：**
  - 仅用于登录校验和下游传递，无直接属性使用。
  - 主要通过 handleOauthUser 等下游函数处理 user 相关逻辑。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## ioNhooks/scheme.coffee

### GET (主路由)、GET 'jump'
- **user 字段使用：**
  - 仅用于登录校验和下游传递，无直接属性使用。
  - 主要用于判断是否登录，未直接访问 user 字段属性。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## ioNhooks/crime.coffee

### 全部路由
- **user 字段使用：**
  - 本文件未直接调用 appAuth 进行 user 字段校验，主要为数据导入、鉴权、邮件通知等功能。
  - 若后续有 appAuth 相关调用，建议补充说明。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## propRelated/mapMixView.coffee

### GET 'mixview'
- **user 字段使用：**
  - 仅用于登录校验和下游传递，无直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## propRelated/contactRealtor.coffee

### POST ''、GET 'manage'、POST 'updateSysdata'、POST 'updateCrmUserStatus'、POST 'getCrmUsers'、POST 'getContactRealtorUserStatus'
- **user 字段使用：**
  - `user._id`：用于查找、校验、分配、状态查询等。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## propRelated/evaluation.coffee

### POST (主路由)、addOwner、delete、props、userHist、propsByLatLng 等
- **user 字段使用：**
  - `user._id`：用于历史记录、房源操作、数据归属、权限校验等。
  - `user.nm`、`user.avt`：用于用户信息展示、记录。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 * @property {string} nm 用户名
 * @property {string} avt 用户头像
 */
```

---

## propRelated/htmltoimg.coffee

### POST 'reload'、GET 'imgflyer'、GET 'templatelist'、POST 'idsData' 等
- **user 字段使用：**
  - `user._id`：用于分组校验、权限判断、日志记录等。
  - `user.eml`：用于日志输出、权限校验。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 * @property {string} eml 用户邮箱
 */
```

---

## propRelated/similarProp.coffee

### POST ''、POST 'building'
- **user 字段使用：**
  - `user._id`：用于分组校验、查找、权限判断等。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## propRelated/tools.coffee

### GET (主路由)
- **user 字段使用：**
  - 本文件未直接调用 appAuth 进行 user 字段校验，主要为工具列表页面展示。
  - 若后续有 appAuth 相关调用，建议补充说明。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## propRelated/toolsMeasure.coffee

### GET 'measure'
- **user 字段使用：**
  - 本文件未直接调用 appAuth 进行 user 字段校验，主要为测量工具页面展示。
  - 若后续有 appAuth 相关调用，建议补充说明。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## propRelated/autocomplete.coffee

### addrSearch、POST 'streetautocomplete' 等
- **user 字段使用：**
  - `user._id`：用于分组校验、查找、权限判断、历史定位等。
  - `user.eml`：用于日志输出。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 * @property {string} eml 用户邮箱
 */
```

---

## propRelated/studentRental.coffee

### GET ''
- **user 字段使用：**
  - 仅用于登录校验和下游传递，无直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## school/mapSchool.coffee

### findMapSchools、findSchools、getSchBnd、getHomeSchools、findPrivateSchools、getPrivateSchool、findUniversities、getUniversity
- **user 字段使用：**
  - 本文件未直接调用 appAuth 进行 user 字段校验，主要为学校数据查询。
  - 若后续有 appAuth 相关调用，建议补充说明。

```js
/**
 * @property {string} _id 用户唯一ID
 */
```

---

## school/schools.coffee

### 各类学校详情、列表、地图、私立/大学/公立学校相关接口
- **user 字段使用：**
  - `user._id`：用于权限校验、token校验、访问统计、分组判断等。
  - `user.eml`、`user.nm`：用于日志、通知、展示等。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 * @property {string} eml 用户邮箱
 * @property {string} nm 用户名
 */
```

---

## map/map.coffee

### GET 'searchLocation'、GET 'notesMap'、GET 'webMap'
- **user 字段使用：**
  - `user._id`：用于登录校验、会话管理、权限判断等。
  - 仅用于登录校验和下游传递，无其他直接属性使用。

```js
/**
 * @property {string} _id 用户唯一ID
 */
``` 