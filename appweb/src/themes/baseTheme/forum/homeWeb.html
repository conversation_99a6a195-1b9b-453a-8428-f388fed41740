{{&
  var {css,js,tabs,id,d} = it;
}}
{{~ css :c:idx}}
<link rel="stylesheet" href="{{= c}}">
{{~}}
<!-- Bootstrap CSS for responsive layout -->
<link rel="stylesheet" href="/css/bootstrap.css">

<div id="loader" class="loader-wrapper"><div class="loader"></div></div>
<div id="forumHome" v-cloak>
  <!-- Web端导航栏 -->
  <nav class="navbar navbar-default navbar-fixed-top">
    <div class="container">
      <div class="navbar-header">
        <button type="button" class="navbar-toggle collapsed" data-toggle="collapse" data-target="#navbar-collapse">
          <span class="sr-only">Toggle navigation</span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
          <span class="icon-bar"></span>
        </button>
        <a class="navbar-brand" href="/">RealMaster Forum</a>
      </div>
      <div class="collapse navbar-collapse" id="navbar-collapse">
        <ul class="navbar-nav navbar-right">
          <li><a href="/app-download">Download App</a></li>
        </ul>
      </div>
    </div>
  </nav>

  <div class="container" style="margin-top: 70px;">
    <div class="row">
      <!-- 主要内容区域 -->
      <div class="col-md-9 col-sm-8">
        <div id="forum-main">
          <flash-message></flash-message>
          <block-popup :flag-post='flagPost'></block-popup>
          <report-forum-form v-if="showReportForm" :owner="{vip:true}" :feedurl="reportForm.feedurl" :title="reportForm.title" :user-form="reportForm.userForm"></report-forum-form>
          
          <!-- 论坛标题和筛选 -->
          <div class="forum-header">
            <h2>{{getTranslate(title)}}</h2>
            <div class="forum-filters" v-if="displayPage=='all'|| hasTag">
              <div class="tag-filter" v-if="hasTag">
                <span class="label label-info">{{-, Filter,forum}}:</span>
                <span class="label label-danger" @click="clearTag('tag')" v-if="tag" style="cursor: pointer; margin-left: 5px;">
                  <span v-if="tag=='HOT'">{{-, HOT,forum}}</span>
                  <span v-else>{{tag}}</span>
                  <span class="glyphicon glyphicon-remove" style="margin-left: 5px;"></span>
                </span>
                <span class="label label-danger" @click="clearTag('gid')" v-if="gid" style="cursor: pointer; margin-left: 5px;">
                  <span>{{gnm}}</span>
                  <span class="glyphicon glyphicon-remove" style="margin-left: 5px;"></span>
                </span>
                <span class="label label-danger" @click="clearTag('src')" v-if="src=='post'" style="cursor: pointer; margin-left: 5px;">
                  <span>{{-, Personal post,forum}}</span>
                  <span class="glyphicon glyphicon-remove" style="margin-left: 5px;"></span>
                </span>
                <span class="label label-danger" @click="clearTag('city')" v-if="curCity.o||curCity.p||curCity.cnty" style="cursor: pointer; margin-left: 5px;">
                  <span>{{ $_(curCity.o||curCity.p||curCity.cnty)}}</span>
                  <span class="glyphicon glyphicon-remove" style="margin-left: 5px;"></span>
                </span>
                <button class="btn btn-sm btn-default" @click="clearTag('all')" style="margin-left: 10px;">
                  <span class="glyphicon glyphicon-remove"></span> Clear All
                </button>
              </div>
              <div v-else>
                <div class="tag-filter all-filter" v-if="displayPage=='all'">
                  <button class="btn btn-sm btn-primary" @click="filterByTag('HOT')">{{-, HOT,forum}}</button>
                  <button class="btn btn-sm btn-info" @click="filterByTag('post')">{{-, Personal Post,forum}}</button>
                  <button class="btn btn-sm btn-info" @click="filterByTag('property')">{{-, Home Review,forum}}</button>
                  <button class="btn btn-sm btn-info" @click="filterByTag('sch')">{{-, School Review,forum}}</button>
                  <button class="btn btn-sm btn-default" v-for="tag in tagList" v-show="tag.key!='HOT'" @click="filterByTag(tag.key)">{{tag.key}}</button>
                </div>
              </div>
            </div>
          </div>

          <!-- 论坛内容容器 -->
          <div id="forum-containter" class="forum-content">
            <check-notification :class-name="'forum'"></check-notification>
            <div class="row">
              <div v-for="post in allPosts" :key="post._id" class="col-xs-12 col-sm-6 col-lg-4">
                <forum-summary-card parent-page="forum" :post="post" :disp-var="dispVar" :display-page="displayPage" :no-tag="noTag" :is-web="true"></forum-summary-card>
              </div>
            </div>
            <div class="text-center" style="padding: 20px;">
              <div class="spinner" v-if="loading">
                <div class="bounce1"></div>
                <div class="bounce2"></div>
                <div class="bounce3"></div>
              </div>
              <button class="btn btn-primary" v-if="posts_more && !loading" @click="loadMore()">
                Load More Posts
              </button>
            </div>
          </div>
        </div>
      </div>

      <!-- 侧边栏 -->
      <div class="col-md-3 col-sm-4">
        <div class="sidebar">
          <!-- 下载App广告 -->
          <div class="panel panel-default">
            <div class="panel-body text-center">
              <h4>Get Our Mobile App</h4>
              <p>Access forum on the go with our mobile app</p>
              <a href="/app-download" class="btn btn-success btn-block">
                <span class="glyphicon glyphicon-download-alt"></span> Download App
              </a>
            </div>
          </div>

          <!-- 热门标签 -->
          <div class="panel panel-default" v-if="tagList.length > 0">
            <div class="panel-heading">
              <h4 class="panel-title">Popular Tags</h4>
            </div>
            <div class="panel-body">
              <span v-for="tag in tagList.slice(0, 10)" class="label label-default tag-item" @click="filterByTag(tag.key)" style="cursor: pointer; margin: 2px;">
                {{tag.key}}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<script type="text/javascript">
  var TRANSLATES = {
      /* forum type translation*/
    "News":"{{-, News,forum}}",
    "Topic":"{{-, Topic,forum}}",
    "My Post":"{{-, My Post,forum}}",
    "My Reply":"{{-, My Reply,forum}}",
    "My Group":"{{-, My Group,forum}}",
    "My Favourite":"{{-, My Favourite,forum}}",
    "My Admin Posts":"{{-, My Admin Posts,forum}}",
    "enter":"{{- Enter forum id or url}}",
  };
</script>

{{~ js :j:idx}}
<script type="text/javascript" src="{{= j}}"></script>
{{~}}
