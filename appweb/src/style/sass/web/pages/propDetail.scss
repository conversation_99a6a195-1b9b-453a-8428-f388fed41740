@import '../../base';
@import '../components/breadcrumbs';
@import '../components/propCommon';
@import './login.scss';
@import '../components/swiper';
@import '../components/loginPopUp';
@import '../../apps/components/boxSelectBtn.scss';

/*detail page*/
body.disable-scroll {
  @include overflowHidden();
}
#model {
  padding-bottom: 80px;
}

#prop-detail-login-buttons {
  max-width: 390px;
  top: 50%;
  @include css3('transform','translateY(-50%)');
}
#prop-detail-login-buttons {
	position: absolute;
	left: 0;
	right: 0;
	margin: auto;
  z-index: 2;
}

#prop-detail-login {
  display: block;
  margin: auto;
  width: 186px;
}

.detail-header {
	display: none;
	background: #FFFFFF;
	padding: 15px;
}
.back-button:hover {
	text-decoration: none;
}
.crumbs {
  padding-top: 0;
  padding-bottom: 0;
}
.breadcrumb-title {
	font-size: 12px;
	display: inline-block;
	margin: 0;
}
.detail-photo {
  @include css3('transition','0.3s ease-in-out');
  height: 350px;
  &:hover {
    @include css3('transform','scale(1.01)');
  }
}
#photos-gallery {
  display: none;
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 10000;
  background: #282828;
  &-close {
    position: absolute;
    top: 20px;
    right: 20px;
    color: #ffff;
    font-size: 24px;
    z-index: 10001;
    cursor: pointer;
  }
}
.detail {
  &-container {
    position: relative;
    z-index: 1;
    margin-bottom: 25px;
    &.has-photo {
      margin-top: -30px;
    }
  }
  &-prop {
    background: #FFFFFF;
    position: relative;
    margin: 0;
    .listing-prop-detail {
      padding: 20px 0;
    }
  }
  &-price {
    font-size: 26px;
    margin-right: 5px;
  }
  &-lp {
    text-decoration: line-through;
  }
  &-section {
    background: #FFFFFF;
    padding: 15px;
    margin-top: 20px;
    position: relative;
    &-title {
      color: $mainColor;
      text-transform: uppercase;
      margin-top: 0;
    }
    &-subtitle {
      font-size: 20px;
      font-weight: bold;
    }
    &-right-link {
      position: absolute;
      right: 20px;
      top: 16px;
      font-size: 12px;
      .fa {
        margin-left: 5px;
      }
    }
  }
}
#location-section {
	padding-bottom: 0;
}
.prop_detail_map {
  margin-top: 40px;
  text-align: right;
  display: block;
  @include css3('transition','0.3s');
  &_icon {
    width: 70px;
  }
  &_text {
    color: #626d70;
    font-size: 16px;
    &:hover {
      color: #337ab7;
    }
  }
}
.prop-summary-row {
  @include css3('transition','0.3s ease');
  display: flex;
  margin: 5px 0;
}
.summary {
  &-label {
    font-weight: bold;
    width: 30%;
  }
  &-value {
    width: 70%;
    text-align: right;
    word-break: break-word;
    vertical-align: top;
  }
}
.detail-room {
  &-head {
    background: #f4f4f4;
    margin: 0;
    padding: 10px 0;
    font-weight: bold;
  }
  &-row {
    margin: 0 0;
    padding: 10px 0 0;
    border-bottom: 1px solid #f9f9f9;
  }
  &-des {
    margin-left: 5px;
    opacity: 0.6
  }
}
.detail-room-head > div,
.detail-room-row > div {
	padding: 5px;
}
.detail-map {
  &-links {
    border-top: 1px solid #cccccc;
    margin-top: 20px;
  }
  &-link {
    text-align: center;
    font-size: 16px;
    padding: 15px;
    border-right: 1px solid #cccccc;
    &:last-child {
      border-right: none;
    }
  }
}
.copyright-desc {
	font-size: 14px;
	margin-bottom: 10px;
}
.disclaimer {
  color:#777;
  font-size:12px;
  margin-top: 10px;
  border-top: 1px solid #ccc;
  padding-top: 10px;
  position:relative;
  .text {
    height: 15px;
    display: block;
    overflow: hidden;
  }
  .expand {
    position: absolute;
    right:0px;
    top:21px;
    cursor: pointer;
  }
}
.copyright-remark {
	font-size: 12px;
}
// .copyright-imgs {
//   text-align: center;
// }
// .copyright-img {
// 	display: inline-block;
// 	vertical-align: middle;
// }
// .copyright-img.mls {
// 	width: 65px;
// }
// .copyright-img.realtor {
// 	width: 17px;
// 	margin-left: 10px;
// }
.detail-oh-row {
	border-top: 1px solid #ddd;
  padding: 10px 0;
  display: flex;
  align-items: center;
}
.detail-oh-day,
.detail-oh-times {
	display: inline-block;
	vertical-align: middle;
}
.detail-oh-day {
	font-size: 30px;
	margin-right: 20px;
}
.detail-oh-link-wrapper {
  float: right;
  margin-left: auto;
}
.detail-oh-link {
  font-size: 14px;
  margin-right: 20px;
  float: right;
  margin-left: auto;
  padding-right: 10px;
  color:#777;
  padding-left: 10px;
}
.detail-oh-link-txt {
  padding-left:10px
}
.detail-oh-link-online {
  color: #428bca;
}
.detail-oh-times {
	font-size: 14px;
}



.school {
  border-top: 2px solid #ebebeb;
  position: relative;
  margin-bottom: 15px;
  padding-top: 10px;
  &-type {
    position: absolute;
    top: 20px;
    right: 0;
    color: #666;
  }
  &-title {
    font-size: 14px;
    font-weight: bold;
    text-transform: uppercase;
    width: calc(100% - 100px);
  }
  &-location {
    margin-bottom: 0px;
  }
  &-address {
    display: inline-block;
    margin-right: 10px;
    color: #666;
  }
  &-distance {
    display: inline-block;
    color: #f0951c;
  }
  &-categories {
    margin: 5px 0 0;
  }
  > span {
    background: #6FCE1B;
  }
  &-category {
    &:first-child {
      background-color: #6FCE1B;
    }
    background-color: #F0951C;
    color: #ffffff;
    display: inline-block;
    padding: 1px 10px;
    border-radius: 30px;
    margin: 0 10px 10px 0;
  }
  &-rating {
    .fa-long-arrow-down {
      padding:0 3px;
      color:#E03131;
      font-size:11px
    }
    .fa-long-arrow-up {
      padding:0 3px;
      color:#2fa800;
      font-size:11px
    }
  }
}
.listing-agent {
  display: flex;
  align-items: center;
  margin:20px 0;
  &-name {
    font-weight: bold;
    font-size: 16px;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
    max-width: calc(100% - 55px);
    display: inline-block;
    vertical-align: middle;
  }
  &-label {
    display: inline-block;
    vertical-align: middle;
    color:#fff;
    margin-left:5px;
    padding:1px 2px;
    border-radius: 3px;
    font-size: 12px;
    line-height: 14px;
    &.cert {
      background-color: #1EB1ED;
    }
    &.reco {
      background-color: #efc439;
    }
  }
  &-avtContainer {
    max-width: 66px;
    width:20%;
  }
  &-avt {
    width: 100%;
    border-radius: 50%;
  }
  &-info {
    font-size:14px;
    padding: 0 10px;
    width: calc(100% - 66px - 20px);
  }
  &-mbl {
    display: none;
    margin: 5px 0;
    .fa {
      margin-right: 5px;
      color:green;
    }
  }
  &-cpny,&-pst {
    color:#777;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }
  &-checkbox {
    width: 20px;
    height: 20px;
    border: 1px solid #d65050;
    cursor: pointer;
    transition: 0.3s;
    position: relative;
    &.selected {
      background-color: #d65050;
      &:before {
        -webkit-transform: rotate(45deg);
        -ms-transform: rotate(45deg);
        transform: rotate(45deg);
        position: absolute;
        left: 4px;
        top: 0;
        display: table;
        width: 9px;
        height: 12px;
        border: 2px solid #fff;
        border-top: 0;
        border-left: 0;
        content: " ";
      }
    }
  }
  &-website .fa-angle-right {
    margin-left: 5px;
    color:#777;
  }
  &-btn {
    margin-left: 5px;
    padding: 5px;
    width: 115px;
  }
}
/*style listingagent*/
.listing-agents {
  display: none;
}
.table-view-cell {
  position: relative;
}
/*end of listingagent style*/
.request-form .search-field {
	width: 100%;
}
.detail-section-msg {
  padding: 20px 0;
}
.submitLink {
  width: 100%;
}
#firstLastNameContainer {
  display: flex;
  .search-field-container {
    width: calc(50% - 5px);
    position: relative;
    &:first-child {
      margin-right: 10px;
    }
  }
}
#wxLineContainer {
  display: flex;
  position: relative;
  .line {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 50%;
    width: 1px;
    height: 50%;
    background-color: #ccc;
    z-index: 1;
  }
  .search-field-input,
  .wxLineMode {
    width: 50%;
  }
  .search-field-input {
    border-right: none;
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
    position: relative;
  }
  .wxLineMode {
    padding: 10px;
    border: 1px solid #cccccc;
    border-top-left-radius: 0;
    border-bottom-left-radius: 0;
    border-left: none;
    outline: none;
    background-color: #FFF;
    @include css3('appearance','none');
  }
  .fa-angle-down {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 10px;
    left: auto;
  }
  #line {
    display: none;
  }
}
#request-form-success {
	margin-top: 20px;
	display: none;
	background-color:#d4faaa;
	text-align:center;
	padding:30px 0;
	border:1px solid #ddd;
}
#request-form-success .fa {
	color: #80d820;
	margin-right:7px;
}
#request-form-failed {
	margin-top: 20px;
	display: none;
	background-color:#d65050;
	text-align:center;
	padding:30px 0;
	border:1px solid #ddd;
}
#request-form-failed .fa {
	color:#d65050;
	margin-right:7px;
}
#send-request,
#open-login-form {
	width: 100%;
}
#send-request .submiting {
	display: none;
}
.textarea-input {
  padding: 10px;
  width: 100%;
  border: 1px solid #e7e5e8;
  background: #f9f9f9;
  line-height: 1.5;
  margin-bottom:20px;
}
// overwrite propCard
.heart-icons{
  &[role="button"]{
    position: unset;
  }
}

@media (min-width: 768px) {
  #prop-detail-login {
    width: 100%;
  }
  .request-form {
    margin-top: 0;
  }
  .detail-header {
    display: block;
  }
  .listing-agent-website.prop-button {
    display: inline-block;
  }
}
.buiding-detail-block {
  margin:0 0 20px 0;
}
.buiding-detail-block-label {
  width:100%;
  font-weight:bold;
}
.goLogin{
  text-align: right;
  color: #428bca;
}

.toggle {
  position: relative;
  display: block;
  width: 46px;
  height: 24px;
  background-color: #ddd;
  border: 2px solid #ddd;
  border-radius: 20px;
  -webkit-transition-duration: .2s;
     -moz-transition-duration: .2s;
          transition-duration: .2s;
  -webkit-transition-property: background-color, border;
     -moz-transition-property: background-color, border;
          transition-property: background-color, border;
  .toggle-handle {
    position: absolute;
    left: -1px;
    z-index: 2;
    top: 0px;
    width: 20px;
    height: 20px;
    background-color: #fff;
    border: 1px solid #ddd;
    border-radius: 100px;
    -webkit-transition-duration: .2s;
      -moz-transition-duration: .2s;
            transition-duration: .2s;
    -webkit-transition-property: -webkit-transform, border, width;
      -moz-transition-property:    -moz-transform, border, width;
            transition-property:         transform, border, width;
    }
  &:before {
    position: absolute;
    top: -1px;
    right: 8px;
    font-size: 11px;
    color: #fff;
    text-transform: capitalize;
    width: 10px;
    text-align: center;
    content: "M";
    line-height: 21px;
  }
  &.active {
    background-color: #40bc93;
    border: 2px solid #40bc93;
    .toggle-handle {
      border-color: #5cb85c;
      -webkit-transform: translate3d(24px, 0, 0);
          -ms-transform: translate3d(24px, 0, 0);
              transform: translate3d(24px, 0, 0);
    }
    &:before {
      right: auto;
      left: 8px;
      color: #fff;
      content: "Ft";
    }
  }
}
.notesBtnPot{
  position: fixed;
  z-index: 10;
  right: 10%;
  bottom: 12%;
}
.notesBtn{
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 50px;
  height: 50px;
  background:#fff;
  border-radius: 50%;
  border: 1px solid #fff;
  color: #e03130;
  box-shadow: 0px 0px 5px 4px rgba(0,0,0,.12);
  padding: 0;
}
.noteIcon{
  margin-top: 4px;
  font-size: 24px;
}
.soldRedTag{
  font-size:18px;
  font-weight: normal;
  background: #e03131;
  padding: 0 3px;
  color: #fff;
  border-radius: 2px;
  margin-right: 3px;
}
#prop-detail-page .school-rating-wrapper{
  display: flex;
  justify-content: space-between;

  .bold{
    font-weight: bold;
  }
  p {
    margin-bottom: 3px;
  }
}