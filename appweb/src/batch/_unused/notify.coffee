# NOTE: this file no longer used !!!!
puts = console.log

cfg =
  twilio:
    sid: '**********************************'
    authToken: '5ccb19e204f6cfa14dd333c1db9649e8'
libSendSMS = require('./sendSMS').getSendSMS cfg

sendSMS = (msg,cb)->
  sms =
    To: '+16472223733'
    Body: msg
    From: '+19056142609'#req.setting?.mbl or '+16472223733'
  libSendSMS sms,(err,result)->
    console.error err if err
    puts result if result
    puts 'sendSMS'
    cb() if cb

path = require('path')
programName = path.basename(__filename)
programNameL = programName.length
if process.argv.length > 2 and process.argv[0] is 'coffee' and process.argv[1].substr(-programNameL) is programName
  msg = process.argv[2]
  puts "Notify: #{msg}"
  sendSMS msg,->
    process.exit 0
else
  module.exports.sendSMS = sendSMS