###
mongoImport.coffee [options] [import-folder]
-c --console: read watch from console.
  when no file or folder is specified, this is the default method.
-f --file: input file name
-u --user: user name for database
-P --password: password
-d --database: database name
-h --host: host name
-p --port: db port number
-e --exclude: do not import excluded collections. separated by ,
-i --include: only import included collections. separated  by ,
-z --gzip: decompress file gzip format
--ignore-json-error: ignore json parsing error, and continue
-v --verbose: verbose mode.
When json parsing error, output error and exit 1

All parameters can be read from environment settings
process.env.RMI_CONSOLE/FILE/USER/PASSWORD/DATABASE/
  HOST/PORT/EXCLUDE/INCLUDE/GZIP/IGNORE_JSON_ERROR/VERBOSE
###
# line by line stream
# https://stackoverflow.com/questions/16010915/parsing-huge-logfiles-in-node-js-read-in-line-by-line
# process.stdin is a stream

###
#  ./start.sh batch/dbScript/mongoImport.coffee -u pgusr -P pgpwd./
###

fs = require 'fs'
path = require 'path'
es = require('event-stream')
readline = require('readline')
MongoClient = require('mongodb').MongoClient
ObjectId = ObjectID = require('mongodb').ObjectId
zlib = require 'zlib'
helpers   = require '../../lib/helpers'
debugHelper = require '../../lib/debug'

yargs = require('yargs') # eslint-disable-line
yargs
  .option 'console', { alias: 'c', description: 'read watch from console'}
  .option 'file', { alias: 'f', type: 'string', description: 'input file name'}
  .option 'user', { alias: 'u', description: 'user name for database'}
  .option 'password', { alias: 'P', description: 'password'}
  .option 'database', { alias: 'd', description: 'database name'}
  .option 'host', { alias: 'h', description: 'host name'}
  .option 'port', { alias: 'p', description: 'db port number'}
  .option 'exclude', { alias: 'e', description: 'collections to be excluded'}
  .option 'include', { alias: 'i', description: 'collections to be included'}
  .option 'gzip', { alias: 'z', description: 'decompress file gzip format'}
  .option 'ignore-json-error', { description: 'ignore json parsing error'}
  .option 'ignore-update-error', { description: 'ignore database update error'}
  .option 'ignore-broken-file', { description: 'ignore broken gzip file'}
  .option 'verbose', { alias: 'v', description: 'verbose mode'}
  .help('help')

opt = yargs.argv
# if opt.console
#   puts = console.error

debug = debugHelper.getDebugger()
debug.debug opt
# conn = '*************************************'
# 设置连接url
host = opt.host or 'localhost'
port = opt.port or '27017'
conn = host + ':' + port
connParams = []
if opt.database
  conn = conn+'/'+opt.database
if opt.user and opt.password
  conn = 'mongodb://'+encodeURIComponent(opt.user)+':'+\
    encodeURIComponent(opt.password)+'@'+conn
  connParams.push 'authSource=admin'
else
  conn = 'mongodb://'+conn
if opt.replicaset
  connParams.push 'replicaSet='+opt.replicaset
if connParams.length > 0
  conn += '?'+connParams.join('&')

database = opt.database
lineNumber = 0
input = null
collection = {}

exitWithError = (err)->
  debug.critical err
  process.exit 1

includeColls = null
excludeColls = null
# 设置监听的表
if opt.include
  includeColls = {}
  for coll in opt.include.split(',')
    includeColls[coll] = 1
if opt.exclude
  if includeColls
    debug.error 'Can not specify exclude together with include'
    process.exit 1
  excludeColls = {}
  for coll in opt.exclude.split(',')
    excludeColls[coll] = 1

IGNORE_JSON_ERROR = opt['ignore-json-error'] or process.env.IGNORE_JSON_ERROR
IGNORE_UPDATE_ERROR = opt['ignore-update-error'] or process.env.IGNORE_UPDATE_ERROR
IGNORE_BROKEN_FILE = opt['ignore-broken-file'] or process.env.IGNORE_BROKEN_FILE


callbackCheckError = (err,cb,line)->
  if IGNORE_UPDATE_ERROR
    debug.warn 'Ignored Error',err,line if err
    return cb()
  if err
    debug.error line
    return exitWithError err
  cb err

toObjectID = (fid)->
  if ('string' is typeof id) and /^[a-f0-9]{24}$/i.test(id)
    return new ObjectId(id)
  fid

fnUpdate = (line,db,cb)->
  debug.debug 'update',line.ns.db,line.ns.coll,line.key._id,line.up
  dbo = db.db(database or line.ns.db)
  updateStr = {}
  updateStr.$set = line.up if line.up and Object.keys(line.up).length > 0
  if line.rm?.length
    updateStr.$unset = {}
    for k in line.rm
      updateStr.$unset[k] = 1
  if (not updateStr.$set) and (not updateStr.$unset)
    return callbackCheckError new Error('No update fields'),cb,line
  dbo.collection(line.ns.coll).updateOne {'_id':toObjectID(line.key._id)},\
      updateStr, {upsert:true}, (err, res)->
    callbackCheckError err,cb,line
fnInsert = (line,db,cb)->
  debug.debug 'insert',line.ns.db,line.ns.coll,line.fd
  dbo = db.db(database or line.ns.db)
  dbo.collection(line.ns.coll).replaceOne {_id:toObjectID(line.fd._id)},\
      line.fd, {upsert:true}, (err, res)->
    callbackCheckError err,cb,line
fnRreplace = (line,db,cb)->
  debug.debug 'replace',line.ns.db,line.ns.coll,line.fd
  dbo = db.db(database or line.ns.db)
  dbo.collection(line.ns.coll).replaceOne {'_id':toObjectID(line.key._id)},\
      line.fd, {upsert:true}, (err, res)->
    callbackCheckError err,cb,line
fnDelete = (line,db,cb)->
  debug.debug 'delete',line.ns.db,line.ns.coll,line.key
  dbo = db.db(database or line.ns.db)
  dbo.collection(line.ns.coll).deleteOne {'_id':toObjectID(line.key._id)}, (err, res)->
    callbackCheckError err,cb,line
fnDrop = (line,db,cb)->
  debug.warn 'drop',line
  debug.debug 'drop',line.ns.db,line.ns.coll
  dbo = db.db(database or line.ns.db)
  dbo.dropCollection line.ns.coll,(err, delOK) ->
    callbackCheckError err,cb,line
fnRename = (line,db,cb)->
  debug.warn 'rename',line
  debug.debug 'rename',line.ns.db,line.ns.coll,line.to.coll
  dbo = db.db(database or line.ns.db)
  dbo.collection(line.ns.coll).rename line.to.coll,\
      {dropTarget:true}, (err, collection2) ->
    callbackCheckError err,cb,line
fnDropDatabase = (line,db,cb)->
  debug.debug 'dropDB',line.ns.db
  dbo = db.db(database or line.ns.db)
  dbo.dropDatabase (err, result) ->
    callbackCheckError err,cb,line

eventActions =
  update: fnUpdate
  insert: fnInsert
  replace: fnRreplace
  delete: fnDelete
  drop: fnDrop
  rename: fnRename
  dropDatabase: fnDropDatabase

gStat = {}

changeFn = (obj,db,cb) ->
  unless fn = eventActions[obj.optp]
    debug.error new Error("Need #{obj.optp} function at line #{lineNumber}")
  gStat[obj.optp] ?= 0
  gStat[obj.optp]++
  fn obj,db,cb


MongoClient.connect conn, {}, (err, db) ->
  exitWithError err if err
  debug.verbose 'MongoClient connected'
  allDone = ()->
    db.close()
    debug.verbose 'mongoClient closed'
    debug.info 'All Done',gStatTotal
    process.exit 0
  importFolders = (folders,base,cb)->
    doOne = ->
      if folder = folders.pop()
        folderPath = path.resolve base,folder
        fileStat = fs.lstatSync folderPath
        if fileStat.isDirectory()
          files = fs.readdirSync(folderPath).filter (fnm)-> not /^\./.test fnm
          importFolders files,folderPath,doOne
        else if fileStat.isFile()
          importFile fs.createReadStream(folderPath),db,folderPath,doOne
        else
          debug.error 'Unsupported file type',fileStat
          doOne()
      else
        cb()
    doOne()
  # 设置文件输入方式
  if opt.file
    importFile fs.createReadStream(opt.file),db,opt.file,allDone
  else if opt.console
    stdInFileName = 'STDIN'
    if opt.gzip
      stdInFileName += '.gz'
    importFile process.stdin,db,stdInFileName,allDone
  else if opt._?.length
    importFolders opt._,process.cwd(),allDone
  else
    yargs.help()
    exitWithError 'Need to specify file, folder or console'

gStatTotal = {}

importFile = (inStream,db,fname,cb)->
  cntToDo=0
  cntDone=0
  finished=false
  checkDone = (callback)->
    cntDone++ if callback
    debug.debug 'checkDone',cntDone,cntToDo,finished
    if (cntToDo <= cntDone) and finished
      debug.info 'done',fname,'Total',cntDone,'/',cntToDo,gStat
      for k,n of gStat
        gStatTotal[k] ?= 0
        gStatTotal[k] += n
      gStat = {}
      cb() # original backback
    else
      if callback
        callback()
  onEnd = ->
    debug.verbose 'Read entire file',fname
    finished = true
    checkDone()
  if /\.gz$/.test(fname)
    inStream.on 'end',onEnd
    gzipStream = zlib.createGunzip()
    inStream = inStream.pipe gzipStream
    gzipStream.on 'error',(err)->
      if IGNORE_BROKEN_FILE
        gzipStream.close()
        inStream.close()
        debug.warn 'close file',fname,'ignore error in gzip file',err
        return cb()
      exitWithError err
  debug.info 'read file',fname
  s = inStream
  .pipe es.split()
  .pipe es.map (line,cb)->
      #  pause the readstream
    ps = es.pause()
    callback = ->
      ps.resume()
      cb()
    lineNumber += 1
    if line.length
      try
        obj = helpers.JSONParse line
      catch jsonError
        debug.error 'Json Error Line',lineNumber,jsonError
        return if IGNORE_JSON_ERROR
        return exitWithError jsonError
      cntToDo++ #有内容计数
      if includeColls
        if includeColls[obj.ns.coll]
          changeFn obj,db,->
            checkDone callback
        else
          callback()
      else if excludeColls
        if not excludeColls[obj.ns.coll]
          changeFn obj,db,->
            checkDone callback
        else
          callback()
      else
        changeFn obj,db,->
          checkDone callback
  s.on 'error', (err) ->
    debug.error "Error while reading file. At line #{lineNumber}"
    exitWithError err
  s.on 'end', onEnd
  
