###
https://www.npmjs.com/package/yargs
https://www.npmjs.com/package/argparse

mongoWatch.coffee
-c --console: output to console, default.
 When -c, debug and message shall use console.error
-o --output: output folder
-f --file: output file name
-u --user: user name for database
-P --password: password
-d --database: database name. When db not specified, use the MongoClient watch.
 https://docs.mongodb.com/manual/changeStreams/ A deployment
-h --host: host name
-p --port: db port number
-e --exclude: collections to be excluded. separated by ,.
-i --include: collections to be included. separated by ,.
 When use -i, all others are excluded. -i takes precedence.
-s --start: startAtOperationTime timestamp. 20200504T120000(Z) format.
 Without Z, use location timezone.
http://mongodb.github.io/node-mongodb-native/3.5/api/Db.html#watch
-z --gzip: output gziped file (filename would be .gz after rotation)
https://nodejs.org/api/zlib.html#zlib_class_zlib_gzip or
 use shell command "gzip filename"
-C --command: run a shell command for yesterday's file.
 filename(folder if no -f) will at the end of the command.
https://stackabuse.com/executing-shell-commands-with-node-js/
-v --verbose: verbose mode.
-Z --compress-update: Do not write fullDocuments when update.
--ignore-error: ignore error on change stream. May get > 16M records sometime.

All parameters can be read from environment settings
process.env.RMW_CONSOLE/OUTPUT/FILE/USER/PASSWORD/DATABASE
/HOST/PORT/EXCLUDE/INCLUDE/GZIP/START/VERBOSE

-c/-o/-f can be used at the same time
-o will generate separated files for each collection
filenames for -o: folder-20200508/dbname/collection.watch[.gz]
filenames for -f: filename-20200508db.watch[.gz]

File rotation will happen after midnight, when a new record starts
 with a new date.
1. create new file for the new day.
2. close yesterday's file. gzip it and rename to .gz if -z parameter supplied.
3. run a shall command on the file(folder if no -f) if supplied -C parameter.

watch file format: change object json for each line

expected output format:
{optp:'update/insert/delete...',ns:{db:'..',call:'...'},
  key:{_id:'...'},up:{},fd:{}}
up: for update
fd: for fullDocument
rm: for remove fields
to: for rename event
cts: clusterTime
lsid: session id
txn: txnNumber
have a look of  https://docs.mongodb.com/manual/reference/change-events/

sample change:
{
  _id: {
    _data: '825EB5D2EA000000082B022C0100296E5A10049D723B698B734
      801BFF8A538CEFE8B1C463C5F6964003C5452425734363938373433000004'
  },
  operationType: 'update',
  clusterTime: Timestamp { _bsontype: 'Timestamp', low_: 8, high_: 1588974314 },
  ns: { db: 'listing', coll: 'properties' },
  documentKey: { _id: 'TRBW4698743' },
  updateDescription: {
    updatedFields: { vc: 239, vcapp: 194, vcappc: 168, vcd: 239 },
    removedFields: []
  }
}

db.createRole({role:'watch',privileges:[{resource:{db:"",collection:""},actions:["find","changeStream"]}],roles:[]})
db.grantRolesToUser('pgusr',['watch']);

#  ./start.sh batch/dbScript/mongoWatch.coffee -u pgusr -P pgpwd -d listing./
###

fs = require 'fs'
path = require 'path'
mongodb = require('mongodb')
MongoClient = mongodb.MongoClient
zlib = require('zlib')
helpers   = require '../../lib/helpers'
debugHelper = require '../../lib/debug'
bson = require 'bson'

yargs = require('yargs') # eslint-disable-line
yargs
  .option 'console', { alias: 'c', description: \
    'Debug and message shall use console.error'}
  .option 'folder', { alias: 'o', description: 'output folder'}
  .option 'file', { alias: 'f', type: 'string', \
    description: 'Run with verbose logging'}
  .option 'user', { alias: 'u', description: 'user name for database'}
  .option 'password', { alias: 'P', description: 'password'}
  .option 'database', { alias: 'd', description: 'database name'}
  .option 'replicaset', { alias: 'r', description: 'replicaset name'}
  .option 'host', { alias: 'h', description: 'host name'}
  .option 'port', { alias: 'p', description: 'db port number'}
  .option 'exclude', { alias: 'e', description: 'collections to be excluded'}
  .option 'include', { alias: 'i', description: 'collections to be included'}
  .option 'start', { alias: 's', description: 'startAtOperationTime timestamp'}
  .option 'gzip', { alias: 'z', description: 'output gziped file'}
  .option 'compress-update', { alias: 'Z', description: 'do not save full document for update'}
  .option 'ignore-error', {alias: 'I', description: 'ignore change error and continue watch'}
  .option 'command', { alias: 'C', description: \
    "run a shell command for yesterday's file"}
  .option 'verbose', { alias: 'v', description: 'verbose mode'}
  .help('help')

opt = yargs.argv
debug = debugHelper.getDebugger()

openedFiles = {}
outputFunctions = []
dateNum = helpers.date2num new Date()

exitWithError = (err)->
  debug.critical err
  process.exit 1

# 设置文件输出方式
closeFiles = () ->
  for fnm,f of openedFiles
    f.end()
  openedFiles = {}

openFile = (filePath)->
  if opt.gzip
    filePath = filePath+'.gz'
  unless f = openedFiles[filePath]
    debug.verbose 'Open file',filePath
    stream = fs.createWriteStream filePath
    f = openedFiles[filePath] = stream
    if opt.gzip
      gzip = zlib.createGzip()
      gzip.pipe stream
      f = openedFiles[filePath] = gzip
  f
if opt.folder
  curDatedFolder = null
  curFolders = {}
  ensureDatedFolder = (folder,cb)->
    return cb() if folder is curDatedFolder
    debug.verbose 'Create folder',folder
    helpers.mkdir folder,(err)->
      return exitWithError err if err
      curDatedFolder = folder
      curFolders = {}
      cb()
  ensureDBFolder = (folder,dbName,cb)->
    ensureDatedFolder folder,->
      folderPath = path.join folder,dbName
      return cb folderPath if curFolders[folderPath]
      debug.verbose 'Create DB folder',folderPath
      helpers.mkdir folderPath,(err)->
        return exitWithError err if err
        curFolders[folderPath] = true
        cb folderPath
  outputFunctions.push (jsonString,obj)->
    newDateNum = helpers.date2num new Date()
    if dateNum isnt newDateNum
      closeFiles()
      dateNum = newDateNum
    #folder-20200508/dbname/collection.watch
    ensureDBFolder (opt.folder + '_' + dateNum),obj.ns.db,(folder)->
      filePath = path.join folder,((obj.ns.coll or '_system')+'.watch')
      openFile(filePath).write jsonString
if opt.file
  outputFunctions.push (jsonString,obj)->
    newDateNum = helpers.date2num new Date()
    if dateNum isnt newDateNum
      closeFiles()
    filePath = opt.file + '-' + dateNum + 'db.watch' #filename-20200508db.watch
    openFile(filePath).write jsonString
if opt.console or outputFunctions.length is 0
  debug.dontUserConsoleError true
  consoleOutput = process.stdout
  if opt.gzip
    consoleGzip = zlib.createGzip()
    consoleGzip.pipe consoleOutput
    consoleOutput = consoleGzip
  outputFunctions.push (jsonString,obj)->
    consoleOutput.write jsonString

closeNExit = (code=0)->
  closeFiles()
  setTimeout (-> process.exit code),5000

beforeExit = (sig)->
  process.on sig, ->
    debug.info 'Get signal',sig
    closeNExit 0
beforeExit 'SIGINT'
beforeExit 'SIGQUIT'
beforeExit 'SIGTERM'
beforeExit 'exit'

debug.debug opt

IGNORE_ERROR = opt['ignore-error']

# conn = '*************************************'
# 设置连接url
host = opt.host or 'localhost'
port = opt.port or '27017'
conn = host + ':' + port
connParams = []
if opt.database
  conn = conn+'/'+opt.database
if opt.user and opt.password
  conn = 'mongodb://'+encodeURIComponent(opt.user)+':'+\
    encodeURIComponent(opt.password)+'@'+conn
  connParams.push 'authSource=admin'
else
  conn = 'mongodb://'+conn
if opt.replicaset
  connParams.push 'replicaSet='+opt.replicaset
if connParams.length > 0
  conn += '?'+connParams.join('&')

COMPRESS_UPDATE = opt['compress-update'] or process.env.COMPRESS_UPDATE
debug.debug 'compress-update',COMPRESS_UPDATE

watchOpt = {fullDocument:'updateLookup',batchSize:1,readPreference:mongodb.ReadPreference.NEAREST}

includeCollArray = null
includeColls = null
excludeCollArray = null
excludeColls = null
# 设置监听的表
if opt.include
  includeColls = {}
  includeCollArray = opt.include.split ','
  for coll in includeCollArray
    includeColls[coll] = 1
if opt.exclude
  if includeColls
    debug.error 'Can not specify exclude together with include'
    process.exit 1
  excludeColls = {}
  excludeCollArray = opt.exclude.split ','
  for coll in excludeCollArray
    excludeColls[coll] = 1

# 设置开始时间
if opt.start
  unless /^\d{8}T\d{6}Z?$/i.test opt.start #20200504T120000(Z)
    return exitWithError "Unknown timestamp formate.\
        Need like 20200504T120000Z. Got #{opt.start}"
  startTime =  opt.start
  if startTime.indexOf '-' < 0
    year = startTime.slice(0,4)
    month = startTime.slice(4,6)
    day = startTime.slice(6,8)
    contact = startTime.slice(8,9)
    hour = startTime.slice(9,11)
    minute = startTime.slice(11,13)
    second = startTime.slice(13)
    startTime = year+'-'+ month+'-'+ day+ contact+ hour+':'+ minute+':'+ second
  dt = Date.parse new Date(startTime)
  startTs = new bson.Timestamp(1,dt/1000 - 10*3600)
  debug.verbose 'start time',startTs
  watchOpt.startAtOperationTime = startTs

changeKeyWords = (obj) ->
  return unless Object.keys(obj).length
  abbr =
    optp : obj.operationType
    ns : obj.ns
    cts : obj.clusterTime
  if obj.fullDocument and ((obj.operationType isnt 'update') or not COMPRESS_UPDATE)
    abbr.fd = obj.fullDocument
  abbr.key = obj.documentKey if obj.documentKey?
  abbr.to = obj.to if obj.to?
  if obj.updateDescription?
    abbr.up = obj.updateDescription.updatedFields if obj.updateDescription.updatedFields
    abbr.rm = obj.updateDescription.removedFields if obj.updateDescription.removedFields?.length > 0
  abbr.lsid = obj.lsid if obj.lsid?
  abbr.txn = obj.txnNumber if obj.txnNumber?
  abbr = JSON.stringify abbr
  return abbr + '\n'

debug.info 'Connect',conn
MongoClient.connect conn, {}, (err,db)->
  return exitWithError err if err
  debug.verbose 'MongoClient connect succeed'
  dbWatch = db
  if opt.database
    dbWatch = db.db(opt.database)
    debug.info 'watch','database',opt.database
  else
    debug.info 'watch','mongo instance'
  filter = []
  if includeCollArray
    filter.push {$match:{'ns.coll':{$in:includeCollArray}}}
  else if excludeCollArray
    filter.push {$match:{'ns.coll':{$nin:excludeCollArray}}}
  debug.info 'watchOption',filter,watchOpt
  changeStream = dbWatch.watch filter,watchOpt
  changeStreamMethods changeStream

changeFns = (json,obj)->
  for fn in outputFunctions
    fn json,obj
changeStreamMethods = (changeStream)->
  changeStream.on 'change',(obj)->
    # transform
    json = changeKeyWords obj
    if includeColls
      if includeColls[obj.ns.coll]
        changeFns json,obj
    else if excludeColls
      if not excludeColls[obj.ns.coll]
        changeFns json,obj
    else
      changeFns json,obj
  changeStream.on 'close',()->
    debug.verbose 'stream closed'
    process.exit 0
  changeStream.on 'error',(err)->
    if IGNORE_ERROR
      debug.warn 'ignore error',err
      return
    debug.error 'stream error',err
    closeNExit 1
  changeStream.on 'resumeTokenChanged',(rtoken)->
    debug.debug 'tokenChange',rtoken

process.on 'uncaughtException',(e)->
  if IGNORE_ERROR
    debug.warn 'ignore unexpected error',e
    return
  debug.critical e
  closeNExit 1
