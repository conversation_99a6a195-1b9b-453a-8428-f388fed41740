###
  Description: 从properties数据库找出3天、7天前未更新的房源，把prop._id 加入到队列中，重新导入
  db.properties.find({status:'A',ptype:'r',
    _mt:{
      $gte: ISODate("2024-03-06T19:00:00.000Z"),
      $lt: ISODate("2024-03-06T19:05:00.000Z")
    }}}).count()

  Options:
      -a --days:        query days ago, number (default: 3)
  New config:
    ./start.sh -t batch -n reDownloadRni -cmd "lib/batchBase.coffee batch/prop/reDownloadRni.coffee -a [days]"
  createDate:    2025-06-23
  Author:        Maggie
  Run frequency: Run once (use cronjob for scheduling)

对于reso(TRB),bcreReso(BRE),creaReso(DDF)房源,找到未更新的active房源之后，通过addToQueue重新 download

###

debug = DEBUG()
helpers = INCLUDE 'lib.helpers'
ResourceDownloadQueue = require '../../libapp/mlsResourceDownloadQueue'
speed = INCLUDE 'lib.speed'
reImportHelper = INCLUDE './reImportHelper'
config = CONFIG(['contact', 'mailEngine', 'mailEngineList'])
sendMailLib = INCLUDE 'lib.sendMail'
sendMailObj = sendMailLib.getSendMail(config)

yargs = require('yargs')

PropertiesCol = COLLECTION 'vow', 'properties'
TrbQueueCol = COLLECTION('rni','reso_treb_evow_property_queue')
BreQueueCol = COLLECTION('rni','bridge_bcre_property_queue')
CreaQueueCol = COLLECTION('rni','reso_crea_property_queue')


REIMPORT_PROP_PRIORITY = 20000
FIVE_MINUTES_MS = 5 * 60 * 1000

yargs = require('yargs')(AVGS)
yargs.option 'days', { alias: 'a', description: 'query days ago', required: false, type: 'number'}

DAYS = yargs.argv.days
dryRun = AVGS.indexOf('dryrun') >= 0

speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

QUEUES_MAP = {
  'TRB': new ResourceDownloadQueue('property', TrbQueueCol),
  'BRE': new ResourceDownloadQueue('property', BreQueueCol),
  'DDF': new ResourceDownloadQueue('property', CreaQueueCol),
}



gNotFound = ''
###
  Description: print not found propId
  Params: propId
  Return: void
###
printNotFound = (propId)->
  # debug.error 'prop has no orig record,propId:',propId
  gNotFound += '.'
  if gNotFound.length > 100
    debug.error 'prop has no orig record:',gNotFound
    gNotFound = ''

main = () ->
  {startTime, endTime} = getCurrentTimeWindow()
  query = {
    status:'A',
    ptype: 'r',
    src:{$ne:'RM'},
    _mt: {
      $gte: startTime,
      $lt: endTime
    }
  }
  startTs = new Date()
  projection = {status:1,lst:1,src:1,sid:1,lp:1,lpr:1,mt:1,ptype2:1,bcf:1,ListingKey:1}
  debug.info "Processing properties with _mt in historical window: #{startTime.toISOString()} to #{endTime.toISOString()}"
  debug.info 'Query:', query
  
  cur = await PropertiesCol.find query, {projection}
  stream = cur.stream()
  
  obj =
    verbose: 1
    high: 10
    stream: stream
    end: (err) ->
      if err
        msg = "reDownloadRni error: #{err}"
        debug.error msg
        sendMailObj.notifyAdmin(msg)
        return EXIT 1, err
      processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
      debug.info "Batch process time #{processTs} mins #{speedMeter.toString()}"
      EXIT 0
    process: (prop, cb) ->
      speedMeter.check {processCount:1}

      try
        result = await reImportHelper.getRniRecord prop
      catch err
        debug.error 'getRniRecord error,propId:', err, prop._id
        speedMeter.check {getRniRecordError:1}
        return cb(err)

      if not result?.rniRecord
        printNotFound(prop._id)
        speedMeter.check {notFound:1}
        return cb()

      if not result?.srcType
        debug.error "prop:#{prop._id} is not found in srcTypeMapping"
        speedMeter.check {notFoundInSrcTypeMapping:1}
        return cb()

      # 重新import
      # addToQueue只处理TRB,BRE,DDF的房源
      if prop.src not in ['TRB','BRE','DDF']
        speedMeter.check {notAddToQueue:1}
        debug.warn "prop:#{prop._id} prop.src is not 'TRB','BRE','DDF', prop.src:#{prop.src}, skip"
        return cb()

      queueId = null
      if prop.src is 'DDF'
        # 历史的ddf房源，没有ListingKey，用_id.slice(3)代替
        queueId = if prop.ListingKey then prop.ListingKey else prop._id.slice(3)
      else
        queueId = prop.sid

      if not queueId
        debug.error "not found queueId, sid:#{prop.sid}, ListingKey:#{prop.ListingKey}"
        speedMeter.check {noQueueId:1}
        return cb()

      speedMeter.check {addedToQueue:1}
      debug.info "prop:#{prop._id} add to queue"
      if not dryRun
        try
          # queueId可能会被转为number,需要改为string
          await QUEUES_MAP[prop.src].addToQueue({_id:queueId.toString()},REIMPORT_PROP_PRIORITY)
        catch e
          debug.error 'addToQueue error:',e
          return cb(e)
      
      return cb()

  helpers.streaming obj

###
  Description: get current 5 minutes time window for historical query
  Return: {startTime, endTime}
###
getCurrentTimeWindow = () ->
  now = new Date()
  # Round down to the nearest 5-minute interval
  minutes = Math.floor(now.getMinutes() / 5) * 5
  currentWindowStart = new Date(now.getFullYear(), now.getMonth(), now.getDate(), now.getHours(), minutes, 0, 0)
  currentWindowEnd = new Date(currentWindowStart.getTime() + FIVE_MINUTES_MS)

  # Calculate how many days ago to query (default to 3 days if not specified)
  daysAgo = DAYS or 3
  daysAgoMs = daysAgo * 24 * 3600 * 1000

  # Query historical data: current window time minus days ago
  startTime = new Date(currentWindowStart.getTime() - daysAgoMs)
  endTime = new Date(startTime.getTime() + FIVE_MINUTES_MS)
  return {startTime, endTime}


main()