###
sh ./start.sh -t batch -n watchTasks -cmd "lib/batchBase.coffee batch/pushNotify/watchTasks.coffee preload-model"


  Description: 任务推送通知批处理器，定时查询并推送待处理任务
  createDate:  2025-06-09
  usage:       sh ./start.sh -t batch -n watchTasks -cmd "lib/batchBase.coffee batch/pushNotify/watchTasks.coffee preload-model"
  Author:      Assistant
  Run duration: 根据任务数量而定
  Run frequency: 持续运行，定时轮询
###

helpersFunction = INCLUDE 'lib.helpers_function'
pushNotifyV2 = INCLUDE 'lib.pushNotifyV2'
speed = INCLUDE 'lib.speed'
pLimit = require 'p-limit'
# NOTE: plimit v4 以上只能import， use 3.1.0
# import pLimit from 'p-limit'
{JT2FT,FT2JT} = INCLUDE 'lib.i18n'

# 模型和集合
TaskModel = MODEL 'Task'
ForumModel = MODEL 'Forum'
PushNotifyModel = MODEL 'PushNotify'
ProcessStatusCol = COLLECTION('vow','processStatus')

# 创建推送通知实例
config = CONFIG(['apn','serverBase','fcmv1_config'])
pushNotifyInstance = pushNotifyV2.createInstance config

# 创建速度统计器
speedMeter = speed.createSpeedMeter {
  intervalTriggerCount: 100,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

debug = DEBUG 3

# 配置参数
BATCH_INTERVAL_MINUTES = 5  # 调度器轮询间隔（分钟）
MAX_PN_PER_TASK = 5000      # 单任务最大用户数
MAX_CONCURRENT_PUSHES = 20  # 最大并发推送数
UPDATE_PROCESS_STATE_INTERNAL = 10 * 60 * 1000  # 10分钟
PROCESS_STATUS_ID = 'watchTasks'
MAX_CONSECUTIVE_ERRORS = 10  # 最大连续错误次数

# 添加退出标志和监控指标
shouldExit = false
lastStatusUpdate = 0
isTaskProcessing = false  # 标记是否有任务正在处理

# 监控指标
monitorMetrics = {
  totalTasksProcessed: 0,
  totalErrors: 0,
  lastTaskTime: null,
  consecutiveErrors: 0
}

{
  processKillSignal,
  updateProcessState,
  RUNNING_NORMAL,
  FINISH_WITHOUT_ERROR
  getProcessHandler,
  HAS_RUNNING_PROCESS
} = require '../../libapp/processHelper'

{
  handleProcessStatus,
  checkRunningProcess,
  handleExit,
} = getProcessHandler {
  ProcessStatusCol,
  id: PROCESS_STATUS_ID,
  filePath: BATCH_FILE_NAME,
  exitFn: EXIT
}

debug.info 'BATCH_FILE_NAME', BATCH_FILE_NAME

gracefulExit = (error) ->
  shouldExit = true
  debug.error 'gracefulExit error', error if error
  debug.info '最终监控报告:', JSON.stringify(monitorMetrics)
  handleExit {error, exitFn: EXIT}

processKillSignal PROCESS_STATUS_ID, gracefulExit

process.on 'uncaughtException', gracefulExit

# 并发限制器
limit = pLimit(MAX_CONCURRENT_PUSHES)

###*
 * 创建 iOS 推送 Promise
 * @param {string} locale - 语言区域标识
 * @param {Object} badgeGroups - badge 分组对象 {badge: [tokens]}
 * @param {Object} notice - 通知对象，包含 id、from、url、title
 * @param {Object} msgMap - 消息映射对象 {locale: message}
 * @returns {Promise} 推送结果 Promise
 * @description 为指定语言区域创建 iOS 推送任务，确保参数正确传递
###
createIOSPushPromise = (locale, badgeGroups, notice, msgMap) ->
  # 立即创建当前迭代的通知对象，避免闭包问题
  currentNotice = Object.assign({}, notice, {msg: msgMap[locale]})
  currentBadgeGroups = badgeGroups
  currentLocale = locale
  
  return limit () ->
    try
      result = await pushNotifyInstance.sendToIOSDevices(currentNotice, currentBadgeGroups, MAX_PN_PER_TASK)
      debug.info "iOS推送完成 (#{currentLocale}) - 成功: #{result.successList?.length}, 失败: #{result.failedList?.length}"
      speedMeter.check {iosPushSuccess: result.successList?.length or 0, iosPushFailed: result.failedList?.length or 0}
      return result
    catch error
      debug.error "iOS推送失败 (#{currentLocale}):", error
      speedMeter.check {iosPushError: 1}
      return {
        errorList: [error.message],
        failedList: [],
        successList: []
      }

###*
 * 创建 Android 推送 Promise  
 * @param {string} locale - 语言区域标识
 * @param {Array} tokens - Android 设备 token 数组
 * @param {Object} notice - 通知对象，包含 id、from、url、title
 * @param {Object} msgMap - 消息映射对象 {locale: message}
 * @returns {Promise} 推送结果 Promise
 * @description 为指定语言区域创建 Android 推送任务，确保参数正确传递
###
createAndroidPushPromise = (locale, tokens, notice, msgMap) ->
  # 立即创建当前迭代的通知对象和token数组，避免闭包问题
  currentNotice = Object.assign({}, notice, {msg: msgMap[locale]})
  currentTokens = tokens.slice()  # 创建数组副本
  currentLocale = locale
  
  return limit () ->
    # 过滤并转换Android设备token，确保不处理null或undefined值
    androidDevices = currentTokens.filter((pn) -> pn and typeof pn is 'string').map (pn) -> pn.substring(12)  # 移除 "android:fcm:" 前缀
    
    try
      result = await pushNotifyInstance.sendToAndroidFcmDevices(currentNotice, androidDevices, MAX_PN_PER_TASK)
      debug.info "Android推送完成 (#{currentLocale}) - 成功: #{result.successList?.length}, 失败: #{result.failedList?.length}"
      speedMeter.check {androidPushSuccess: result.successList?.length or 0, androidPushFailed: result.failedList?.length or 0}
      return result
    catch error
      debug.error "Android推送失败 (#{currentLocale}):", error
      speedMeter.check {androidPushError: 1}
      return {
        errorList: [error.message],
        failedList: [],
        successList: []
      }

###*
 * 处理单个推送任务
 * @param {Object} task - 任务对象，包含_id、targetUser、from、url、title、msg等字段
 * @returns {Object} 推送结果，包含errorList、failedList、successList三个数组
 * @description 根据任务配置查询目标用户，按设备类型和语言分组，并发推送通知
###
processTask = (task) ->
  debug.info '开始处理任务:', task._id, JSON.stringify(task) 
  
  unless task.targetUser
    debug.warn '任务没有目标用户配置:', task._id
    speedMeter.check {noTargetUser: 1}
    return {errorList: [], failedList: [], successList: []}
  
  # 1. 根据targetUser配置查询用户，使用Forum.getTargetUserListPn
  try
    users = await ForumModel.getTargetUserListPn task.targetUser, 'zh'
    debug.info '查询到用户数量:', users?.length
    speedMeter.check {queryUserCount: users?.length or 0}
  catch error
    debug.error '查询用户失败:', error
    speedMeter.check {queryUserFailed: 1}
    # 关键操作失败，抛出错误让上层处理
    throw new Error("查询用户失败: #{error.message}")

  unless users?.length
    debug.warn '未找到有效用户，推送目标为0人'
    speedMeter.check {noValidUsers: 1}
    return {errorList: [], failedList: [], successList: []}
  
  # 2. 用户分组 - 按设备类型和语言区域分组
  iosGroups = {}  # {locale: {pnBadge: []}}
  andGroups = {}  # {locale: []}
  
  for user in users
    # 确保用户对象存在且pn不为空
    continue unless user?.pn and (typeof user.pn is 'string')
    
    # 验证token格式，跳过包含非法字符的token
    if user.pn.includes(';') or user.pn.includes(' ') or user.pn.length < 10
      debug.warn '发现异常token，跳过:', user.pn
      speedMeter.check {invalidToken: 1}
      continue
    
    locale = user.locale or 'zh-cn'
    
    if user.pn.startsWith('ios:')
      iosGroups[locale] ?= {}
      pnBadge = (user.pnBadge || 0) + 1
      iosGroups[locale][pnBadge] ?= []
      iosGroups[locale][pnBadge].push user.pn.substring(4)
    else if user.pn.startsWith('android:fcm:')
      andGroups[locale] ?= []
      andGroups[locale].push user.pn
  
  # 统计设备数量
  iosCount = 0
  androidCount = 0
  
  for locale, badgeGroups of iosGroups
    for badge, tokens of badgeGroups
      iosCount += tokens.length
  
  for locale, tokens of andGroups
    androidCount += tokens.length
  
  debug.info "设备分组完成 - iOS: #{iosCount}, Android: #{androidCount}"
  speedMeter.check {iosDeviceCount: iosCount, androidDeviceCount: androidCount}

  # 检查推送结果是否为0人，发出警告
  totalDeviceCount = iosCount + androidCount
  if totalDeviceCount == 0
    debug.warn "推送目标为0人，任务ID: #{task._id}，请检查目标用户配置或设备token有效性"
    speedMeter.check {zeroPushTarget: 1}
  
  # 3. 构建通知对象
  notice = 
    id: task._id
    from: task.from
    url: task.url
    title: task.title
  
  msgMap = 
    'zh-cn': FT2JT(task.msg)
    'zh': JT2FT(task.msg)
    # NOTE: 避免空内容，用户的local=en, 但是splang=Mandarin
    'en': task.msg

  # 4. 并发推送处理
  allResults = {
    errorList: [],
    failedList: [],
    successList: []
  }
  

  # 处理 iOS 推送
  if iosCount > 0
    iosPromises = []
    for locale, badgeGroups of iosGroups
      # 使用提取的函数创建推送 Promise，确保参数正确传递
      iosPromise = createIOSPushPromise(locale, badgeGroups, notice, msgMap)
      iosPromises.push iosPromise
    
    # 等待所有 iOS 推送完成
    iosResults = await Promise.all(iosPromises)
    for result in iosResults
      allResults.errorList = allResults.errorList.concat(result.errorList or [])
      allResults.failedList = allResults.failedList.concat(result.failedList or [])
      allResults.successList = allResults.successList.concat(result.successList or [])
  
  # 处理 Android 推送
  if androidCount > 0
    androidPromises = []
    for locale, tokens of andGroups
      continue unless tokens.length
      
      # 使用提取的函数创建推送 Promise，确保参数正确传递
      androidPromise = createAndroidPushPromise(locale, tokens, notice, msgMap)
      androidPromises.push androidPromise
    
    # 等待所有 Android 推送完成
    androidResults = await Promise.all(androidPromises)
    for result in androidResults
      allResults.errorList = allResults.errorList.concat(result.errorList or [])
      allResults.failedList = allResults.failedList.concat(result.failedList or [])
      allResults.successList = allResults.successList.concat(result.successList or [])
  
  debug.info "任务推送完成:", task._id
  speedMeter.check { totalPushSuccess: allResults.successList.length, totalPushFailed: allResults.failedList.length}
  
  return allResults

###*
 * 推送工作器 - 处理单个任务的完整推送流程
 * @param {Object} task - 任务对象，包含_id等字段
 * @description 完整的推送流程：执行推送 -> 清理错误记录 -> 处理失败记录 -> 更新badge -> 标记任务完成
 * @throws {Error} 如果标记任务完成失败则抛出错误
###
pushWorker = (task) ->
  debug.info '推送工作器开始处理任务:', task._id
  return unless task
  
  # 标记任务开始处理
  isTaskProcessing = true
  taskStartTime = Date.now()
  
  try
    # 1. 处理任务推送
    pushResult = await processTask(task)
    
    # 2. 后续处理
    # 2.1 处理成功的推送
    if pushResult.successList?.length > 0
      try
        await PushNotifyModel.removeErrorPn pushResult.successList
        debug.info "已清理成功推送的错误记录，数量: #{pushResult.successList.length}"
        speedMeter.check {removedErrorPn: pushResult.successList.length}
      catch error
        debug.error '清理成功推送错误记录失败:', error
        speedMeter.check {removeErrorPnFailed: 1}
    
    # 2.2 处理失败的推送
    if pushResult.failedList?.length > 0
      try
        await PushNotifyModel.removeFailedPn pushResult.failedList
        debug.info "已处理失败推送记录，数量: #{pushResult.failedList.length}"
        speedMeter.check {removedFailedPn: pushResult.failedList.length}
      catch error
        debug.error '处理失败推送记录失败:', error
        speedMeter.check {removeFailedPnFailed: 1}
    
    # 2.3 批量更新 iOS 用户 pnBadge
    iosSuccessTokens = pushResult.successList?.filter (token) -> token.startsWith('ios:')
    if iosSuccessTokens?.length > 0
      try
        await PushNotifyModel.updateBadgeCount iosSuccessTokens
        debug.info "已更新iOS用户badge，数量: #{iosSuccessTokens.length}"
        speedMeter.check {updatedBadgeCount: iosSuccessTokens.length}
      catch error
        debug.error '更新iOS用户badge失败:', error
        speedMeter.check {updateBadgeFailed: 1}
    
    # 3. 标记任务完成，传递成功推送的用户数量
    try
      totalSuccessCount = pushResult.successList?.length || 0
      await TaskModel.markTaskAsDone task._id, speedMeter.values

      debug.info '任务已标记为完成:', task._id, "成功推送用户数: #{totalSuccessCount}"
      speedMeter.check {taskCompleted: 1}

      # 如果推送成功用户数为0，发出警告
      if totalSuccessCount == 0
        debug.warn "任务 #{task._id} 推送成功用户数为0，请检查目标用户或推送配置"
        speedMeter.check {zeroSuccessPush: 1}

    catch error
      debug.error '标记任务完成失败:', error, task._id
      speedMeter.check {markTaskDoneFailed: 1}
      # 关键操作失败，必须抛出错误
      throw new Error("标记任务完成失败: #{error.message}")
    
    # 4. 更新监控指标并打印任务统计信息
    taskProcessTime = (Date.now() - taskStartTime) / 1000
    monitorMetrics.totalTasksProcessed++
    monitorMetrics.lastTaskTime = new Date()
    monitorMetrics.consecutiveErrors = 0  # 重置连续错误计数
    
    debug.info "任务 #{task._id} 处理完成，耗时: #{taskProcessTime}s, 统计信息: #{speedMeter.toString()}"
    
    # 定期输出监控报告
    if monitorMetrics.totalTasksProcessed % 10 == 0
      debug.info "监控报告:", JSON.stringify(monitorMetrics)
    
    # 清除统计信息，准备下一次任务
    speedMeter.reset()
    
  catch error
    debug.error '推送工作器处理失败:', error
    speedMeter.check {workerFailed: 1}
    
    # 更新错误监控指标
    monitorMetrics.totalErrors++
    monitorMetrics.consecutiveErrors++
    
    # 打印错误统计信息并清除统计
    taskProcessTime = (Date.now() - taskStartTime) / 1000
    debug.error "任务 #{task._id} 处理失败，耗时: #{taskProcessTime}s, 统计信息: #{speedMeter.toString()}"
    debug.error "错误监控指标:", JSON.stringify(monitorMetrics)
    speedMeter.reset()
    throw error
  finally
    # 标记任务处理完成
    isTaskProcessing = false

###*
 * 任务调度器 - 定时查询并处理待处理任务
 * @description 持续运行的调度器，定期查询最高优先级任务并处理
 * @features 进程状态更新、连续错误监控、并发控制、优雅退出
###
taskScheduler = ->
  debug.info '任务调度器开始运行'
  
  while not shouldExit
    try
      # 1. 定期更新进程状态 (每10分钟)
      now = Date.now()
      if now - lastStatusUpdate > UPDATE_PROCESS_STATE_INTERNAL
        await updateProcessStatusForTasks(new Date())
        lastStatusUpdate = now
        debug.info '已更新进程状态'
      
      # 2. 检查连续错误次数
      if monitorMetrics.consecutiveErrors >= MAX_CONSECUTIVE_ERRORS
        debug.error "连续错误次数过多: #{monitorMetrics.consecutiveErrors}, 退出程序"
        break
      
      # 3. 检查是否有任务正在处理
      if isTaskProcessing
        debug.debug '有任务正在处理中，跳过查找新任务'
      else
        # 查询并更新优先级最高的任务状态
        try
          task = await TaskModel.findOneAndUpdateHighestPriority()
        catch error
          debug.error '查询任务失败:', error
          # 关键操作失败，抛出错误让外层处理
          throw new Error("查询任务失败: #{error.message}")

        if task
          debug.info '找到待处理任务:', task._id, task.priority

          # 4. 执行推送工作器
          try
            await pushWorker(task)
          catch error
            debug.error '推送工作器执行失败:', error
            # 推送工作器失败，抛出错误让外层处理
            throw new Error("推送工作器执行失败: #{error.message}")

        else
          debug.debug '没有待处理任务'
      
      # 5. 等待下一次轮询
      await helpersFunction.sleep(BATCH_INTERVAL_MINUTES * 60 * 1000)
      
    catch error
      monitorMetrics.totalErrors++
      monitorMetrics.consecutiveErrors++
      debug.error "任务调度器错误 (连续第#{monitorMetrics.consecutiveErrors}次):", error
      
      # 指数退避，最大60秒
      retryDelay = Math.min(10000 * monitorMetrics.consecutiveErrors, 60000)
      debug.info "将在 #{retryDelay}ms 后重试"
      await helpersFunction.sleep(retryDelay)
      continue
  
  debug.info '任务调度器退出'

###*
 * 更新进程状态
###
updateProcessStatusForTasks = (startTs) ->
  debug.info '更新进程状态'
  try
    await handleProcessStatus
      startTs: startTs
      status: RUNNING_NORMAL
      nextTs: Date.now() + UPDATE_PROCESS_STATE_INTERNAL
  catch error
    debug.error 'updateProcessStatusForTasks', error
    # 关键操作失败，抛出错误
    throw new Error("更新进程状态失败: #{error.message}")

###*
 * 主函数
###
main = ->
  try
    # 检查运行进程
    try
      hasRunningProcess = await checkRunningProcess()
    catch error
      debug.error '检查运行进程失败:', error
      return gracefulExit new Error("检查运行进程失败: #{error.message}")

    if hasRunningProcess
      return gracefulExit HAS_RUNNING_PROCESS

    startTs = new Date()
    try
      await updateProcessStatusForTasks(startTs)
    catch error
      debug.error '更新进程状态失败:', error
      return gracefulExit new Error("更新进程状态失败: #{error.message}")

    debug.info '任务监控批处理器启动成功'
    debug.info "配置参数 - 轮询间隔: #{BATCH_INTERVAL_MINUTES}分钟, 最大并发: #{MAX_CONCURRENT_PUSHES}, 单任务最大用户: #{MAX_PN_PER_TASK}"

    # 启动任务调度器
    try
      await taskScheduler()
    catch error
      debug.error '任务调度器执行失败:', error
      return gracefulExit new Error("任务调度器执行失败: #{error.message}")

  catch error
    debug.error '主函数执行失败:', error
    return gracefulExit error

# 启动主函数
main()