gConfig = CONFIG(['serverBase','share','fileBase'])
debug = DEBUG()
libUser = INCLUDE 'libapp.user'
helpers = INCLUDE 'lib.helpers'
{filterSpecialCase,isSubstring,filterJSONSpecial<PERSON>har} = INCLUDE 'lib.helpers_string'
{getProvAbbrName} = INCLUDE 'lib.cityHelper'
getPropDetails = DEF 'getPropDetails'
{getPropDetailUrl,getPropAlternateUrls,getDispVar,blurProp} = INCLUDE 'libapp.propWeb'
{getContactRealtor} = INCLUDE 'libapp.contactRealtor'
{
  genListingPicReplaceUrls,
  computedOnDate,
  getBasePropDetailOpt,
  computedOffDate,
  getPropId,
  isRMListing,
  isPassed,
  isCondo,
  setupDisclaimer
} = INCLUDE 'libapp.properties'
{getOauthUrls,setLastPageSession} = INCLUDE 'libapp.oauth'
{ sprintf }  = INCLUDE 'lib.sprintf'
{ CONTACT_REALTOR_VALUES,listingPicUrlReplace } = INCLUDE 'libapp.common'

UserModel = MODEL 'User'
ProjectModel = MODEL 'Project'
PropertiesModel = MODEL 'Properties'
webErrPage = DEF 'webErrPage'
getDotHtml = DEF 'getDotHtml'
getTopUid = DEF 'getTopUid'
getTopUpUsers = DEF 'getTopUpUsers'
Condo = MODEL 'Condo'
SysDataModel = MODEL 'SysData'
GroupModel = MODEL 'Group'
InRealNoteModel = MODEL 'InRealNoteInst'

rateReducer = INCLUDE 'lib.rateReducer'
# rateLimiter = INCLUDE 'lib.rateLimiter'
rateLimiter = INCLUDE 'lib.rateLimiterV2'
getConfig = DEF 'getConfig'
keyWords = getConfig('blockUAFields') or 'SM-G900P'
keyWords = keyWords.split(' ')
rateReducer = rateReducer({reduceBy:'UA',keyWords})
SysDataModel.getBadIpAccessRecord (err,ret)->
  if ret?
    rateReducer.initBlackListIPs(ret)
    rateLimiter.initBlackListIPs(ret)

NO_IMAGE = '/img/no-image.png'
CITY_PROV_RE = /^[a-z\-]+\-[a-z]{2}$/
PROP_ID_RE = /[A-Z\-(\d)+]/
SEO_TITLE = '%s | %s %s | Realmaster.com'
SEO_DESC = '%s %s %s, %s %s %s %s, %s: %s. %s'
SEO_KEYWORDS = '%s, %s %s, %s %s, %s %s, %s search homes, APP, MLS, CREA, TREB'
School = MODEL 'School'
popularCities = DEF 'popularCitiesWithOwnControlData'

getSaletp = (saletp) ->
  #TODO: missing saletp in data structure
  return null unless saletp
  return saletp if 'string' is typeof saletp
  #saletp is array
  if helpers.isArray saletp
    if saletp.length is 1
      return saletp[0]
    else
      return [saletp[0],saletp[1]]
  else
    #handle other datatype
    return saletp.toString()

getPropMapLink = (prop,isCip) ->
  return '' unless prop.addr
  if (not prop.addr) and prop.lat and prop.lng
    addr = prop.lat + ',' + prop.lng
  else
    {city_en,city,prov_en,prov,cnty_en,cnty} = prop
    addr = "#{(city_en or city)},#{(prov_en or prov)}"
    cntyEn = cnty_en or cnty or ''
    if /^CA$/i.test(cntyEn)
      addr += ' Canada'
    else
      addr += ' ' + cntyEn
    if prop.addr
      addr = prop.addr + "," + addr
    else
      addr = addr + ', ' + prop.zip
  mapUrl = if isCip then 'https://www.bing.com/maps?q=' else 'https://maps.google.com/?q='
  mapUrl + encodeURIComponent(addr)

getMetaData = (prop,lang,opt)->
  prov = prop.prov
  city = prop.city
  addr = "#{(prop.addr or '')} #{prop.unt or ''}"
  # zip = prop.zip
  ptype2 = if prop.ptype2 then prop.ptype2[0] else prop.pstyl
  unless ptype2
    ptype2 = opt.l10n 'Homes'

  if lang is 'en'
    bdrmsUnt = 'Bd'
    bthrmsUnt = 'Ba'
  else
    bdrmsUnt = opt.l10n('b','prop')
    bthrmsUnt = opt.l10n('w','prop')
  bthrms = if prop.bthrms then "#{prop.bthrms}#{bthrmsUnt}" else ''
  bdrms = if prop.bdrms then "#{prop.bdrms}#{bdrmsUnt}" else ''
  canadaCity = "#{opt.l10n 'Canada'}#{city}"
  if prop.status_en is 'U'
    priceType = saleType = opt.l10n('Sold Price','prop') #成交价
  else
    saleType = if getSaletp(prop.saletp_en) is 'Sale' then opt.l10n('For Sale') else opt.l10n('For Rent')
    priceType = opt.l10n('Asking Price','prop') #房价
  title = filterJSONSpecialChar(sprintf opt.l10n(SEO_TITLE,'propdetail'),addr,ptype2,saleType)
  m = if lang is 'en' then (prop.m or prop.m_zh) else (prop.m_zh or prop.m)
  description = filterJSONSpecialChar(sprintf opt.l10n(SEO_DESC,'propdetail')\
    ,addr,city,prov,bdrms,bthrms,ptype2,saleType,priceType,prop.priceValStrRed,m)
  keywords = filterJSONSpecialChar(sprintf opt.l10n(SEO_KEYWORDS,'propdetail'),
    addr,city,ptype2,city,saleType,city,priceType,city)
  geoRegion = "CA-#{prop.p_ab}"
  geoPlaceName = "#{addr} #{city}"
  geoPosition = "#{prop.lat},#{prop.lng}"
  return {title,description,keywords,geoRegion,geoPlaceName,geoPosition}

###
return prop sid or id with give idSection
ph3401-bay-street-corridor-C3378778 -> C3378778
RM1-26715 -> RM1-26715
###
getPropIdFromPart = (idSection) ->
  idArray = idSection.split('-')
  id = idArray[idArray.length-1]
  id = idArray.slice(idArray.length-2).join('-') if idSection.indexOf('RM') isnt -1
  id

# TODO: add docs
getRealtor = ({req, prop,user}, cb) ->
  locale = req.locale()
  if (prop.marketRmProp or (not isRMListing(prop)))
    if prop.marketRmProp
      page = 'trustedAssignment'
    else
      page = 'mls'
    try
      ret = await getContactRealtor {
        MSG_STRINGS,
        gConfig,
        UserModel,
        ProjectModel,
        PropertiesModel,
        getDotHtml,
        locale,user,page,
        l10n:req.l10n,
        src:'web',
        popularCities,
        prop,SysDataModel}
    catch err
      return cb err
    return cb null,ret
  else
    uid = prop.uid
    opt = {}

  uids = if uid then [uid] else []
  getTopUpUsers uids,opt,(err,users)->
    debug.error err if err
    if users?.length > 0
      retu = users[0]
      retu.cert = req.isAllowed 'rcmdRealtor',retu
      retu.fnm = libUser.fullNameOrNickname req.locale(),retu
      retu.cpny = libUser.userCpny(req.locale(),retu)
      retu.eml = if Array.isArray(retu.eml) then retu.eml[0] else retu.eml
      retu.isVip = req.isAllowed 'vipPlus', retu
      return cb err, retu
    q = { city: prop.city_en, role: 'realtor' }
    if prop.bndcity?.nm
      q.city = prop.bndcity.nm
    if prop.bndSubCity?.nm
      q.subCity = prop.bndSubCity.nm
    UserModel.getTopAgents req, q, (err, tlist) ->
      if tlist?.length > 3
        tlist = tlist.splice(0, 3)
      cb err, tlist

getPropParentUrl = ({prop,urlAndQuery})->
  queryStringObj = {}
  # parse string qusery into object
  if queryString = urlAndQuery[1]?.split('&')
    for query in queryString
      [field,value] = query.split('=')
      queryStringObj[field] = value
  if redirect = queryStringObj.d
    # check if it is from list page
    return redirect
  else
    # if it is not open from list page, for example, edm, autocomplete
    saletpFeaturesMapping = {
      sale:'sale',
      lease:'rent'
    }
    stp = prop.stp_en?.toLowerCase() or 'sale'
    "/for-#{saletpFeaturesMapping[stp]}/#{prop.city_en||prop.city}/view=map.saletp=#{stp}.ptype=#{prop.ptype_en}.prov=#{prop.p_ab}"

#old url structure /for-sale/toronto-多伦多/1234-toronto-str/pty-(sid|rm1-id)
# GET ':feature/:cityProv/:addr/:id', (req, resp, next)->
GET /(for-sale|for-rent|sold){1}\/[a-zA-Z\-\s\'\.]+(\-.*)?\/[0-9a-zA-Z\-\'\.]+\/([^\#]+)?(RM1\-)?(A-Z)?[0-9]+/, (req, resp, next)->
  renderPropDetail req, resp, next

# /north-vancouver-bc/110-e-17th-st/616-central-lonsdale-BRER2792903

#url sample: /toronto-on/1234-toronto-st/unt-community-(sid|rm1-id)
# GET ':cityProv\/:addr\/:id', (req, resp, next)->
GET /[a-z\-]+\-[a-z]{2}\/[0-9a-zA-Z\-\'\.]+\/([^\#]+)?(RM1\-)?(A-Z)?[0-9]+(\?d\=.+)?/, (req, resp, next)->
  renderPropDetail req, resp, next

alertHandler = ({data,msg})->
  debug.error msg,data
  SysDataModel.logBadAccess data

getFullAccess = ({prop,req,opt,user})->
  return true if user
  return true unless prop.login
  return true if req.param('fub_src') is 'fub'
  return true if opt.isWeChat
  return true if isSearchEngine(req)
  return false

getPropDetailWebOpt = ({req,user,id})->
  opt = getBasePropDetailOpt req,user
  opt.id = id
  # if /^RM/.test id
  #   opt.id = id
  #   delete opt._id
  opt.isWebDetail = true
  opt.isWeChat = req.isWeChat()
  opt.isAllowed = req.isAllowed
  opt.transDDF = req.getConfig('transDDF')
  return opt


buildPropExtraFields = ({prop,propURL,req})->
  prop.webUrl = propURL
  prop.p_ab ?= getProvAbbrName(prop.prov_en)
  prop.addr = filterSpecialCase(prop.addr)
  prop.picUrls = listingPicUrlReplace(prop)
  prop.jsonldImg = prop.picUrls?[0] or "#{req.getProtocol()}://#{req.host}/#{NO_IMAGE}"
  prop.mapLink = getPropMapLink prop,req.isChinaIP()
  if prop.ohz?.length
    ohz = []
    for oh in prop.ohz
      ohz.push(oh) unless isPassed(oh.t)
    ohz = ohz.sort (a,b) ->
      if a.f < b.f
        return -1
      if a.f > b.f
        return 1
      return 0
    prop.ohz = ohz
  prop.onDate = prop.onD
  prop.offDate = prop.offD
  prop.propId = getPropId prop
  if isSearchEngine(req)
    prop.rltr = req.l10n('RealMaster') unless prop.rltr
    delete prop.sp

isSearchEngine = (req)->
  return helpers.isSearchEngine(req)

getParsedPropDetailUrl = (req)->
  currentUrl = req.url
  {gAuthUrl,fAuthUrl} = getOauthUrls(req,currentUrl)
  urlAndQuery = currentUrl.split('?')
  urlPart = urlAndQuery[0].replace(/^\/|\/$/g, '').split('/')
  queryString = urlAndQuery[1]
  idSection = urlPart.pop()
  addr = urlPart.pop()
  cityProv = urlPart.pop()
  id = getPropIdFromPart idSection
  #handle if url(old) has feature
  feature = urlPart.pop()
  return {gAuthUrl,fAuthUrl,urlAndQuery,idSection,addr,cityProv,id,feature,queryString}


renderPropDetail = (req,resp,next)->
  setLastPageSession req,''
  ip = req.remoteIP()
  if rateReducer.isBadIp(ip)
    rateLimiter.addBlackListIP(ip)
    return webErrPage req, resp, 'no id', 'NA'
  if rateReducer.isBadAccess(req)
    # ret.items = rateReducer.reduceResults(ret.items)
    rateReducer.logBadRequest(req,alertHandler)
  {gAuthUrl,fAuthUrl,urlAndQuery,idSection,addr,cityProv,id,feature,queryString} = getParsedPropDetailUrl(req)
  if feature in ['for-sale','for-rent','sold']
    shouldRedirect = true
  else
    return next() if ((not (CITY_PROV_RE.test cityProv)) or (not idSection) or (not (PROP_ID_RE.test id)))
  return webErrPage req, resp, 'no id', 'NA' unless id
  # lang = req.locale()
  UserModel.auth {req,resp},(user)->
    devType = req.getDevType()
    if user
      try
        showedTermWeb = await UserModel.findShowedTerm user._id,{devType}
      catch err
        debug.error "uid:#{user._id},devType:#{devType},err:#{err}"
      user.showedTermWeb = showedTermWeb
    opt = getPropDetailWebOpt {req,user,id}
    projection = PropertiesModel.genExcludes opt
    opt.isSearchEngine = true if isSearchEngine(req)
    try
      opt.isRealGroup = await GroupModel.isInGroup {uid:user?._id, groupName:':inReal'}
    catch err
      debug.error 'GroupModel.isInGroup',err,' uid:',user?._id
    opt.isLarge = true
    try
      result = await PropertiesModel.getTranslatedPropDetailByID {
        id:opt.id,
        fromStr:devType,
        trans:req.locale(), #req.param local or lang?
        disKeyOnly:true,
        additionalConfig:opt,
        projection,
        user
      }
    catch err
      debug.error err
      return webErrPage req, resp, req.l10n(MSG_STRINGS.DB_ERROR), 'NA'
    if not result.detail
      return webErrPage req, resp, req.l10n(MSG_STRINGS.NOT_FOUND), 'NA'
    prop = result.detail
    pageParams = {
      rmapp:1,
      page:'propDetail',
      author:req.l10n('Realmaster Technology Inc.'),
      swiper:true,
      jquery: 1
    }
    if result.detail?.needLogin
      ctx = {
        prop,
        fullAccess:false
        dispVar:getDispVar(req),
        isCip:opt.isChinaIP,
        showedTermWeb:user?.showedTermWeb,
        parentListUrl:getPropParentUrl {prop,urlAndQuery},
        webRmInfoDisp:req.getConfig('webRmInfoDisp'),
        locale:opt.locale,
      }
      ctx.gAuthUrl = gAuthUrl if gAuthUrl
      ctx.fAuthUrl = fAuthUrl if fAuthUrl
      ctx.jsonDispVar = helpers.encodeObj2JsonString(ctx.dispVar)
      pageParams.alternateUrls = getPropAlternateUrls prop,opt.locale, {langPrefix:req.setting?.langPrefix,protocol:opt.protocol,host:opt.host}
      pageParams.description = req.l10n('Login To View More')
      pageParams.canonicalUrl = req.getCanonicalUrl(propUrlWithoutDomain)
      pageParams.prop = prop
      pageParams.title = sprintf opt.l10n(SEO_TITLE,'propdetail'),req.l10n('Login To View More'),req.l10n('Homes'),req.l10n('Login To View More')
      return resp.ckup 'prop-detail',ctx,'layout',pageParams
      # return resp.redirect '/www/login'
      # errorNeedLogin = 'According to Real Estate Board rules, you have to login to see this listing!'
      # return webErrPage req, resp, req.l10n(errorNeedLogin), 'origin', true
    setupDisclaimer prop,result.dis # 设置prop.disclaimer
    # BUG: no prov if user not loggedin
    propUrlWithoutDomain = getPropDetailUrl(prop,opt.locale)
    propURL = req.fullUrl propUrlWithoutDomain
    # TODO: use addr-id or addr/cmty-id?
    curURL = encodeURI(req.fullUrl "/#{cityProv}/#{addr}/#{idSection}")
    # curURLPathname = encodeURI("/#{cityProv}/#{addr}/#{idSection}")
    loginUrl = encodeURI(req.fullUrl "/www/login?d=#{propURL}&loginCurPage=loginToProp")
    if opt.isSearchEngine # and isSubstring(propUrlWithoutDomain,curURLPathname)
      propURL = "#{propURL}#{if queryString then '?'+queryString else ''}"
    else if shouldRedirect or (curURL isnt propURL)
      return resp.redirect "#{propURL}#{if queryString then '?'+queryString else ''}"
    
    buildPropExtraFields {prop,propURL,req}

    fullAccess = getFullAccess {prop,req,opt,user}
    
    if isRMListing(prop)
      # genListingPicReplaceUrls(prop\
      #   ,req.isChinaIP(),{},gConfig.fileBase?.imgServerDlAddr)
      prop.picUrls = prop.pic?.l
      isMlsProp = false
    else
      isMlsProp = true
      if req.isAllowed('noteAdmin')
        try
          {hasSelfM,totalCount} = await InRealNoteModel.getNotesCount(prop.uaddr, user?._id)
        catch err
          debug.error err
        
    {
      title,
      description,
      keywords,
      geoRegion,
      geoPlaceName,
      geoPosition
    } = getMetaData prop,opt.locale,req
    if prop.lstStr or prop.saleTpTag
      prop.statusStr = req.l10n(prop.lstStr or prop.saleTpTag)
    # NOTE: DDF房源没有lst与MlsStatus字段
    else if prop.status is 'A'
      prop.statusStr = req.l10n('Current Listing')
    else if prop.status is 'U'
      prop.statusStr = req.l10n('Historical Listing')
    prop.title = title

    ctx = {
      prop,
      fullAccess,
      dispVar:getDispVar(req),
      isCip:opt.isChinaIP,
      showedTermWeb:user?.showedTermWeb,
      isDDFProp:prop.isDDFProp,
      user,
      # formPage:req.param('formPage'),
      parentListUrl:getPropParentUrl {prop,urlAndQuery},
      webRmInfoDisp:req.getConfig('webRmInfoDisp'),
      locale:opt.locale,
      isCondo:isCondo prop
      hasMfee:prop.mfee?
      loginUrl
    }
    ctx.jsonDispVar = helpers.encodeObj2JsonString(ctx.dispVar)
    ctx.gAuthUrl = gAuthUrl if gAuthUrl
    ctx.fAuthUrl = fAuthUrl if fAuthUrl
    ctx.hasSelfM = hasSelfM if hasSelfM?
    ctx.totalCount = totalCount if totalCount?
    ctx.isMlsProp = isMlsProp
    if (ctx.isCondo or ctx.hasMfee) and prop.addr
      cmty = prop.cmty
      if cmty
        cmty = encodeURIComponent cmty
      else
        cmty = 'NAN'
      #TODO: what if addr has specail character, addr may has - also
      addr = encodeURIComponent prop.addr
      listUrl = "#{prop.city_en}-#{prop.p_ab}/#{cmty}/#{addr}"
      prop.saleUrl = "/#{ctx.locale}/for-sale/"+listUrl
      prop.leaseUrl ="/#{ctx.locale}/for-rent/"+listUrl
    
    if req.setting.hideCmty
      prop.hideCmty = true
    if req.setting.hideAmenity
      prop.hideAmenity = true

    pageParams.alternateUrls = getPropAlternateUrls prop,opt.locale, {langPrefix:req.setting?.langPrefix,protocol:opt.protocol,host:opt.host}
    pageParams.canonicalUrl = req.getCanonicalUrl(propUrlWithoutDomain)
    pageParams.noSEOIndex = prop.noSEOIndex
    pageParams.prop = prop
    pageParams.title = title
    pageParams.description = description
    pageParams.keywords = keywords
    pageParams.geoRegion = geoRegion
    pageParams.geoPosition = geoPosition
    pageParams.geoPlaceName = geoPlaceName
    getRealtor {req, prop,user}, (err, ret) ->
      if err
        debug.error err if not (/login/ig.test err.toString())
      ctx.topU = ret
      # NOTE:web端在没有勾选contact city时不展示报名表或经济信息
      if ret?.action?.UIType
        if CONTACT_REALTOR_VALUES.SHOW_NOTHING not in ret.action.UIType
          ctx.contactRealtorData = ret or {}
      # search schools if prop is not commercial
      propHasSchoolsOrLoc = (prop.schs and prop.bnds) or prop.lat or prop.loc
      try
        buildingDetail = await getBuildingDetail {prop,ctx}
      catch err
        debug.error "propId:#{prop._id},error:#{err}"
      if buildingDetail
        prop.buildingDetail = buildingDetail
      debug.debug 'ctx.buildingDetail',prop.buildingDetail
      if (prop.ptype_en?.toLowerCase() isnt 'commercial') and propHasSchoolsOrLoc
        loc = [prop.lat,prop.lng] or [prop.loc.coordinates[1],prop.loc.coordinates[0]]
        if prop.schs and prop.bnds # pass loc if dist needed.
          d = {schs:prop.schs, bnds:prop.bnds,loc}
        else
          d = {mode:'bnd', loc}
        d.locale = req.locale()
        try
          results = await School.getHomeSchools d
        catch err
          debug.error err
        if results
          ctx.schs = results
        return resp.ckup 'prop-detail',ctx,'layout', pageParams
      else
        resp.ckup 'prop-detail',ctx,'layout', pageParams

getBuildingDetail = ({prop,ctx})->
  if not (ctx.isCondo or ctx.hasMfee)
    return
  await Condo.getCondoInfoToDisplay prop.uaddr

VIEW 'prop-detail', ->
  blur = if @prop.needLogin then 'blur' else ''
  div class: 'backdrop'
  div id:'terms-modal', class:'frame',->
    div class:'iframe-wrapper',->
      iframe src:'/terms', frameborder:'0'
    div class: 'btn-wrapper',->
      button -> text _('I Agree')
  div id: 'prop-detail-page', ->
    div class: 'detail-header container ' + blur, ->
      div class: 'col-sm-12 crumbs', ->
        ul ->
          li ->
            span -> text @prop.prov
            span class: 'fa fa-angle-right'
          li ->
            a href: @parentListUrl, -> text @prop.city
            span class: 'fa fa-angle-right'
          li ->
            h1 class: 'breadcrumb-title', -> text @prop.addr
    detailContainerClass = 'detail-container container ' + blur
    if @fullAccess and @prop.pho and @prop.picUrls?.length
      text ckup 'propPhotos', {photos: @prop.picUrls, fullAccess: @fullAccess}
      text ckup 'photoGallery', {photos: @prop.picUrls}
      detailContainerClass += ' has-photo'
    div class: detailContainerClass, ->
      div class: 'row', ->
        div class: 'col-sm-7 col-md-8', ->
          text ckup 'propHeading', {prop:@prop,fullAccess:@fullAccess,isLoggedIn:@dispVar?.isLoggedIn}
          if not @dispVar?.isLoggedIn
            section class: 'detail-section box-shadow border-radius-all', ->
              div class: 'row', ->
                h4 class: 'col-xs-9 detail-section-title', style:'margin:0', -> _('Log in to view more information')
                a class: 'col-xs-3 goLogin', href: "#{@loginUrl}", -> _('Go To Login')
          if @fullAccess is true
            if @showTranslateNotice
              text ckup 'properties/translateNotice',{originCopyLink:@originCopyLink}
            text ckup 'propSummary', {prop: @prop,isLoggedIn:@dispVar?.isLoggedIn}
            text ckup 'propRemarks', {prop: @prop}
            if @prop.ohz and @prop.ohz.length
              text ckup 'propOpenHouse', {prop: @prop}
            text ckup 'propLocation', {prop: @prop,isCip:@isCip}
            if @prop.rms
              text ckup 'propRooms', {rms: @prop.rms}
            if @schs and Object.keys(@schs).length > 0
              text ckup 'schools', {schs: @schs, prop: @prop}
            if @isCondo or @hasMfee
              text ckup 'properties/condoBuildingCard', {prop: @prop}
        if @contactRealtorData
          div class: 'col-sm-5 col-md-4', ->
            text ckup 'requestForm', {fullAccess:@fullAccess,prop: @prop,contactRealtorData:@contactRealtorData,webRmInfoDisp:@webRmInfoDisp,user:@user or {}}
        if @dispVar?.isNoteAdmin and @isMlsProp
          div class: 'notesBtnPot', ->
            button class: 'notesBtn', ->
              span class: 'fa fa-rmmemo noteIcon'
              span style: 'font-size:12px', ->
                text "#{@hasSelfM}/#{@totalCount}"
    if @fullAccess is false
      div id: 'prop-detail-login-buttons',->
        div id: 'prop-detail-login', class: 'prop-button red',style:"#{if @prop.needLogin then 'display: none;' else ''}", -> text _('Login To View More')
        div class: 'button-login-thirdParty', ->
          if (not @isCip) and @gAuthUrl
            a href: @gAuthUrl, class: 'button-google box-shadow',->
              img src: '/img/user/google-logo.png', alt: 'Google Sign in'
              span class: 'button-google-text', -> text _('Sign in with Google')
          if (not @isCip) and @fAuthUrl
            a href: @fAuthUrl, class: 'button-facebook box-shadow', ->
              i class: 'fa fa-facebook-official'
              span class: 'button-facebook-text', -> text _('Sign in with Facebook')
    text ckup 'components/loginModal', {dispVar:@dispVar,\
      jsonDispVar:@jsonDispVar,gAuthUrl:@gAuthUrl,fAuthUrl:@fAuthUrl}
  # react root
  text ckup 'components/favoriteModal', {}
  js '/web/packs/bootstrap.min.js'
  # js '/js/Chart-2.5.0.min.js'
  coffeejs {vars:{
    # formPage:@formPage
    dispVar:@dispVar
    fullAccess:@fullAccess,
    showedTermWeb:@showedTermWeb,
    isDDFProp: @isDDFProp
    agentEml:(if @topU then @topU.eml else ''),
    agentUid:(if @topU then @topU._id else ''),
    isWeChat:@req.isWeChat(),
    _id:@prop._id,
    loc:((if @prop.loc then @prop.loc.coordinates) or [@prop.lng,@prop.lat]),
    addr: @prop.addr,
    city:@prop.city_en,
    prov:@prop.prov_en,
    contactRealtorPage: if @prop.src is 'RM' then '' else 'mls'
    lp: @prop.lp,
    lpr: @prop.lpr,
    saletp: @prop.saletp_en,
    ptype2: @prop.ptype2_en,
    marketRmProp: @prop.marketRmProp
    needLogin: @prop.needLogin,
    uaddr: @prop.uaddr,
    isBuilding: @prop.isBuilding
    }},->
      null
  js '/js/web/propDetail.min.js'

VIEW 'imgPlaceholder', ->
  div class: 'swiper-slide nophoto-placeholder',->
    img class: 'detail-photo', src: '/img/no-photo.png'

VIEW 'propPhotos', ->
  containerClass = if @fullAccess then 'swiper-container' else 'swiper-container blur'
  div class: containerClass, id: 'detail-photos', ->
    swiperWrapperClass = 'swiper-wrapper'
    swiperWrapperClass += ' center' if @photos.length < 3
    div class: swiperWrapperClass, ->
      # if @photos.length == 1
      #   text ckup 'imgPlaceholder', {}
      for s,i in @photos
        div class: 'swiper-slide', ->
          img class: 'detail-photo', dataIndex: i, src: s
      # if @photos.length < 3
      #   text ckup 'imgPlaceholder', {}
    div class: 'swiper-button-next'
    div class: 'swiper-button-prev'
  js '/js/propPhotos.min.js'

VIEW 'photoGallery', ->
  div class: 'swiper-container', id: 'photos-gallery', ->
    div class: 'fa fa-times', id: 'photos-gallery-close'
    div class: 'swiper-wrapper', ->
      for p in @photos
        div class: 'swiper-slide', ->
          img class: 'gallery-photo', src: p.replace(/(\/(414|768|828)\.)/,'/')
    div class: 'swiper-button-next', id: 'photo-gallery-next'
    div class: 'swiper-button-prev', id: 'photo-gallery-prev'
  text """
  <script>
    if (window.$) {
      $(document).ready(function() {
        $('#photos-gallery-close').on('click', function() {
          $('#photos-gallery').hide();
        });
        $(document).keyup(function(e) {
          if (e.key === "Escape") {
            $('#photos-gallery').hide();
          }
        });
      });
    }
  </script>
  """
  # coffeejs {}, ->
  #   if window.$
  #     $(document).ready ->
  #       $('#photos-gallery-close').on 'click', ->
  #         $('#photos-gallery').hide()
  #       $(document).keyup (e) ->
  #         if e.key is "Escape"
  #           $('#photos-gallery').hide()
  #   return

VIEW 'propHeading', ->
  blur = if @fullAccess then '' else 'blur'
  div class: 'detail-prop border-radius-all box-shadow row ' + blur, ->
    div class: 'col-xs-8 col-sm-9', ->
      div class: 'listing-prop-detail', ->
        h3 class: 'listing-prop-price',style:'display:flex;align-items:center;line-height: normal;', ->
          propPrice ="#{@prop.priceValStrRed or @prop.askingPriceStr or ''}"
          if @prop.showSoldRedTag
            span class: 'soldRedTag', -> _('Sold')
          span class: 'detail-price', -> propPrice
          # fav modal toggler
          span id: 'fav-button',style:'line-height: 22px;', \
          class: "#{if @prop.fav then 'fa fa-heart heart-icons'\
          else 'fa fa-heart-o heart-icons'}", \
          role:'button', 'data-property-id':"#{@prop._id}",\
          if @prop.favGrp?.length > 0 then 'data-favorite-group':"#{@prop.favGrp}"
        if @prop.askingPriceStr
          h6 class: 'detail-lp', -> text "#{@prop.askingPriceStr} #{@prop.askingPriceStrDesc}"
        if @prop.addr
          span class: 'listing-prop-address', -> text "#{@prop.origUnt or @prop.unt or ''} #{@prop.addr}"
        addr2 = []
        if @prop.city
          addr2.push @prop.city
        if @prop.prov
          addr2.push @prop.prov
        if @prop.zip
          addr2.push @prop.zip
        if addr2.length > 0
          p class: 'listing-prop-address', -> text addr2.join ', '
        p class: 'listing-prop-size', ->
          span -> text "#{@prop.saleTpTag or ''}"
          if @prop.lstStr and (@prop.lst isnt 'Pc')
            if @prop.saleTpTag
              showLstStr = ' &#183; ' + @prop.lstStr + ' &#183; '
            else
              showLstStr = @prop.lstStr + ' &#183; '
            span -> text "#{showLstStr or ''}"
          if @isLoggedIn is true
            span class: 'listing-prop-dom', -> text " #{@prop.dom} " + _ 'Days on Website'
            span -> text "(#{@prop.onD}#{if @prop.offD then ' - '+@prop.offD else ''})"
        p class:'listing-prop-rooms',->
          if @prop.rmbdrm or @prop.tbdrms or @prop.bdrms
            span class: 'listing-prop-room', ->
              span class: 'fa fa-rmbed'
              span -> text "#{@prop.rmbdrm or @prop.tbdrms or @prop.bdrms}"
          if @prop.rmbthrm or @prop.tbthrms or @prop.bthrms
            span class: 'listing-prop-room', ->
              span class: 'fa fa-rmbath'
              span -> text "#{@prop.rmbthrm or @prop.tbthrms or @prop.bthrms}"
          if @prop.rmgr or @prop.tgr or @prop.gr
            span class: 'listing-prop-room', ->
              span class: 'fa fa-rmcar'
              span -> text "#{@prop.rmgr or @prop.tgr or @prop.gr}"
          if @prop.sqft
            span class: 'listing-prop-sqft', -> text "| #{@prop.sqft} #{_('sqft')}"

        p class: 'listing-prop-mt',->
          if @prop.mt
            span class: 'listing-prop-mt-detail', style: 'font-size: 14px;color: #5A6569;margin-top: 10px;display: block;', ->
              text "#{_('Listing information last updated on')} "
              span id: 'listing-prop-mt-updated-date', -> text "#{@prop.mt}"
      div class:'listing-prop-actions', ->

    if @prop.mapLink
      a class: 'col-xs-4 col-sm-3 prop_detail_map', href: @prop.mapLink, target: '_blank', ->
        img class: 'prop_detail_map_icon',\
            src: '/img/mapmarkers/prop-detail-map.png',alt: 'Property Map Icon'
        div class: 'prop_detail_map_text', -> _('Open Map')

VIEW 'propSummary', ->
  show = (nm,v,isLink)->
    div class: 'col-sm-12 prop-summary-row', style: 'display: flex; justify-content: space-between; align-items: flex-start;', ->
      span class: 'summary-label', -> nm
      if isLink and v
        # Convert to string and ensure it's a string before splitting
        vStr = v.toString()
        if typeof vStr is 'string'
          div class: 'summary-value-container', ->
            # Extract domain from URL
            getDomain = (url) ->
              matches = url.match(/^(?:https?:\/\/)?([^\/]+)/)
              return matches?[1] or url
            
            links = vStr.split(',')
            for link, index in links
              if link?.trim()
                link = link.trim()
                domain = getDomain(link)
                if domain
                  a class: 'summary-value', target:"_blank", href:"#{link}", -> text domain
                  if index < links.length - 1
                    text ", "
        else
          span class: 'summary-value', -> text vStr
      else
        span class: 'summary-value', -> text v if v?
  section class: 'detail-section box-shadow border-radius-all', ->
    h4 class: 'detail-section-title', -> text _ 'Summary'
    div class: 'row', ->
      getAge = (prop) ->
        if prop.bltYr
          return prop.bltYr
        if prop.age
          return prop.age
        if prop.Age
          return prop.Age.v
        if prop.ConstructedDate
          return prop.ConstructedDate.v
        return ''
      if @prop.propId then show _('ID','prop'), @prop.propId
      if @prop.statusStr then show _('Status'), @prop.statusStr
      if @prop.dom and @isLoggedIn then show _('DOM','prop'), "#{@prop.dom} #{_('Days')}"
      if @prop.OwnershipType then show @prop.OwnershipType.n, @prop.OwnershipType.v
      if @prop.BusinessType then show @prop.BusinessType.n, @prop.BusinessType.v
      show _('Type','prop'), "#{@prop.ptype} #{if @prop.ptype2 then @prop.ptype2.join(',') else @prop.pstyl}"
      if @prop.ptype isnt 'Commercial'
        show _('Rooms','prop'), "#{_('Bed','room')}:#{if @prop.bdrms then @prop.bdrms else 0}#{if @prop.br_plus then '+' + @prop.br_plus else ''},#{if @prop.kch then _('Kitchen')+':'+@prop.kch+',' else ''}#{_('Bath','room')}:#{@prop.rmbthrm or @prop.tbthrms or @prop.bthrms or 0}"
      if @prop.sqft and @prop.ptype is 'Residential' then show _('Square Footage'), "#{@prop.sqft} #{_('sqft','prop')}"

      if ((@prop.front_ft and @prop.depth) or (@prop.dim))
        show _('Lot Size'), "#{if @prop.front_ft then "#{@prop.front_ft} * #{@prop.depth}" else @prop.dim} #{@prop.lotsz_code} #{@prop.irreg or ''}"
      if (@prop.lotsz) then show _('Land Size'), "#{@prop.lotsz} #{@prop.lotszUt or ''}"
      if @prop.gr then show _('Parking','prop'), "#{@prop.gr} #{if @prop.tgr then "(#{@prop.tgr})" else ''} #{if @prop.gatp then @prop.gatp else ''} #{if @prop.park_spcs then "+"+@prop.park_spcs else ''}"
      show _('Age','prop'), "#{if (@prop.bltYr or @prop.ConstructedDate) then _('Constructed Date') + ': ' else ''} #{getAge @prop}"
      if @prop.mfee then show _('Maint Fee','prop'),@prop.mfee
      if @prop.MaintenanceFeeType then show @prop.MaintenanceFeeType.n, @prop.MaintenanceFeeType.v
      if @prop.MaintenanceFeePaymentUnit then show @prop.MaintenanceFeePaymentUnit.n, @prop.MaintenanceFeePaymentUnit.v
      if @prop.psn then show _('Possession Date'), @prop.psn
      if @prop.stp_en is 'Rent' or @prop.stp_en is 'Lease'
        if @prop.rtp then show _('Rent Type','prop'), @prop.rtp
        if @prop.rgdr then show _('Gender Req','propertyListing'), @prop.rgdr
        if @prop.water_inc then show _('Water Inc','prop'), @prop.water_inc
        if @prop.hydro_inc then show _('Hydro Inc','prop'), @prop.hydro_inc
        if @prop.mintrm then show _('Min Term','prop'), @prop.mintrm + if @prop.mintrm then "#{ _('Month')}"
        if @prop.entr then show _('Entrance','prop'), @prop.entr
        if @prop.kchntp then show _('Kitchen','prop'), @prop.kchntp
        if @prop.lndrytp then show _('Laundry','prop'), @prop.lndrytp
        if @prop.wrmtp then show _('Washroom','prop'), @prop.wrmtp
        if @prop.smok then show _('Smoking Allowed','prop'), @prop.smok
        if @prop.pets then show _('Pets','prop'), @prop.pets
      if @prop.ltp is 'assignment'
        if @prop.prj then show _('Project Name','prop'), @prop.prj
        if @prop.fas then show _('Phase','prop'), @prop.fas
        if @prop.mdl then show _('Model','prop'), @prop.mdl
        if @prop.dvlpr then show _('Developer','prop'), @prop.dvlpr
        if @prop.bldr then show _('Builder','prop'), @prop.bldr
        if @prop.origpr then show _('Original Price','prop'), @prop.origpr
        if @prop.dpst then show _('Deposit','prop'), @prop.dpst
      if @prop.retail_a then show _('Retail Area','prop'), "#{@prop.retail_a} #{@prop.retail_ac}"
      if @prop.tot_area then show _('Total Area','prop'), "#{@prop.tot_area} #{@prop.tot_areacd}"
      if @prop.type_taxes then show _('Type Taxes','prop'), "#{@prop.type_taxes}"
      if @prop.rltr then show _('Listing Courtesy of','prop'), @prop.rltr
      if @prop.vturl then show _('Virtual Tour','prop'), @prop.vturl, true
  section class: 'detail-section box-shadow border-radius-all', ->
    h4 class: 'detail-section-title', -> text _('Detail')
    div class: 'row', ->
      if @prop.Building?.v
        h5 class: 'col-xs-12 detail-section-subtitle', -> @prop.Building.n
        for v in @prop.Building.v
          show v.t, if v.m then v.m.toString() else ''
      if @prop.Basement?.v
        h5 class: 'col-xs-12 detail-section-subtitle', -> @prop.Basement.n
        for v in @prop.Basement.v
          show v.t, if v.m then v.m.toString() else ''
      if @prop.Land?.v
        h5 class: 'col-xs-12 detail-section-subtitle', -> @prop.Land.n
        for v in @prop.Land.v
          if v.m isnt false
            show v.t, if v.m then v.m.toString() else ''
      if @prop.Parking
        h5 class: 'col-xs-12 detail-section-subtitle', -> @prop.Parking.n
        if @prop.Parking.v
          for v in @prop.Parking.v
            show v.t, if v.m then v.m.toString() else ''
        else
          for v in @prop.Parking
            show '', v.Name
      if @prop.Utilities?.v
        h5 class: 'col-xs-12 detail-section-subtitle', -> @prop.Utilities.n
        for v in @prop.Utilities.v
          show v.t, if v.m then v.m.toString() else ''
      if @prop.Surrounding?.v
        h5 class: 'col-xs-12 detail-section-subtitle', -> @prop.Surrounding.n
        for v in @prop.Surrounding.v
          show v.n, if v.v then v.v.toString() else ''
      if @prop.Other?.v
        h5 class: 'col-xs-12 detail-section-subtitle', -> @prop.Other.n
        for v in @prop.Other.v
          show v.n, if v.v then v.v.toString() else ''
      detailFields = [
        ['lpunt'],
        ['locker_num'],
        ['bsmt'],['den'],
        ['blcny'],
        ['grdn'],
        ['gym'],['pool'],
        ['fpop'],['lkr'],
        ['fpl'],['htb'],
        ['ac'],['heat'],
        ['wifi', ''],
        ['tv', ''],
        ['wsr', ''],
        ['dyr', ''],
        ['frdg', ''],
        ['movn', ''],
        ['frnshd', ''],
        ['share_perc'],
        ['lvl'], ['unt'],
        ['fce'],
        ['park_desig','park_spc1','park_desig_2','park_spc2'],
        ['constr1_out','constr2_out'],
        ['condo_corp','corp_num'],
        ['prop_mgmt'],
        ['com_cn_fee'],
        ['franchise'],
        ['freestandg']
      ]
      getShowCond = (n, prop) ->
        k = n[1]
        if k and (k is '')
          prop.stp_en is 'Rent' or prop.stp_en is 'Lease'
        else
          prop[n[0]]
      isPropFld = (i) ->
        /^[a-z]+/i.test(i)
      isTextFld = (i) ->
        /^[\-|\*]+/i.test(i)
      for n in detailFields
        if getShowCond(n, @prop)
          value = ''
          for i in n
            if isPropFld(i)
              value += if @prop[i] then @prop[i].toString() else ''
            if isTextFld(i)
              value += if i then i.toString() else ''
          show _ab(n[0],'propertyListing'), value
VIEW 'propRemarks', ->
  isTrebProp = (prop) ->
    if not prop._id
      false
    /^TRB/.test(prop._id)
  section class: 'detail-section box-shadow border-radius-all', ->
    h4 class: 'detail-section-title', -> text _('Remarks','props')
    div class: 'copyright-desc', -> text if @prop.m then @prop.m else ''
    if disclaimerTerms = @prop.disclaimerTerms #@prop.ddfID and (@prop.ddfID.length > 0)
      # p class: 'copyright-remark', -> trademarkDesc
      div class: 'copyright-remark', ->
        for term in disclaimerTerms
          p term
    if @prop.disclaimerIcons
      div class: 'copyright-imgs', ->
        for icon in @prop.disclaimerIcons
          a href: @prop.link, alt:icon.poweredByAlt, target: '_blank',->
            img src:icon.poweredBylogoPath, width: icon.poweredBylogoWidth
      # img class: 'copyright-img mls', src: '/img/mls_b-w.png', alt: 'MLS'
      # img class: 'copyright-img realtor', src: '/img/REALTOR_R.png', alt: 'Realtor R'
    if isTrebProp(@prop) and @prop.m_zh
      div class: 'copyright-desc disclaimer', ->
        span class:'text', -> text _('The following "Remarks" is automatically translated by Google Translate. Sellers,Listing agents, RealMaster, Canadian Real Estate Association and relevant Real Estate Boards do not provide any translation version and cannot guarantee the accuracy of the translation. In case of a discrepancy, the English original will prevail.')
        span class:'expand fa fa-ellipsis-h'
      div class: 'copyright-desc', -> text @prop.m_zh
      # Uncaught SyntaxError: Illegal return statement if return inside main function
      text """
      <script>
        if (window.$) {
          $('.disclaimer .expand').on('click', function() {
            $('.disclaimer .text').css('height', 'auto');
            $(this).hide();
          });
        }
      </script>
      """
      # coffeejs {},->
      #   if window.$
      #     $('.disclaimer .expand').on 'click',->
      #       # console.log '@@@@@@@@'
      #       $('.disclaimer .text').css('height','auto')
      #       $(this).hide()
      #     return
      # return


VIEW 'propOpenHouse', ->
  section class: 'detail-section box-shadow border-radius-all', ->
    h4 class: 'detail-section-title', -> text _('Open House')
    for oh in @prop?.showOhz or []
      div class: 'detail-oh-row', ->
        div class: 'detail-oh-day', -> text oh.f?.split(' ')[0].split('-')[2]
        div class: 'detail-oh-times', ->
          text "#{oh.f?.split(' ')[0]}<br/>#{oh.f?.split(' ')[1]} #{oh.f?.split(' ')[2] or ''} - #{oh.t?.split(' ')[1]} #{oh.t?.split(' ')[2] or ''}"
        div class: 'detail-oh-link-wrapper',->
          if oh.tp == 'P'
            div class: 'detail-oh-link',->
              i class:'fa fa-user'
              span class:'detail-oh-link-txt ',-> text  _('On Site','prop')
          if oh.l
            a class: 'detail-oh-link', href: oh.l, target: '_blank', ->
              i class: 'fa fa-video-camera'
              span class:'detail-oh-link-txt detail-oh-link-online',-> text  _('Online','prop')


VIEW 'propLocation', ->
  section id: 'location-section', class: 'detail-section box-shadow border-radius-all', ->
    if @prop.mapLink
      a class: 'detail-section-right-link', href: @prop.mapLink, target: '_blank', ->
        span -> _('Open Map')
        span class: 'fa fa-location-arrow'
    h4 class: 'detail-section-title', -> text _ "Location"
    if @prop.prov
      div class: 'row', ->
        div class: 'col-xs-5', -> text "#{_('Province', 'prop')}:"
        div class: 'col-xs-7', -> text "#{@prop.prov}"
    if @prop.city
      div class: 'row', ->
        div class: 'col-xs-5', -> text "#{_('City', 'prop')}:"
        div class: 'col-xs-7', -> text "#{@prop.city}"
    if @prop.cmty
      div class: 'row', ->
        div class: 'col-xs-5', -> text "#{_('Community', 'prop')}:"
        div class: 'col-xs-7', -> text "#{@prop.cmty} #{@prop.cmtycd or ''}"
    if @prop.crsst
      div class: 'row', ->
        div class: 'col-xs-5', -> text "#{_('Crossroad', 'prop')}:"
        div class: 'col-xs-7', -> text "#{@prop.crsst}"
    div class: 'row detail-map-links', ->
      propHasLatLng = @prop.lat and @prop.lng
      if (not @isCip) and (propHasLatLng)
        a class: 'col-xs-4 detail-map-link', href: "https://maps.google.com/maps?q=&layer=c&cbll=#{@prop.lat},#{@prop.lng}", target: '_blank', ->
          span class: 'fa fa-street-view'
          div -> text _('Street View')
      if propHasLatLng and (not @prop.hideAmenity)
        localLogicLink = "/1.5/SV/locallogic?lat=#{@prop.lat}&lng=#{@prop.lng}"
        a class: 'col-xs-4 detail-map-link', href: localLogicLink, target: '_blank', ->
          span class: 'fa fa-shopping-cart'
          div -> text _('Nearby Amenities')
      if propHasLatLng and (not @prop.hideCmty)
        a class: 'col-xs-4 detail-map-link', href: "/1.5/census/commu?year=2016&loc=#{@prop.lat},#{@prop.lng}", target: "_blank", ->
          span class: 'fa fa-pie-chart'
          div -> text _('Community')
VIEW 'propRooms', ->
  section class: 'detail-section box-shadow border-radius-all', ->
    h4 class: 'detail-section-title',style:'line-height: 24px;', ->
      text _ "Room"
      span class:'toggle ftm pull-right active',->
        span class:'toggle-handle'
    div class: 'row detail-room-head', ->
      div class: 'col-xs-4', -> text _ "Room"
      div class: 'col-xs-2', -> text _ "Level"
      div class: 'col-xs-2', -> text _ "Length"
      div class: 'col-xs-2', -> text _ "Width"
      div class: 'col-xs-2', -> text _('Area','unit')
    for rm in @rms
      div class: 'row detail-room-row', ->
        div class: 'col-xs-4', -> text rm.t
        div class: 'col-xs-2', -> text rm.l
        div class: 'col-xs-2 unit unit_m ',style:'display:none', -> text rm.h
        div class: 'col-xs-2 unit unit_m ',style:'display:none', -> text rm.w
        div class: 'col-xs-2 unit unit_m ',style:'display:none', -> text rm.area
        div class: 'col-xs-2 unit unit_ft', -> text rm.h_ft
        div class: 'col-xs-2 unit unit_ft', -> text rm.w_ft
        div class: 'col-xs-2 unit unit_ft', -> text rm.area_ft
      if rm.d
        div class: 'detail-room-des', -> text rm.d

VIEW 'schools', ->
  section class: 'detail-section box-shadow border-radius-all', ->
    h4 class: 'detail-section-title', -> text _ "School Info"
    privateSchoolLink = "/school/private?loc=#{@prop.lat},#{@prop.lng}"
    a class: 'detail-section-right-link', href: privateSchoolLink, target: '_blank', ->
      span -> _('Private Schools')
    showCategory = (v) ->
      span class: 'school-category', -> text v
    for sch in @schs
      div class: 'school', ->
        div class: 'school-type', -> text "#{sch.gf}-#{sch.gt} #{_('Grades Only','school-grade')}"
        h5 class: 'school-title', -> text sch.nm
        p class: 'school-location', ->
          span class: 'school-address', -> text "#{sch.addr}, #{sch.city}"
          span class: 'school-distance', -> text "#{sch.dist/1000} km"
        div class: 'school-categories', ->
          schCats =
            ele: 'Elementary'
            mid: 'Middle'
            hgh: 'Secondary'
            pri: 'Private'
            chtr: 'Charter'
            sprt: 'Separate'
            frcp: 'Francophone'
            idp: 'Independent'
            frna: 'First Nation'
            ptst: 'Protestant'
            c: 'Catholic'
            ef: 'Extended French'
            fi: 'French Immersion Program'
            eng: 'English'
          for fld,nm of schCats
            showCategory _(nm, 'school') if sch[fld]
        if sch.keyFacts?.length
          div class:'school-rating-wrapper', ->
            for v in sch.keyFacts
              div class: 'school-rating', ->
                p ->
                  span class:'bold',-> text "#{v.val}"
                  if v.valTotal
                    span style:'color:#999;font-size:12px',-> text "/#{v.valTotal}"
                    if v.diffRank > 0
                      span class: 'fa fa-long-arrow-down'
                    else if v.diffRank < 0
                      span class: 'fa fa-long-arrow-up'
                  if v.isStyle2 and v.rating
                    span -> text " | "
                    span class:'bold',style:"",-> text "#{v.rating}"
                p -> 
                  span style:'color: #6f6f6f;font-size:12px;line-height: 14px;', -> 
                    text "#{v.key}#{if v.grade then '/'+v.grade else ''}"

VIEW 'propStats', ->
  section class: 'detail-section box-shadow row border-radius-all', ->
    h4 class: 'detail-section-title', -> text _ "Community"
    p class: '', -> text "Community Willowdale West"
    p class: '', -> text "Crossroad Yonge St South Of Finch"
    div id: 'community-map'
    div class: 'row', ->
      div class: 'col-sm-4'
      div class: 'col-sm-4', ->
        canvas id: 'chart-age', class: 'chart'
      div class: 'col-sm-4'
