UserModel = MODEL 'User'

MenuRegistryModel = MODEL 'MenuRegistryIns'
getMenu = ({req, user}) ->
  # webComment 包含了 forumAdmin里的权限，但是以防之后有某一个进行了变更，所以都加上了
  if (req.isAllowed 'webComment',user) or (req.isAllowed 'forumAdmin',user)
    return {name: 'Forum', url: '/1.5/forum', index: 1,isIfrm: 1}
  return null
MenuRegistryModel.registerMenu('forum', getMenu)

APP 'forum'

# Web端论坛主页路由
GET (req,resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    isZh = req.locale() in ['zh' , 'zh-cn']

    # 检查设备类型，为web端提供论坛访问
    devType = req.getDevType()

    # 如果是管理员或有权限用户，重定向到admin页面
    if (UserModel.accessAllowed 'forumAdmin', user) or (UserModel.accessAllowed 'webComment', user)
      encodeUrl = encodeURIComponent('/1.5/forum')
      resp.redirect "/adminindex?d=#{encodeUrl}"
    # 为web端用户提供论坛访问（browser, mobile, wechat）
    else if devType in ['browser', 'mobile', 'wechat']
      # 准备web端论坛页面的上下文数据
      js = ['/js/vue3.min.js','/libs/jquery-1.9.1.min.js','/js/xpull.js',
        '/js/overthrow/overthrow.min.js ','/js/entry/commons.js ',
        '/js/axios.min.js', '/js/forum/indexPage.js']
      ctx =
        d: req.param('d') or '/forum'
        js: js
        css: ['/css/bootstrap.css', '/css/apps/forum.css','/css/xpull.css','/css/apps/drawers.css']
        page:'news'
        appmode: null  # web端不需要appmode
        lang: req.locale()
        isChinaIP: req.isChinaIP()
        os: if req.isIOSDevice() then 'ios' else 'android'
        isApp: false  # 明确标识为web端
        isWeb: true   # 新增web端标识

      # 渲染web端论坛页面
      resp.ckup 'forum/home', ctx, '_', {noAngular: true, noref: true}
    else
      # 其他设备类型重定向到app下载页面
      resp.redirect '/app-download'

# VIEW 'forum', ->
#   iframe src:'/1.5/forum',
#   width:'700px',
#   height:'700px',
#   onload:'this.height=document.body.offsetHeight;'
