UserModel = MODEL 'User'

MenuRegistryModel = MODEL 'MenuRegistryIns'
getMenu = ({req, user}) ->
  # webComment 包含了 forumAdmin里的权限，但是以防之后有某一个进行了变更，所以都加上了
  if (req.isAllowed 'webComment',user) or (req.isAllowed 'forumAdmin',user)
    return {name: 'Forum', url: '/1.5/forum', index: 1,isIfrm: 1}
  return null
MenuRegistryModel.registerMenu('forum', getMenu)

APP 'forum'

GET (req,resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    isZh = req.locale() in ['zh' , 'zh-cn']
    # resp.ckup 'wecard-list'
    if (UserModel.accessAllowed 'forumAdmin', user) or (UserModel.accessAllowed 'webComment', user)
      encodeUrl = encodeURIComponent('/1.5/forum')
      resp.redirect "/adminindex?d=#{encodeUrl}"
      # resp.ckup 'forum', {isZh:isZh}, '_', {noref:true, side:1}
    else
      resp.ckup 'contact-us-vip', {}, '_', {noAngular:1, noAppcss:1, noRatchetcss:1, bootstrap:1}

# VIEW 'forum', ->
#   iframe src:'/1.5/forum',
#   width:'700px',
#   height:'700px',
#   onload:'this.height=document.body.offsetHeight;'

# Web端论坛主页路由
GET '/list', (req,resp)->
  UserModel.auth {req,resp,url:'/login'},(user)->
    # 准备web端论坛页面的上下文数据
    js = ['/js/vue3.min.js','/libs/jquery-1.9.1.min.js','/js/xpull.js',
      '/js/overthrow/overthrow.min.js ','/js/entry/commons.js ',
      '/js/axios.min.js', '/js/forum/indexPage.js']
    ctx =
      d: req.param('d') or '/'
      js: js
      css: ['/css/bootstrap.css', '/css/apps/forum.css','/css/xpull.css','/css/apps/drawers.css']
      page:'news'
      appmode: null  # web端不需要appmode
      lang: req.locale()
      isChinaIP: req.isChinaIP()
      os: if req.isIOSDevice() then 'ios' else 'android'
      isApp: req.getDevType() is 'app'

    # 渲染web端论坛页面
    resp.ckup 'forum/homeResponsive', ctx, 'layout', {noAngular: true, noref: true}

