
config = CONFIG(['serverBase','share'])
debug = DEBUG()
{replaceRM2REImagePath} = INCLUDE 'libapp.propertyImage'
Properties = MODEL 'Properties'
UserModel = MODEL 'User'
PropFavouriteModel = MODEL 'PropFavourite'
GroupModel = MODEL 'Group'
ClaimModel = MODEL 'Claim'
{respError} = INCLUDE 'libapp.responseHelper'
{genListingPicReplaceUrls,setUpPropRMPlusFields,LIST_PROP_FIELDS,isRMListing} = INCLUDE 'libapp.properties'
{listingPicUrlReplace} = INCLUDE 'libapp.common'
{fullNameOrNickname} = INCLUDE 'libapp.user'
{replaceRM2REImagePath} = INCLUDE 'libapp.propertyImage'
DateHelper = INCLUDE 'lib.helpers_date'
objectCache = INCLUDE 'libapp.objectCache'
TokenModel = MODEL 'Token'

APP 'propClaim'

GET '', (req, resp,next)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login?d=/1.5/index'}, (user)->
    uid = user._id
    try
      isRealGroup = await GroupModel.isInGroup {uid:uid,groupName:':inReal'}
    catch err
      debug.error err
    if (not req.isAllowed 'claimAdmin') and (not isRealGroup)
      return resp.redirect '/1.5/index'
    layoutCfg = {noAngular:true,noUserModal:true,noRatchetJs:true}
    # if req.param 'popup'
    #   layoutCfg.bodyStyle = 'background-color: transparent;overflow: hidden;'
    resp.ckup 'saves/dealsEdit',{},'_',layoutCfg

# 更新claim信息
POST 'edit',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    uid = user._id
    try
      isRealGroup = await GroupModel.isInGroup {uid:uid,groupName:':inReal'}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR,resp}
    if (not req.isAllowed 'claimAdmin') and (not isRealGroup)
      debug.error {msg:"editClaim:#{MSG_STRINGS.NO_AUTHORIZED}",uid:uid,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    body = req.body
    if not ClaimModel.checkSaveClaim body
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
    try
      propMergedTop = await Properties.findMergedTopPropAsids body.pid, {sldd:1,saletp:1}
      unless propMergedTop?.mergedTopId
        return respError {clientMsg:req.l10n(MSG_STRINGS.NOT_FOUND), resp}
      mergedIds = Properties.getMergedIdsFromMergedTop(propMergedTop)
      extraInfo = propMergedTop.extraInfo[propMergedTop.mergedTopId]
      body.mergedIds = mergedIds
      body.mergedTopId = propMergedTop.mergedTopId
      if body.onlyUnitTest # 为了防止ut每年需要改时间，ut采用传入的sldd
        sldd = body.sldd
      else if sldd = extraInfo?.sldd # 从数据库房源中拿到的sldd
        body.sldd = sldd
      else
        return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
      body.saletp = extraInfo?.saletp
      {err,returnList} = await ClaimModel.saveBaseOnPc uid,body
      msg = 'Claimed'
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp}
    if err
      return respError {clientMsg:req.l10n(err), resp}
    # 认领token加分
    try
      for item in returnList
        {err,tokenId} = await TokenModel.applyToken(item.tokenQuery)
        if tokenId
          await ClaimModel.updateTokenIdById item.id,tokenId
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.TOKEN_ERROR), sysErr:err, resp}
    ret = {ok:1,msg:req.l10n(msg)}
    ret.ids = returnList.map((r) -> r.id)
    return resp.send ret

# 删除claim信息
POST 'delete',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    body = req.body
    if (not body.id)
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
    if (not req.isAllowed 'claimAdmin')
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      deleted = await ClaimModel.deleteById body.id
      msg = 'Disclaimed'
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), sysErr:err, resp}
    if err
      return respError {clientMsg:req.l10n(err), resp}
    # 删除增加的token
    try
      await TokenModel.cancelToken(deleted.tokenId)
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.TOKEN_DELETE_ERROR), sysErr:err, resp}
    ret = {ok:1,msg:req.l10n(msg)}
    return resp.send ret

getClaimTokenValues = (type) ->
  keyMap = ClaimModel.tokenKeysMap[type]
  tokenMap = {}
  for s in keyMap
    token = objectCache.getObjectCacheList 'systemTokenKeys',s
    tokenMap[s.slice(9)] = token.t/100
  return tokenMap

# 检查claim pcOfShr信息
POST 'detail',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    body = req.body
    if (not body.id)
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
    pid = body.id
    uid = user._id
    try
      propMergedTop = await Properties.findMergedTopPropAsids pid
      mergedTopId = propMergedTop?.mergedTopId
      prop = await Properties.findOneByIDAsync mergedTopId,Object.assign {}, LIST_PROP_FIELDS, {sldd:1}
      claim = await ClaimModel.getById {uid,pid:pid}
      if claim?
        claimList = await formatPropAndClaimedUserInfo {list:claim,req,user}
      else
        q = {uid,pid:mergedTopId,lst:prop.lst,sldd:prop.sldd,saletp: prop.saletp}
        claimInfo = await ClaimModel.checkIfCanClaim q
        userMap = await ClaimModel.claimedPc [mergedTopId],user.locale
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), resp}
    l10n=(a,b)->req.l10n(a,b)
    _ab=(a,b)->req._ab(a,b)
    genListingPicReplaceUrls(prop, req.isChinaIP(), {
      use3rdPic:config.serverBase?.use3rdPic,
      isPad:req.isPad(),
      shareHostNameCn:config.share?.hostNameCn,
      useThumb:true
    })
    setUpPropRMPlusFields prop,l10n,_ab
    if claimList?
      return resp.send {ok:1,list:claimList,prop}
    if not claimInfo.canClaim
      return respError {clientMsg:req.l10n('This property can not claim.'), resp}
    if userMap and (propClaimedInfo = userMap[mergedTopId])
      for k in ['coop','listing']
        continue unless v = propClaimedInfo[k]
        delete propClaimedInfo[k]?[uid]
        claimedUser = Object.values(propClaimedInfo[k])
        claimInfo[k].claimedUser = claimedUser
    prop.claimInfo = claimInfo
    prop.claimSystemToken = getClaimTokenValues claimInfo.tp
    return resp.send {ok:1,prop}

# 根据参数获取claim列表
POST 'list',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,errorCode:0 ,resp} unless user
    uid = user._id
    try
      isRealGroup = await GroupModel.isInGroup {uid,groupName:':inReal'}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR,resp}
    isNoteAdmin = req.isAllowed 'claimAdmin'
    body = req.body
    if (not isNoteAdmin) and ((not isRealGroup) or body?.agent)
      debug.error {msg:"get Claim list:#{MSG_STRINGS.NO_AUTHORIZED}",uid,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    ids = []
    try
      list = await ClaimModel.getList {uid,body,isNoteAdmin}
      returnList = await formatPropAndClaimedUserInfo {list,req,user,sort:body.sort,getProps:1}
    catch err
      debug.error err
      return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), sysErr:err, resp}

    return resp.send {ok:1,list:returnList}

formatProp = ({ids,req})->
  propMap = {}
  _props = await Properties.findListingsByIDsAsync ids,{projection:LIST_PROP_FIELDS}
  l10n=(a,b)->req.l10n(a,b)
  _ab=(a,b)->req._ab(a,b)
  if _props?.length
    for _prop in _props
      genListingPicReplaceUrls(_prop, req.isChinaIP(), {use3rdPic:config.serverBase?.use3rdPic,isPad:req.isPad(),shareHostNameCn:config.share?.hostNameCn,useThumb:1})
      setUpPropRMPlusFields _prop,l10n,_ab
      _prop.del = false
      propMap[_prop._id] = _prop
  return propMap


formatPropAndClaimedUserInfo = ({list,req,user,sort,getProps})->
  propMap = {}
  l10n=(a,b)->req.l10n(a,b)
  ids = []
  for l in list
    ids.push l.pid
  if getProps
    propMap = await formatProp {ids,req}
  userMap = await ClaimModel.claimedPc ids,user.locale
  uid = user._id.toString()
  lastDate = ''
  sortField = 'ts'
  datefunction = 'date2str'
  if sort is 'sldd'
    sortField = 'sldd'
    datefunction = 'dateNum2str'
  for l in list
    usersInfo = userMap[l.pid]?[l.side]
    user = Object.assign({},usersInfo?[l.uid])
    l.claimedUser = Object.values(usersInfo).filter((u) -> u.fnm isnt user.fnm)
    l.user = user
    # 处理是否显示日期
    l.dateString = DateHelper[datefunction](l[sortField])
    if lastDate is l.dateString
      l.showDate = false
    else
      l.showDate = true
      lastDate = l.dateString
    l.claimMt = DateHelper.formatCmntTs(l.ts,l10n)

    if not l?.lock
      l.canDelete = true
    if propMap[l.pid]
      l.prop = propMap[l.pid]
  return list
