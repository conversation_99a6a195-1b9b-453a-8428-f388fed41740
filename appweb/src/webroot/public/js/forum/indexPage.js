function time(t){return(t=new Date(t)).getMonth()+1+"/"+t.getDate()+" "+t.getHours()+":"+(t.getMinutes()<10?"0":"")+t.getMinutes()}function day(t){if(t)return(t=new Date(t)).getUTCDate()}function yearMonth(t){if(t)return(t=new Date(t)).getFullYear()+"."+(t.getUTCMonth()+1)}function number(t,e){return null!=t?(e=parseInt(e),isNaN(t)?0:parseFloat(t.toFixed(e))):t}function propPrice(t,e){return null!=t?(t=parseInt(t),isNaN(t)||0==t?"":(t<1e3?t+="":t=t<1e4?(t/1e3).toFixed(1)+"K":t<999500?Math.round(t/1e3).toFixed(0)+"K":(t/1e6).toFixed(e=e||1)+"M",t)):""}function percentage(t,e){return null!=t?(t=parseFloat(t),isNaN(t)?0:(100*t).toFixed(2)):t}function dotdate(t,e,s="."){if(!t)return"";"number"==typeof t&&(t=(t+="").slice(0,4)+"/"+t.slice(4,6)+"/"+t.slice(6,8)),"string"!=typeof t||/\d+Z/.test(t)||(t+=" EST");var o=e?"年":s,i=e?"月":s,r=e?"日":"";if(/^2\d{3}-\d{1,2}-\d{1,2}/.test(t)&&!/\d+Z/.test(t)){var a=t.split(" ")[0].split("-");return a[0]+o+a[1]+i+a[2]+r}var n=new Date(t);return!n||isNaN(n.getTime())?t:n.getFullYear()+o+(n.getMonth()+1)+i+n.getDate()+r}function datetime(t){return(t=new Date(t)).getMonth()+1+"/"+t.getDate()+"/"+t.getFullYear()+" "+t.getHours()+":"+(t.getMinutes()<10?"0":"")+t.getMinutes()}function monthNameAndDate(t){if(!t)return"";var e=new Date(t);return["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sept","Oct","Nov","Dec"][e.getMonth()]+"."+e.getDate()}function currency(t,e="$",s){try{if("string"==typeof t&&-1!=t.indexOf(e))return t;var o=parseInt(t);if(isNaN(o))return null;o<100&&s<2&&(s=2);var i=t.toString().split(".");return i[0]=i[0].replace(/\B(?=(\d{3})+(?!\d))/g,","),0==s?i[1]=void 0:s>0&&i[1]&&(i[1]=i[1].substr(0,s)),e+i.filter((function(t){return t})).join(".")}catch(t){return console.error(t),null}}function arrayValue(t){return Array.isArray(t)?t.join(" "):t}var filters={time:time,day:day,number:number,dotdate:dotdate,datetime:datetime,propPrice:propPrice,percentage:percentage,yearMonth:yearMonth,monthNameAndDate:monthNameAndDate,currency:currency,arrayValue:arrayValue},rmsrvMixins={created:function(){"undefined"!=typeof RMSrv&&null!==RMSrv&&(this.ready=!0)},methods:{trackEventOnGoogle(t,e,s,o){trackEventOnGoogle(t,e,s,o)},exMap(t,e){let s;return this.dispVar.isApp||(document.location.href="/adPage/needAPP"),t.useMlatlng||"N"===t.daddr&&t.lat&&t.lng?s=t.lat+","+t.lng:(s=(t.city_en||t.city||"")+", "+(t.prov_en||t.prov||"")+", "+(t.cnty_en||t.cnty||""),s="N"!==t.daddr?(t.addr||"")+", "+s:s+", "+t.zip),e=(e||this.dispVar.exMapURL)+encodeURIComponent(s),RMSrv.showInBrowser(e)},goBack(){if("nativeMap"==vars.src||"nativeAutocomplete"==vars.src)return window.rmCall(":ctx::cancel");vars.d?document.location.href=vars.d:window.history.back()},sprintf(){var t=arguments,e=1;return t[0].replace(/%((%)|s|d)/g,(function(s){var o=null;if(s[2])o=s[2];else{if(o=t[e],"%d"===s)o=parseFloat(o),isNaN(o)&&(o=0);e++}return o}))},appendLocToUrl(t,e,s){if(null!=e.lat&&null!=e.lng){var o=t.indexOf("?")>0?"&":"?";return t+=o+"loc="+e.lat+","+e.lng}return t},appendCityToUrl(t,e,s={}){if(!e.o)return t;var o=t.indexOf("?")>0?"&":"?";return t+=o+"city="+e.o,e.p&&(t+="&prov="+e.p),e.n&&(t+="&cityName="+e.n),e.pn&&(t+="&provName="+e.pn),e.lat&&(t+="&lat="+e.lat),e.lng&&(t+="&lng="+e.lng),s.saletp&&(t+="&saletp="+s.saletp),null!=s.dom&&(t+="&dom="+s.dom),null!=s.oh&&(t+="&oh="+!0),s.ptype&&(t+="&ptype="+s.ptype),t},appendDomain(t){var e=window.location.href.split("/");return t=e[0]+"//"+e[2]+t},clickedAd(t,e,s,o){var i=t._id;if(t.inapp)return window.location=t.tgt;e&&trackEventOnGoogle(e,"clickPos"+s),i=this.appendDomain("/adJump/"+i),RMSrv.showInBrowser(i)},goTo(t,e={}){if(t.googleCat&&t.googleAction&&trackEventOnGoogle(t.googleCat,t.googleAction),t.t){let e=t.t;"For Rent"==t.t&&(e="Lease");var s=t.cat||"homeTopDrawer";trackEventOnGoogle(s,"open"+e)}var o=t.url,i=t.ipb,r=this;if(o){if(t.login&&!this.dispVar.isLoggedIn)return window.location="/1.5/user/login";if(!this.jumping){if(t.vipplus&&!this.dispVar.isVipPlus)return this.confirmVip();if(t.trebuser&&!this.dispVar.hasAid)return this.confirmTreb();if(t.t,"Agent"==t.t&&!t.direct)return this.dispVar.isLoggedIn?this.dispVar.isProdSales||this.dispVar.marketAdmin?window.location=t.url:null:window.location="/1.5/user/login";if("Services"==t.t)return window.location=o;if(1==i){return t.jumpUrl&&(o=t.jumpUrl+"?url="+encodeURIComponent(t.url)),this.tbrowser(o,{backButton:{image:"back",imagePressed:"back_pressed",align:"left",event:"backPressed"}})}if(3==i)return RMSrv.scanQR("/1.5/iframe?u=");if(4==i)return console.log(o),RMSrv.showInBrowser(o);if(1==t.loc){var a=this.dispVar.userCity;o=this.appendCityToUrl(o,a)}if(t.projQuery){var n=this.dispVar.projLastQuery||{};o+="?";for(let t of["city","prov","mode","tp1"])n[t]&&(o+=t+"="+n[t],o+="&"+t+"Name="+n[t+"Name"],o+="&")}if(1==t.gps){a=this.dispVar.userCity;o=this.appendLocToUrl(o,a)}1==t.loccmty&&(o=this.appendCityToUrl(o,e)),t.tpName&&(o+="&tpName="+this.$_(t.t,t.ctx)),this.jumping=!1,r.isNewerVer(r.dispVar.coreVer,"5.8.0")&&/mapSearch|autocomplete/.test(o)&&!/mode=list/.test(o)||(r.jumping=!0),setTimeout((function(){window.location=o}),10)}}},clearCache(){"undefined"!=typeof RMSrv&&null!==RMSrv&&RMSrv.clearCache&&RMSrv.clearCache()},confirmNoFnLn:function(t,e,s,o){e=e||"To be presented here, please complete your personal profile.";var i=this.$_?this.$_:this.$parent.$_,r=i(e),a=i("Later"),n=i("Do it Now");s=s||"";return RMSrv.dialogConfirm(r,(function(t){t+""=="2"?window.location="/1.5/settings/editProfile":o&&(window.location=o)}),s,[a,n])},confirmSettings:function(t,e){t=t||"To find nearby houses and schools you need to enable location";var s=this.$_?this.$_:this.$parent.$_;"function"!=typeof s&&(s=t=>t);var o=s(t),i=s("Later"),r=s("Go to settings"),a=a||"";return RMSrv.dialogConfirm(o,(function(t){t+""=="2"?RMSrv.openSettings():"function"==typeof e&&e()}),a,[i,r])},confirmNotAvailable(t){t=t||"According to the Real Estate Board notice, the sold price information is open by Oct. 22. We will keep you updated at earliest time possible.";var e=this.$_?this.$_:this.$parent.$_,s=e(t),o=e("I Know"),i=i||"";return RMSrv.dialogConfirm(s,(function(t){}),i,[o])},confirmUpgrade:function(t,e){e=e||"Only available in new version! Upgrade and get more advanced features.";var s=this.$_?this.$_:this.$parent.$_,o=s(e),i=s("Later"),r=s("Upgrade"),a=this.appendDomain("/app-download");return RMSrv.dialogConfirm(o,(function(e){t&&(a+="?lang="+t),e+""=="2"&&RMSrv.closeAndRedirect(a)}),"Upgrade",[i,r])},confirmVip:function(t,e){e=e||"Available only for Premium VIP user! Upgrade and get more advanced features.";var s=this.$_?this.$_:this.$parent.$_,o=s(e),i=s("Later"),r=s("See More");return RMSrv.dialogConfirm(o,(function(t){t+""=="2"&&RMSrv.showInBrowser("https://www.realmaster.ca/membership")}),"VIP",[i,r])},tbrowser:function(t,e={}){var s;s={toolbar:{height:44,color:"#E03131"},closeButton:{image:"close",align:"right",event:"closePressed"},fullscreen:!1},s=Object.assign(s,e),RMSrv.openTBrowser(t,s)}}},LazyImage={name:"v-lazy-item",props:{dispVar:{type:Object,default:function(){return{}}},dataindex:{type:Number},imgstyle:{type:String,default:""},imgclass:{type:String,default:""},alt:{type:String,default:""},load:{type:Function,default:function(t){}},error:{type:Function,default:function(t){window.hanndleImgUrlError&&hanndleImgUrlError(t.target||t.srcElement)}},data:{type:Object,default:function(t){return{}}},emit:{type:Boolean,default:!0},src:{type:String,default:""},placeholder:{type:String,default:"data:image/png;base64,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"}},data:()=>({lazyExist:!0,intersected:!1,intersectionOptions:{}}),computed:{computedSrc:function(){return this.lazyExist&&!this.intersected?this.placeholder:this.src?this.src:this.placeholder}},methods:{imgOnPress(t){this.emit&&window.bus.$emit("lazy-image-onpress",t,this.data)}},mounted(){"IntersectionObserver"in window?(this.observer=new IntersectionObserver((t=>{t[0].isIntersecting&&(this.intersected=!0,this.observer.disconnect())}),this.intersectionOptions),this.observer.observe(this.$el)):window.replaceSrc?setTimeout((function(){replaceSrc()}),100):this.lazyExist=!1},destroyed(){"IntersectionObserver"in window&&this.observer.disconnect()},template:'\n  <div class="v-lazy-item">\n    <img :rm-data-src="src" :src="computedSrc" @error="error" :alt="alt" :class="imgclass" :style="imgstyle" @load="load" @click="imgOnPress" :dataindex="dataindex" />\n  </div>\n  '},forumCommonMixins={created:function(){},data:()=>({monthArray:["Jan","Feb","Mar","Apr","May","Jun","Jul","Aug","Sep","Oct","Nov","Dec"]}),computed:{computedAdmin:function(){var t=!1,e=this;if(!this.dispVar)return!1;if(this.post&&this.dispVar.userGroups){var s=this.dispVar.userGroups.find((function(t){return t._id==e.post.gid}));s&&(s.isAdmin||s.isOwner)&&(t=!0)}return this.dispVar.forumAdmin||t}},methods:{showComments(t){event.stopPropagation(),window.bus.$emit("showComments",t)},getImageUrl:t=>t?"url("+t+"), url('/img/user-icon-placeholder.png')":"url('/img/user-icon-placeholder.png')",formatTs2:function(t){if(!t)return"";t=new Date(t);var e=new Date-t;return e<6e4?this.$_("Just now"):e>864e5?this.$_(this.monthArray[t.getMonth()])+" "+t.getDate():e>36e5?parseInt(e/36e5)+this.$_("Hrs"):parseInt(e/6e4)+this.$_("Mins")},trimStr(t,e){if(!t||!e)return"";var s=0,o=0,i="";for(o=0;o<t.length;o++){if(t.charCodeAt(o)>255?s+=2:s++,s>e)return i+"...";i+=t.charAt(o)}return t},formatTs(t){if(t){var e=(t=new Date(t)).getMinutes();return e<10&&(e="0"+e),t.getFullYear()+"-"+(t.getMonth()+1)+"-"+t.getDate()+" "+t.getHours()+":"+e}return""},blockCmnt(t,e){axios.post("/1.5/forum/blockCmnt",t).then((t=>(t=t.data).ok?e(null,t):(t.e,null))).catch((t=>e(t.status+":"+t.statusText,null)))}}},checkNotification={props:{className:{type:String,default:""}},data:()=>({isOpenNotify:!0,hasGoogleService:!0,canRefresh:!1,refresh:!1,dispVar:{isEmailVerified:!0}}),mounted(){if(window.bus){var t=this;window.bus.$on("pagedata-retrieved",(function(e){if(t.dispVar=Object.assign(t.dispVar,e),e.coreVer){var s=t.$parent.isNewerVer(e.coreVer,"6.3.1");RMSrv.hasGoogleService&&RMSrv.hasGoogleService((function(e){t.hasGoogleService=e,1==e&&(t.checkNotify(),s&&RMSrv.onAppStateChange?RMSrv.onAppStateChange((e=>t.checkNotify())):t.canRefresh=!0)}))}}))}else console.error("global bus is required!")},computed:{showRefresh:function(){return this.canRefresh&&this.refresh},computedHasIssueReceivePN(){return!this.dispVar.isEmailVerified||!this.hasGoogleService||!this.isOpenNotify}},methods:{isAuthedRet:(t="")=>["authorized","granted"].indexOf(t)>-1,checkNotify(){var t=this;this.refresh=!1,RMSrv.onReady((()=>{RMSrv.permissions("notification","check",-1,(function(e){t.isOpenNotify=t.isAuthedRet(e)}))}))},openSetting(){this.refresh=!0,RMSrv.openSettings&&RMSrv.openSettings()},openVerifyPopup(){var t=this;RMSrv.getPageContentIframe("/1.5/settings/verify?verify=v","#callBackString",{transparent:!0},(function(e){if(":cancel"!=e)try{JSON.parse(e).emlV&&(t.dispVar.isEmailVerified=!0)}catch(t){console.error(t)}}))}},template:'\n<div class="check-notification" v-if=\'computedHasIssueReceivePN\' :class=\'className\'>\n  <span class="status fa fa-exclamation-circle" style="color: rgb(255, 205, 0); display: inline;"></span>\n  <span class="field">\n    <span class="field-name" v-if=\'!dispVar.isEmailVerified\'> {{$_(\'Email is not verified.\')}}\n      <span class="explain" @click="openVerifyPopup()">{{$_(\'Verify\')}}</span>\n    </span>\n    <span class="field-name" v-else-if=\'!hasGoogleService\'> {{$_(\'Install Google Play to get listing updates.\')}}\n    </span>\n    <span class="field-name" v-else> {{$_(\'Turn on notifications to get listing updates.\')}}\n      <span class="explain" @click="checkNotify()" v-if=\'showRefresh\'>{{$_("Refresh")}}</span>\n      <span class="explain" @click="openSetting()" v-else>{{$_(\'Notification Settings\')}}</span>\n    </span>\n  </span>\n</div>\n  '},forumMixins={created:function(){},data:()=>({}),computed:{},methods:{reloadPosts(){var t=this;t.allPosts=[];var e={};t.computedListKey&&(t[t.computedListKey]=[],e.listKey=this.computedListKey,e.hasMoreKey=this.computedHasMoreKey),document.getElementsByClassName("tabs-container").length>0&&document.getElementsByClassName("tabs-container")[0].click(),t.pgNum=1;var s=t.getSearchParmas();s.page=t.pgNum,t.getAllPost(s,e)},goTo(t){window.location.href=t},openTBrowser(t,e={}){this.dispVar.isApp?this.tbrowser(t):window.location.href=t},changeStatus(t,e,s,o){var i=this;axios.post("/1.5/forum/changeStatus",{id:e,status:t,gid:s}).then((e=>{(e=e.data).ok&&(i.post.readStatus=t,o())})).catch((t=>{console.error(t.status+":"+t.statusText)}))},refreshPost(t,e){var s=this;axios.post("/1.5/forum/detail/"+t,{gid:e,type:"summary"}).then((t=>{(t=t.data).ok&&window.bus.$emit("forum-view-close",t.post)})).catch((t=>{s.loading=!1,console.error(t.status+":"+t.statusText)}))},showPostView(t,e,s,o,i,r){var a=this;if(t&&"null"!=t){if(window.vars&&vars.postid&&vars.postid==t){vars.postid=null;var n=new URL(window.location);n.searchParams.set("postid",null),n.search=n.searchParams,n=n.toString(),history.replaceState({},null,n),timeout=0}if(o&&i&&r)axios.post("/1.5/forum/adClick",{id:t,index:0,gid:s}).then((t=>{(t=t.data).ok&&RMSrv.showInBrowser(i)})).catch((t=>{console.error(t.status+":"+t.statusText)}));else{let o=null;o="psch"==e?"/1.5/school/private/detail/"+t:"sch"==e?"/1.5/school/public/detail?id="+t+"&redirect=1":"/1.5/forum/details?id="+t,s&&(o+="&gid="+s),o=this.appendDomain(o);var l={hide:!1,title:this.$_("RealMaster")};if(!this.dispVar.isApp)return o+="&iswebAdmin=1",document.location.href=o;o.indexOf("?")>0?o+="&inFrame=1":o+="?inFrame=1",RMSrv.getPageContent(o,"#callBackString",l,(function(e){try{if(/^cmd-redirect:/.test(e)){var o=e.split("cmd-redirect:")[1];return window.location=o}}catch(t){console.error(t)}if(":cancel"==e||":later"==e)if(s){var i=":cancel"==e?"read":"later";a.changeStatus(i,t,s,(function(){a.refreshPost(t,s)}))}else a.refreshPost(t,s);else{console.log(e);try{var r=JSON.parse(e);window.bus.$emit("forum-view-close",r)}catch(t){console.error(t)}}}))}}},appendDomain(t){var e=window.location.href.split("/");return t=e[0]+"//"+e[2]+t},getAllPost(t,e={}){var s=this,o="posts_more";e.hasMoreKey&&(o=e.hasMoreKey),s[o]=!1,s.doSearch(t,(function(i){s.loading=!1,s.refreshing=!1,i.length>20&&(s[o]=!0);var r="allPosts";e.listKey&&(r=e.listKey),s[r]=s[r].concat(i.splice(0,20)),s.updateForumsAfterBlocked(r),t.scroll&&setTimeout((()=>{s.scrollPostIntoView()}),20)}))},isForumUserBlocked(t,{blkUids:e,blkCmnts:s}){var o=t.uid;return s[t._id]&&s[t._id].b||o&&e[o]},updateForumsAfterBlocked(t="allPosts"){try{var e=JSON.parse(localStorage.getItem("blkUids"))||{},s=JSON.parse(localStorage.getItem("blkCmnts"))||{}}catch(t){alert(t)}var o=this[t].length,i=[];for(let r=0;r<o;r++){const o=this[t][r];this.isForumUserBlocked(o,{blkUids:e,blkCmnts:s})||i.push(o)}this[t]=i},doSearch(t,e){var s=this;trackEventOnGoogle(this.form,"search"),axios.post("/1.5/forum/query",t).then((t=>{(t=t.data).ok?e(t.forums):t.url&&s.goTo(t.url)})).catch((t=>{console.error(t)}))},setUpPreview(t){var e,s;s=t.naturalWidth,e=t.naturalHeight,s>0&&e>0&&(this.sizes.push(s+"x"+e),t.setAttribute("data-size",s+"x"+e))},listScrolled(){var t=this;t.scrollElement=document.getElementById("forum-containter"),!t.waiting&&t.posts_more&&(t.waiting=!0,t.loading=!0,setTimeout((function(){t.waiting=!1;var e=t.scrollElement;if(e.scrollHeight-e.scrollTop<=e.clientHeight+260)if(t.posts_more){t.pgNum+=1;var s=t.getSearchParmas();s.page=t.pgNum,t.getAllPost(s)}else t.loading=!1;else t.loading=!1}),400))},formatNews(t){var e=document.querySelectorAll(".post-content *[style]");for(var s of e)s.style.removeProperty("font-size"),s.style.removeProperty("line-height"),s.style.removeProperty("color");var o=document.querySelectorAll(".post-content a")||[];for(var i of o)if("realmaster"!=i.getAttribute("data-src"))i.setAttribute("href","javascript:void(0)");else{var r=i.getAttribute("href");/^tel:/.test(r)||/^mailto:/.test(r)||(t?(i.setAttribute("href","javascript:void(0)"),i.setAttribute("onclick","window.RMSrv.showInBrowser('"+r+"')")):(i.setAttribute("href",r),i.setAttribute("target","_blank")))}var a=document.querySelectorAll(".post-content img")||[];for(var n of a){var l=n.getAttribute("data-src");n.getAttribute("data-s");/^(http|https):\/\/mmbiz.qpic.cn/i.test(n.src)?n.src=n.src.replace(/&tp=\w+&wxfrom=\d&wx_lazy=\d/gi,""):l&&(n.src=l),n.setAttribute("style",""),n.style.height="auto",n.style.width="100%",n.getAttribute("data-ignore")||(n.addEventListener("click",this.previewPic),this.setUpPreview(n)),n.parentElement&&(n.parentElement.style.height="auto",n.parentElement.style.width="100%")}var p=document.querySelectorAll("iframe")||[];for(var c of p){var d=c.getAttribute("style");l=c.getAttribute("data-src"),r=c.getAttribute("src");"realmaster"!=l&&(c.src="");var g=/width=((\d|\.)+)&height=((\d|\.)+)/;if(g.test(r)){var u=window.innerWidth-30,h=parseFloat(r.match(g)[1]),m=parseFloat(r.match(g)[3])/h*u,f=r.replace(/width=((\d|\.)+)&/,"width="+u+"&");f=f.replace(/&height=((\d|\.)+)&/,"&height="+m+"&"),c.src=f}if(d){m=c.style.height;var A=c.style.minHeight;c.setAttribute("style",""),c.style.height=m||"auto",c.style.minHeight=A||"240px",c.style.width="100%"}}},getThumbUrl(t){if(t){var e=`img.${this.dispVar.shareHostNameCn||"realmaster.cn"}`;return"url("+t+"),url("+t.replace(e,"img.realmaster.com")+"), url('/img/no-pic.png')"}return"url('/img/no-pic.png')"},inValidNickName:(t,e)=>!1,saveForumName(t){axios.post("/1.5/forum/setFornm",{fornm:this.nickname}).then((e=>{(e=e.data).ok?t():t(e.e)})).catch((t=>{console.error(t)}))},handleMsgAfterBlock(t){var e=this;window.bus.$emit("flash-message",t),setTimeout((function(){e.toggleFlagModal(!1),e.updateForumsAfterBlocked()}),1e3)},blockAuthor(t){var e=t.uid;if(!this.dispVar.isLoggedIn)return this.blkUids[e]=1,localStorage.setItem("blkUids",JSON.stringify(this.blkUids)),void this.handleMsgAfterBlock(this.$_("User was block"));trackEventOnGoogle(this.form,"blockAuthor");var s=this;this.blockCmnt({type:"user",uid:e},(function(t,o){if(t)return window.bus.$emit("flash-message",t);s.blkUids[e]=1,localStorage.setItem("blkUids",JSON.stringify(s.blkUids)),s.handleMsgAfterBlock(o.msg)}))},updateBlkCmnts(t){this.blkCmnts[t._id]?this.blkCmnts[t._id].b=1:this.blkCmnts[t._id]={b:1},localStorage.setItem("blkCmnts",JSON.stringify(this.blkCmnts))},blockPost(t){var e=this;if(!this.dispVar.isLoggedIn)return this.updateBlkCmnts(t),window.bus.$emit("flash-message",this.$_("Post was blocked")),void setTimeout((function(){e.toggleFlagModal(!1),e.showMask=!1,e.reloadPosts()}),1e3);trackEventOnGoogle(this.form,"blockPost");var s={type:"post",forumId:t._id};this.blockCmnt(s,(function(s,o){if(s)return window.bus.$emit("flash-message",s);setTimeout((function(){e.toggleFlagModal(!1),e.showMask=!1,e.reloadPosts()}),1e3),e.updateBlkCmnts(t),e.handleMsgAfterBlock(o.msg)}))},reportPost(t){this.toggleFlagModal(!1),this.showMask=!1,this.showReportForm=!0;var e=this.reportForm,{protocol:s,host:o}=location;if(this.dispVar.isLoggedIn){var{eml:i,nm:r}=this.dispVar.sessionUser;e.userForm.eml=i,e.userForm.nm=r}trackEventOnGoogle(this.form,"reportPost"),e.userForm.id=t._id,e.userForm.url=s+"//"+o+"/1.5/forum/details?id="+t._id,e.userForm.img=t.thumb,e.userForm.tl=t.tl,this.reportForm=e},closeReportForm(){this.showReportForm=!1,this.showMask=!1;var t=this.reportForm;t.userForm.violation="",t.userForm.m="",t.userForm.url="",t.userForm.img="",t.userForm.id="",this.reportForm=t},contactUs(){trackEventOnGoogle(this.form,"contactUs"),RMSrv.showInBrowser("https://realmaster.ca/about-us")},toggleFlagModal(t){var e=document.querySelector("body");t?(e.classList.add("smb-open"),this.flagPost=t,this.showMask=!0):(e.classList.remove("smb-open"),this.flagPost=!1,this.showMask=!1)}}},ForumSummaryCard={mixins:[pageDataMixins,forumMixins,forumCommonMixins],props:{postType:{type:String,default:"forum"},parentPage:{type:String,default:""},post:{type:Object,default:function(){return{hasUpdate:!1,src:"news"}}},dispVar:{type:Object,default:function(){return{}}},hideStickyIcon:{type:Boolean,default:!1},noTag:{type:Object,default:function(){return{}}},noTagAction:{type:Boolean,default:!1},isWeb:{type:Boolean,default:!1},displayPage:{type:String,default:"all"}},components:{LazyImage:LazyImage},computed:{computedThumb:function(){return this.post.thumb?this.post.thumb:"sch"==this.post.src?"/img/school/school_forum.png":"psch"==this.post.src?"/img/school/school_forum_p.png":null},computedForumFas:function(){return this.isForumFas(this.dispVar,{city:this.post.city,prov:this.post.prov})},commentsHeight:function(){return this.post.tags&&this.post.tags[0]||"property"==this.post.src?40:60},computedVc:function(){return(this.post.vc||0)+(this.post.vcc||0)},computedTp:function(){return this.post.tpbl&&this.post.tpbl[0]?this.post.tpbl[0]:this.post.tp?this.$_("Topic"):null}},data:()=>({}),mounted(){window.bus?this.post.del||(this.post.del=!1):console.error("global bus is required!")},methods:{toggleFlagModal(t){this.$parent.toggleFlagModal(t)},parseDate(t=""){var e=t.split(" "),s=e[0].split("-"),o=e[1].split("-");return s[1]+"."+s[2]+" "+o[0]},imageLoadError(){this.post.thumb=null},openView(){return event.stopPropagation(),this.post.hasUpdate&&(this.post.hasUpdate=!1),this.isWeb?window.open("/1.5/forum/webedit?web=true&id="+this.post._id,"_blank"):"wecard"==this.postType?this.$parent.showWecard(this.post):this.$parent.showPostView(this.post._id,this.post.src,this.post.gid),!1},showAd(){event.stopPropagation(),this.$parent.showPostView(this.post._id,this.post.src,this.post.gid,this.post.adInlist,this.post.adTop,this.post.adTopPhoto)},addCityFilter(){if(!this.noTagAction)return event.stopPropagation(),this.isWeb?window.open(window.location.href+"&city="+this.post.city+"&prov="+this.post.prov,"_blank"):(this.$parent.curCity={o:this.post.city,p:this.post.prov,cnty:this.post.cnty},this.$parent.reloadPosts()),!1},openTag(){if(!this.noTagAction){event.stopPropagation();var t=this.post.tags[0];return this.isWeb?window.open(window.location.href+"&tag="+t,"_blank"):(this.$parent.tag=t,this.$parent.reloadPosts()),!1}},openGroup(){return event.stopPropagation(),this.isWeb?window.open(window.location.href+"&gid="+gid,"_blank"):(this.$parent.gid=this.post.gid,this.$parent.gnm=this.post.gnm,this.$parent.reloadPosts()),!1},openBySrcView(t){this.noTagAction||(event.stopPropagation(),this.isWeb?window.open(window.location.href+"&src="+t,"_blank"):(this.$parent.src=t,this.$parent.reloadPosts()))},openTp(){if(!this.noTagAction){if(event.stopPropagation(),this.post.tp)this.isWeb?window.open("http://"+this.dispVar.reqHost+"/forum/"+this.post._id+"/"+formatUrlStr(this.post.tl),"_blank"):this.$parent.showPostView(this.post._id,this.post.src);else{var t=this;axios.get("/1.5/forum/findPostByTp/"+t.computedTp).then((e=>{if(!(e=e.data).ok)return window.bus.$emit("flash-message",e.e);this.isWeb?window.open("http://"+t.dispVar.reqHost+"/forum/"+e.postid,"_blank"):this.$parent.showPostView(e.postid,this.post.src)})).catch((()=>{console.error(err.status+":"+err.statusText)}))}return!1}}},template:'\n  <div class="forum-summary-card" v-bind:class="{summaryWeb: isWeb , greybg: post.gid}">\n    <div class="post-top-div" style="position:relative; display:block!important;" v-if="post.adInlist && post.adTopPhoto && post.adTop">\n        <div class="edit" v-if="dispVar.forumAdmin" @click="openView()"><span>{{post.vcad0}}</span><span>{{$_(\'Edit\')}}</span></div><img class="post-top-img" v-if="post.adTopPhoto" :src="post.adTopPhoto" @click="showAd()" />\n        <div class="post-top-text">{{$_(\'AD\')}}</div>\n    </div>\n    <div v-else style="height: 103px;" :class="{noCity: dispVar.forumAdmin && !post.city && !post.cnty && !post.prov }">\n        <div class="post-summary">\n            <div class="post-title" v-bind:class="{deleted: post.del}" @click="openView()"><span class="red-dot-forum fa fa-circle" v-if="post.hasUpdate"></span><span class="red-button" v-if="post.sticky && !noTag.top">{{$_(\'TOP\')}}</span><span class="red-button" @click="openGroup()" v-if="post.gid && post.gnm && !noTag.gid">{{post.gnm}}</span><span class="red-button blue" @click="openTp()" v-else-if="(post.tpbl && post.tpbl.length) || (post.tp && !noTag.topic) ">{{computedTp}}</span><span class="red-button blue" @click="openTag()" v-else-if="(post.tags && post.tags[0]) && !noTag.tag"><span v-if="post.tags[0]==\'HOT\'">{{$_(\'HOT\')}}</span><span v-else>{{post.tags[0]}}</span></span><span class="red-button blue" @click="openBySrcView(\'property\')" v-else-if="post.src==\'property\' && post.cc>0 && !noTag.property">{{$_(\'Home Review\')}}</span><span class="red-button blue" @click="openBySrcView(\'sch\')" v-else-if="(post.src==\'sch\'||post.src==\'psch\') && !noTag.sch && !noTag.psch">{{$_(\'School Review\')}}</span><span class="red-button blue" @click="addCityFilter()" v-show="(post.city||post.prov||post.cnty) && !noTag.city && post.cnty!==\'No City\'">{{$_(post.city||post.prov||post.cnty,\'city\')}}</span><span class="txt" v-if="[\'property\',\'psch\',\'sch\'].indexOf(post.src)>=0&& post.cmntl">{{post.cmntl + \' \' + post.tl}}</span><span class="txt" v-else>{{post.tl}}</span></div>\n            <div class="post-comments">\n              <span class="post-name pull-left">{{trimStr(post.fornm,12)}}</span>\n              <span class="post-bottom">\n                <span v-if="post.src!=\'sch\' && post.src!==\'psch\'">\n                  <span v-if="dispVar.isAdmin">{{post.vc}} | {{post.vcapp}} | {{post.vcc}}</span>\n                  <span v-else>{{computedVc}}</span>\n                  <span class="fa fa-eye" style="padding-left:\'5px\'"></span>\n                </span>\n                <span v-if="(post.cc > 0) && (!post.discmnt || dispVar.forumAdmin)">\n                  <span v-if="post.src!=\'sch\' && post.src!==\'psch\'">|</span>\n                  <span>{{post.cc}}</span>\n                  <span class="fa fa-comments"></span>\n                </span>\n                <span v-if="dispVar.forumAdmin && post.similars">| S: {{post.similars.length}} |</span>\n              </span>\n              <span class="post-ts" v-if="dispVar.isAdmin">{{formatTs2(post.mt)}}</span>\n              <span class="post-ts" style="padding-right:5px;">{{formatTs2(post.ts)}}</span>\n              <span class="realtor-only" v-if="post.realtorOnly">{{$_(\'Realtor Only\')}}</span>\n              <span class="icon icon-close reportForumIcon" v-if="parentPage == \'forum\'" data-sub="report forum" :data-id="post._id" @click="toggleFlagModal(post)"></span>\n            </div>\n        </div>\n        <div style="padding-top: 5px;" @click="openView()">\n            <lazy-image class="img post-img" v-if="computedThumb" :alt="post.tl" :error="imageLoadError" :src="computedThumb" :imgstyle="\'width: 120px;height: 90px;background-size: 100% 100%;\'"></lazy-image><span class="vidRecord" v-if="post.src == \'video\' && post.vidRecord || post.passedLive" :class="{noPic:!computedThumb}"><i class="fa fa-video-camera"></i></span><span class="vidLive vidLive1" v-if="post.src == \'video\' && post.vidLive && post.isLiving" :class="{noPic:!computedThumb}">{{$_(\'LIVE\')}}</span><span class="vidLive vidLive2" v-if="post.src == \'video\' && post.vidLive && !post.isLiving && !post.passedLive" :class="{noPic:!computedThumb}">{{parseDate(post.ohv)}}</span>\n            <div class="img post-img" v-if="!computedThumb">\n                <div class="no-img">{{post.tl? post.tl.substr(0,1) : \'\'}}</div>\n            </div>\n        </div>\n    </div>\n  </div>\n  \n  '},ReportForumForm={components:{PageSpinner:PageSpinner},props:{dispVar:{type:Object,default:function(){return{}}},target:{type:Object,default:function(){return{}}},needWxid:{type:Boolean,default:!1},cstyle:{type:Object,default:function(){return{}}},owner:{type:Object,default:function(){return{vip:!1}}},userForm:{type:Object},page:{type:String,default:"forumHome"},isWeb:{type:Boolean,default:!1},title:{type:String,default:""},feedurl:{type:String,default:"/chat/api/feedback"},mblNotRequired:{type:Boolean,default:!1},mRequired:{type:Boolean,default:!1}},data(){return{closeCountdown:3,closeCountdownTimer:null,options:["Uncivil or offensive","Safety issue, fraud or illegal","Misinformation","Copyright violation","Spam or annoying"],nmErr:!1,emlErr:!1,mblErr:!1,mErr:!1,sending:!1,message:null,picUrls:this.$parent.picUrls||[]}},mounted(){if(this.closeCountdownTimer&&(clearInterval(this.closeCountdownTimer),this.closeCountdown=3),window.bus){var t=this;bus.$on("reset-signup",(function(e){t.message=null;var s=document.querySelector("#ForumSignUpSuccess"),o=document.querySelector("#signUpForm");o&&t.owner.vip&&(o.style.display="block"),s&&(s.style.display="none")}))}else console.error("global bus is required!")},methods:{closeReportForm(){this.$parent.closeReportForm()},signUp(){var t=this;t.nmErr=!1,t.emlErr=!1,t.mblErr=!1,t.mErr=!1,t.message=null;let e=document.querySelector("#ForumSignUpSuccess");e.style.display="none",t.sending||(t.sending=!0,axios.post(t.feedurl,t.userForm).then((s=>(s=s.data,t.sending=!1,s.ok?(document.querySelector("#signUpForm").style.display="none",t.closeCountdownTimer=setInterval((()=>{t.closeCountdown>1?t.closeCountdown--:(clearInterval(t.closeCountdownTimer),t.$parent.closeReportForm())}),1e3),s.msg&&(t.message=s.msg)):t.message=s.err||s.e,e.style.display="block"))).catch((()=>{t.sending=!1,console.error("Server Error")})))}},template:'\n  <div id="reportForm">\n    <div class="mask show"></div>\n    <div id="reportFormContainer" :class="page">\n        <div id="ForumSignUpSuccess" :style="cstyle">\n          <i class="fa fa-check-circle" v-if="!message"></i>\n          <span v-if="message">{{message}}</span>\n          <span v-else>{{$_(\'Your feedback has been submitted.\')}}\n            <span>{{$_(\'This will close in \') + closeCountdown + \'s\'}}</span>\n          </span>\n        </div>\n        <form id="signUpForm" :class="{\'visible\':owner.vip, \'web\':isWeb}" :style="cstyle">\n            <page-spinner :loading.sync="sending"></page-spinner>\n            <div class="tl"><span v-if="title">{{title}}</span><span v-else>{{$_(\'Contact Me\')}}</span></div>\n            <div><label><span class="tp">{{$_(\'What is wrong\')}}</span><span class="ast">*</span></label><select style="height: 35px;background-color:#fff" v-model="userForm.violation">\n                    <option value="" selected="selected">{{$_(\'Please Select\')}}</option>\n                    <option v-for="(o,idx) in options" :value="o">{{$_(o)}}</option>\n                </select></div>\n            <div><label><span class="tp">{{$_(\'Message\',\'signUpForm\')}}</span><span class="ast" v-if="mRequired">*</span></label><textarea class="m" style="padding:10px;" rows="3" v-model="userForm.m" :class="{\'error\':mErr}"></textarea></div>\n            <div><button class="btn btn-block btn-signup" :disabled="!userForm.violation" type="button" @click="signUp()">{{$_(\'Submit\',\'signUpForm\')}}</button></div>\n        </form><img class="close" @click="closeReportForm()" src="/img/staging/close.png" />\n    </div>\n  </div>\n'},BlockPopup={props:{dispVar:{type:Object,default:function(){return{}}},flagPost:{type:Object,default:function(){return{}}}},data:()=>({}),mounted(){if(this.closeCountdownTimer&&(clearInterval(this.closeCountdownTimer),this.closeCountdown=3),window.bus);else console.error("global bus is required!")},methods:{blockPost(){this.$parent.blockPost(this.flagPost)},blockAuthor(){this.$parent.blockAuthor(this.flagPost)},reportPost(){this.$parent.reportPost(this.flagPost)},contactUs(){this.$parent.contactUs()},toggleFlagModal(){this.$parent.toggleFlagModal()}},template:'\n<div>\n  <nav class="menu slide-menu-bottom smb-auto">\n    <ul class="table-view">\n        <li class="table-view-cell"><a class="flagModalLink" data-sub="block post" :data-id="flagPost&&flagPost._id?flagPost._id:\'\'" @click="blockPost(flagPost)"><i class="fa fa-ban"></i>{{$_(\'Block this post\')}}</a></li>\n        <li class="table-view-cell" v-if="flagPost && flagPost.author && (!flagPost.author.isAdmin)"><a class="flagModalLink" data-sub="block user" :data-id="flagPost&&flagPost.author&&flagPost.author._id?flagPost.author._id:\'\'" @click="blockAuthor(flagPost)"><i class="fa fa-ban"></i>{{$_(\'Block this user\')}}</a></li>\n        <li class="table-view-cell"><a class="flagModalLink" data-sub="report info" :data-id="flagPost&&flagPost._id?flagPost._id:\'\'" @click="reportPost(flagPost)"><i class="fa fa-flag"></i>{{$_(\'Report\',\'forum\')}}</a></li>\n        <li class="table-view-cell"><a class="flagModalLink" data-sub="contact us" :data-id="flagPost&&flagPost._id?flagPost._id:\'\'" @click="contactUs()"><i class="fa fa-fw fa-phone"></i>{{$_(\'Contact us\')}}</a></li>\n        <li class="cancel table-view-cell" @click="toggleFlagModal(false)" data-sub="cancel report" :data-id="flagPost&&flagPost._id?flagPost._id:\'\'">{{$_(\'Cancel\')}}</li>\n    </ul>\n  </nav>\n</div>\n'},PageSpinner={props:{loading:{type:Boolean,default:!1}},data:()=>({}),template:'\n  <div class="overlay loader-wrapper" id="busy-icon" v-show="loading">\n    <div class="loader"></div>\n  </div>\n  '},// 检测设备类型和环境
function detectEnvironment() {
  var isApp = typeof RMSrv !== 'undefined' && RMSrv !== null;
  var isWeb = !isApp;
  var isMobile = /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);

  return {
    isApp: isApp,
    isWeb: isWeb,
    isMobile: isMobile,
    isDesktop: !isMobile
  };
}

// 全局环境变量
var ENV = detectEnvironment();

forum={data:()=>({filterType:"",showSearchbar:!1,search:"",datas:["isCip","isApp","isLoggedIn","lang","forumAdmin","sessionUser","reqHost","shareHost","newsAdmin","globalTags","isVipRealtor","canWebComment","isAdmin","isRealtor","edmAdmin","coreVer","isEmailVerified","userGroups"],dispVar:{isLoggedIn:!1,lang:"zh",isApp:ENV.isApp,isWeb:ENV.isWeb,isCip:!1,forumAdmin:!1,sessionUser:"",newsAdmin:!1,globalTags:[],isVipRealtor:!1,canWebComment:!1,isAdmin:!1,isRealtor:!1,edmAdmin:!1,userGroups:[]},forumLangs:[],flagPost:null,allPosts:[],posts_more:!1,qna:[],qna_more:!1,todayChoices:[],todayChoices_more:!1,news:[],news_more:!1,curCity:{},exCities:null,curPost:{},loading:!1,displayPage:"all",pgNum:1,scrollElement:null,waiting:!1,refreshing:!1,postid:null,tag:null,src:null,gid:null,gnm:"",tagList:[],showMask:!1,showMenu:!1,title:"RealMaster",blkUids:{},blkCmnts:{},showedForumTerms:!0,showReportForm:!1,reportForm:{postId:null,title:"Report",feedurl:"/1.5/form/forminput",userForm:{m:"",tp:"feedback",subjectTp:"Forum Complaint",formid:"system",id:"",violation:"",tl:""}},menuMapping:{all:"News",topic:"Topic",my_post:"My Post",my_reply:"My Reply",my_group:"My Group",my_favourite:"My Favourite",post_admin:"My Admin Posts"},form:"news"}),beforeMount(){this.getTagList()},mounted(){this.$getTranslate(this);var t=this;"undefined"!=typeof RMSrv&&RMSrv||(t.dispVar.isApp=!1,t.dispVar.isWeb=!0),localStorage.showedForumTerms||(t.showedForumTerms=!1,setTimeout((()=>{t.showMask=!0}),100));try{this.blkUids=JSON.parse(localStorage.getItem("blkUids"))||{},this.blkCmnts=JSON.parse(localStorage.getItem("blkCmnts"))||{}}catch(t){return window.bus.$emit("flash-message",t.toString())}(t.$nextTick((function(){t.dispVar.isApp&&"undefined"!=typeof $&&$("#forum-containter").xpull({callback:function(){t.reloadPosts()}})})),window.bus)?((t=this).getPageData(t.datas,{},!0),window.bus.$on("pagedata-retrieved",(function(e){if(t.dispVar=Object.assign(t.dispVar,e),t.dispVar.sessionUser.splang&&(t.forumLangs=t.dispVar.sessionUser.splang),vars.tag?t.filterByTag(vars.tag):vars.city?t.curCity=city:vars.section?(vars.group&&(t.gid=vars.group,t.gnm=vars.gnm),t.selectFilter(vars.section)):t.selectFilter("all"),vars.page&&vars.postid)for(;t.pgNum<vars.page;){t.pgNum+=1;var s=t.getSearchParmas();s.page=t.pgNum,t.getAllPost(s)}if(vars.flagModalFromHome)return t.getForumDetail(vars.postid,"flagModalFromHome");vars.postid&&t.showPostView(vars.postid,vars.src,vars.gid)})),bus.$on("forum-view-close",(function(e){function s(t){return t._id==e._id}t.postid=null;var o=t.allPosts.find(s);if(o&&(o.vc=o.vc+1,o.vcapp&&(o.vcapp=o.vcapp+1),o.cc=e.cc,o.vt=e.vt,o.fav=e.fav,o.cmntl=e.cmntl,o.realtorOnly=e.realtorOnly,o.hasUpdate=!1),"read"==e.readStatus&&"my_group"!=t.displayPage){var i=t.allPosts.findIndex(s);t.allPosts.splice(i,1)}e.newPostId&&t.showPostView(e.newPostId,e.src,e.gid),e.tag&&(t.tag=e.tag,t.reloadPosts()),e.loadPosts&&t.reloadPosts()}))):console.error("global bus is required!")},methods:{scrollPostIntoView(){var t=0;if(this.allPosts.forEach(((e,s)=>{if(e._id==vars.postid)return t=s})),t>0){document.getElementsByClassName("forum-summary-card")[t].scrollIntoView(),this.scrollElement=document.getElementById("forum-containter");var e=this.scrollElement.scrollTop;this.scrollElement.scrollTop=e-89}},getTranslate:function(t){if(t)return TRANSLATES[t]||t},clickMask(){this.showMask=!1,this.showMenu=!1,this.toggleFlagModal(!1),this.closeSearchbar()},searchForums(){this.allPosts=[],this.showSearchbar=!1,this.showMask=!1,this.getAllPost(this.getSearchParmas())},openSearchBar(){this.showSearchbar=!0,this.showMask=!0},closeSearchbar(){this.showSearchbar=!1,this.showMask=!1},handleForumLangs(t){var e=this.forumLangs,s=e.indexOf(t);s>-1?e.splice(s,1):e.push(t)},_agreeTerms(){localStorage.showedForumTerms=!0,this.showedForumTerms=!0,this.showMask=!1},agreeTerms(){var t=this;t.dispVar.isLoggedIn?axios.post("/1.5/forum/updateSplang",{splang:t.forumLangs}).then((e=>{if(!(e=e.data).ok)return window.bus.$emit("flash-message",e.e);t._agreeTerms()})).catch((()=>{console.error(err.status+":"+err.statusText)})):t._agreeTerms()},displayBlockName(t){return"en"==(this.dispVar.lang||"en")?t.nm_en||t.nm||t.nm_zh:t.nm_zh||t.nm||t.nm_en},back(){if(vars.isPopup)return window.rmCall(":ctx::cancel");var t=vars.d||"/1.5/index";this.goTo(t)},dropDownClicked(){this.showMenu=!0,this.showMask=!0},getTagList(t){var e=this;axios.get("/1.5/forum/tags?type=all").then((s=>{if(!(s=s.data).ok)return window.bus.$emit("flash-message",s.e);var o=s.tags||[];o.sort(((t,e)=>t.sort?e.sort?t.sort-e.sort:-1:1)),e.tagList=o,t&&t()})).catch((()=>{console.error(err.status+":"+err.statusText)}))},filterByTag(t){["property","post","sch"].indexOf(t)>=0?(this.src=t,this.tag=null):(this.src=null,this.tag=t),trackEventOnGoogle(this.form,"filterByTag",this.src,this.tag),this.reloadPosts()},clearTag(t){switch(t){case"all":this.tag=null,this.src=null,this.curCity={};break;case"tag":this.tag=null;break;case"gid":this.gid=null;break;case"src":this.src=null;break;case"city":this.curCity={}}this.reloadPosts()},goTo(t){window.location.href=t},goToEdit(){t="/1.5/forum?section="+this.displayPage,vars.d&&(t=t+"&d="+vars.d);var t="/1.5/forum/edit?d="+encodeURIComponent(t);trackEventOnGoogle(this.form,"goToEdit"),this.goTo(t)},getSearchParmas(){var t={},e=this;return e.search&&(t.search=e.search),["my_post","topic","my_reply","my_favourite","my_reply","my_group"].indexOf(e.displayPage)>-1?t.category=e.displayPage:"post_admin"==e.displayPage&&(t.src="post"),this.curCity&&this.curCity.o&&(t.city=this.curCity.o),this.gid&&(t.gid=this.gid),this.curCity&&this.curCity.p&&(t.prov=this.curCity.p),this.curCity&&this.curCity.cnty&&(t.cnty=this.curCity.cnty),this.tag&&(t.tag=this.tag),["property","post","psch","sch"].indexOf(this.src)>=0&&(t.src=this.src),vars.page&&vars.postid&&e.pgNum==vars.page&&(t.scroll=!0),t},clearModals(){this.filterType=""},close(){this.showMask=!1,this.showMenu=!1,this.toggleFlagModal(!1)},selectFilter(t){var e=this;e.title=e.menuMapping[t],e.close(),e.allPosts=[],e.displayPage=e.filterType,e.filterType="",this.tag=null,this.src=null,this.curCity={},e.displayPage=t,trackEventOnGoogle(this.form,"selectFilter",t),e.getAllPost(e.getSearchParmas())},goToForumList(t){var e="/1.5/forum/list?src="+t;["sticky","qna","post"].includes(t)&&(e+="&city="+this.curCity.o+"&prov="+this.curCity.p),"post_admin"==t&&(e="/1.5/forum/list?src=post"),window.location.href=e},getForumDetail(t,e){var s=this;trackEventOnGoogle(this.form,"forumDetail"),axios.post("/1.5/forum/detail/"+t,{}).then((t=>{(t=t.data).ok&&"flagModalFromHome"==e&&(t.post.author={},t.post.author.isAdmin=vars.authorIsAdmin||!1,vars.authorId&&(t.post.author._id=vars.authorId),s.toggleFlagModal(t.post))})).catch((t=>{s.loading=!1,console.error(t.status+":"+t.statusText)}))}},computed:{computedWith:()=>.78*window.innerWidth-24+"px",hasTag:function(){return this.tag||this.gid||["property","post","sch","psch"].indexOf(this.src)>=0||this.curCity.o||this.curCity.p||this.curCity.cnty},noTag:function(){return{tag:this.tag,property:"property"==this.src,psch:"psch"==this.src,sch:"sch"==this.src,topic:"topic"==this.displayPage,city:this.curCity.o,gid:this.gid}}}};initUrlVars();var app=Vue.createApp(forum);app.mixin(rmsrvMixins),app.mixin(pageDataMixins),app.mixin(forumMixins),app.mixin(forumCommonMixins),app.component("forum-summary-card",ForumSummaryCard),app.component("report-forum-form",ReportForumForm),app.component("flash-message",flashMessage),app.component("block-popup",BlockPopup),app.component("check-notification",checkNotification),app.config.globalProperties.$filters={currency:filters.currency},trans.install(app,{ref:Vue.ref}),app.mount("#forumHome");
