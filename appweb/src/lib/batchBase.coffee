###*
* Bacth file base setup.
* support keywords: MODEL, DEF, <PERSON>LLEC<PERSON><PERSON>, MSG_STRINGS, DEBUG, INCLUDE, LOAD, CONFIG, EXIT, AVGS, NOTIFY
* procedure:
*   check args
*   load debug, include, config, notify, exit modules
*   load db if config has db info
*   load startup script
* features:
*   notify admin users(pn or sms)
*   sigChangeVerbose: use SIGUSR1/2 to change verbose level
*   params: yargs
*   getConfig: get config file contents
* usage:
*   ./start.sh lib/batchBase.coffee batchfile-name ...other parameters
###

debugHelper = require './debug'
loader      = require './loader'
dmodule     = require './dmodule'
coffeemate  = require './coffeemate3'
# notify      = require './notify'
msgStrings  = require './msgStrings'
appBase     = require './appBase'
os          = require 'os'
fs          = require 'fs'
path        = require 'path'
abbr        = require './abbreviate'
#yargs = require 'yargs'
# read config
cfgfile = process.env.RMBASE_FILE_CFG
cfg = coffeemate.parse_config cfgfile

module.exports.mSrvCfg = cfg.dbs
module.exports.dnld_dir = cfg.imageStore?.dnld_dir
dryRun = false
isProd = false
noGeo = false
isDebug = false
avgs = []
avgsLast = null
batchFile = null
preloadModel = false
noDB = false
noMailLog = false
abbreviator = null
module.exports.hasParam = (key)->
  key = key.toLowerCase()
  for a in avgs
    a = (''+a).toLowerCase()
    if a is key
      return true
  false

# load debug
debug =null
_setDebugger = ->
  debugHelper.setDefaultModule batchFile
  #this debug is for batchBase file only.
  debugLevel = if isDebug then debugHelper.DEBUG else debugHelper.INFO
  if cfg?.serverBase?.logLevel and (cfg.serverBase.logLevel > debugLevel)
    debugLevel = cfg.serverBase.logLevel
  debug = debugHelper.getDebugger debugLevel
  #debug = debugHelper.getDebugger(cfg?.app_config?.logLevel) unless debug
  #通过setdebug来改变loader里的debug的level
  loader.setDebug debug
  debugHelper.setDefaultThreshhold debugLevel
_setDebugger()

srcPath = path.dirname(__dirname)
debug.debug 'srcPath',srcPath

# if logLevel = cfg?.app_config?.logLevel
#   console.log 'logLevel',logLevel
#   this debug will change system default debug.
#   debugHelper.setDefaultThreshhold logLevel

module.exports.getConfig = getConfig = -> Object.assign {},cfg
module.exports.errorNotify = errorNotify = (err,cb)->
  errMsg = err?.message or err?.toString()
  debug.error errMsg
  if cfg.serverBase?.developer_mode
    return cb()
  # use config
  if sendSMS = service 'sendSMS'
    # Get phone numbers from config
    alertSMS = cfg?.contact?.alertSMS or '6472223733'
    
    # Convert to array if it's a comma-separated string
    phoneNumbers = if typeof alertSMS is 'string' then alertSMS.split(',').map((phone) -> phone.trim()).filter((phone) -> phone.length > 0) else alertSMS
    sms =
      to: phoneNumbers
      from: '+19056142609'
      body: "#{errMsg} at #{os.hostname()} in #{batchFile}"
    sendSMS sms,(err)->
      if err then console.error err
      cb()
  
module.exports.exit = exit = (ecode,err)->
  if not err
    if (ecode is 0) or (not ecode?)
      process.exit 0
    else
      err = ecode
      ecode = 1
  if ecode > 0
    debug.critical 'exit with error',ecode
  else
    debug.info 'exit',ecode
  if err
    debug.critical err
    errorNotify err,->
      process.exit ecode
  else
    process.exit 0

globalCtx = null
MongoDb = null

module.exports.getMongoDb = getMongoDb = (cb)->
  if MongoDb
    return cb null,MongoDb.Database,MongoDb
  # if cfg.dbs?.mongo4
  MongoDb = require './mongo4'
  # else
  #   MongoDb = require './mongo_db'
  MongoDb.setup cfg, (err,db)->
    if err then return exit 1,err
    debug.info 'MongoDb Connected.'
    globalCtx = buildContext()
    # NOTE:batch启动时生成/获取predefine collections
    if noMailLog
    # NOTE: 不保存mail log
      delete cfg.preDefColls?.mailLog
      delete cfg.preDefBatchColls?.mailLog
    MongoDb.set_predef_colls cfg.preDefColls,(err,ret)->
      return cb err if err
      MongoDb.set_predef_colls cfg.preDefBatchColls,(err,ret)->
        return cb err if err
        if preloadModel
          return loadModels path.join(srcPath,'model'),(err)->
            cb err, MongoDb.Database, MongoDb
        cb null,MongoDb.Database, MongoDb
  null

# load models
loadModels = (parentPath,cb)->
  fs.readdir parentPath,(err,files)->
    if err then return cb err
    if not files then return cb()
    files = files.sort()
    folders = []
    loadfolder = ->
      if f = folders.shift()
        fullPath = path.join parentPath,f
        self.loadModels fullPath,(err)->
          if err then return cb err
          process.nextTick loadfolder
      else
        cb()
    loadone = ->
      if f = files.shift()
        if f.match(/^\.|^\_|^unused|unused$|~$/)
          process.nextTick loadone
        else
          fullPath = path.join parentPath,f
          fs.stat fullPath,(err,stat)->
            if stat.isDirectory()
              folders.push f
            else if stat.isFile()
              includeLib fullPath, globalCtx,{}
            process.nextTick loadone
      else
        process.nextTick loadfolder
    loadone()



# include/require file
includeLib = (filename,ctx,origParams={})->
  # NOTE: deconstruct in params causes /src/lib/batchBase.coffee:165:13: error: unexpected =
  # monitor = false,
  #         ^
  # https://stackoverflow.com/questions/11493163/deconstructing-a-list-of-parameters-in-class-constructor-coffeescript
  { monitor, useRequire, isBatchFile, forceReload } = origParams
  monitor ?= false
  useRequire ?= false
  isBatchFile ?= false
  forceReload ?= false
  if ctx
    ctx = Object.assign {},globalCtx,ctx
  debug.debug 'includeLib',filename
  if m = filename?.match /^(lib|libapp|model)\./
    parentPath = path.join srcPath,m[1]
    filename = filename.substr(m[0].length)
  else if /^\.+\//.test filename
    callerFile = loader.caller(2)
    debug.debug 'caller',callerFile
    parentPath = path.dirname(callerFile) # use current caller
  else if isBatchFile
    parentPath =  srcPath
  else
    parentPath = path.dirname(filename)
  debug.debug 'loadfile',filename,parentPath
  if useRequire
    return require path.join parentPath,filename
  loader.get filename,parentPath,{ctx:ctx},monitor,forceReload

# monitor file with auto-reload
loadLib = (filename,ctx)->
  if not ctx
    ctx = globalCtx
  includeLib filename,ctx,{monitor:true}

globalDefinedObjects = {}
defGlobal = (key,obj)->
  if obj?
    return globalDefinedObjects[key] = obj
  else
    return globalDefinedObjects[key]

collection = (dbname,collectionname)->
  db = new MongoDb.Database()
  if not collectionname?
    throw new Error('Need DB name for ' + dbname)
  db.db(dbname).coll(collectionname)

getConfig:(name)->
  configDic =
    'pushNotify': ['apn','serverBase','fcmv1_config']
    'sendSMS': ['twilio']
    'sendMail': ['contact', 'mailEngine', 'mailEngineList']
    'sendCall': ['twilio']
    'translator': ['i18n','serverBase']
    'promoTo3pty': ['p58','sqldbs']
  
  combinedConfig = {}
  for section in configDic[name]
    if cfg[section]
      combinedConfig[section] = cfg[section]
  return combinedConfig


libFns = {}
service = (name)->
  return fn if fn = libFns[name]
  if 0 <= ['sendSMS','sendMail','sendCall','translator','promoTo3pty'].indexOf name
    lib = includeLib "lib.#{name}",{},{useRequire:true}
    name2 = name.substr(0,1).toUpperCase() + name.substr(1)
    return libFns[name] = lib['get' + name2] getConfig(name)
  null

useI18n = (cb = ->)->
  i18nCfg = cfg.i18n?.coll
  debug.debug i18nCfg
  coll = collection i18nCfg.db,i18nCfg.coll
  cityColl = null
  if cfg.i18n?.cityColl?
    cityCollCfg = cfg.i18n.cityColl
    cityColl = collection cityCollCfg.db, cityCollCfg.coll
  i18n = includeLib 'lib.i18n',globalCtx,{}
  developer_mode = cfg.serverBase?.developer_mode
  i18n.init {coll:coll,allowed:i18nCfg.allowed,developer_mode, cityColl},(err)->
    sysdataColl = collection 'chome', 'sysdata'
    abbr.init sysdataColl,cfg.abbr.file,(err)->
      if err then throw err
      abbreviator = abbr
      cb() if cb?

linkAppBase = (ctx)->
  debug.debug 'linkAppBase'
  for key in ['INCLUDE','MSG_STRINGS','DEBUG','DEF','MODEL','COLLECTION','CONFIG','SERVICE','GETTRANSACTION']
    if appBase.coffeemate.locals[key]
      ctx[key] = appBase.coffeemate.locals[key]
    else
      appBase.coffeemate.locals[key] = ctx[key]
  ctx

buildContext = ()->
  ctx = {}
  ctx.INCLUDE = (name)->
    if name is 'lib.debug'
      return debugHelper
    includeLib name,globalCtx,{}
  ctx.LOAD    = loadLib
  ctx.MSG_STRINGS = msgStrings.newInstance()
  ctx.DEBUG   = debugHelper.getDebugger
  ctx.DEF     = defGlobal
  if MongoDb?
    ctx.MODEL   = defGlobal
    ctx.COLLECTION  = collection
  ctx.CONFIG  = (sections)->
    if sections
      sections = [sections] unless Array.isArray(sections)
      combinedConfig = {}
      for section in sections
        if cfg[section]
          combinedConfig[section] = cfg[section]
      return combinedConfig
    else
      app_config = Object.assign {},cfg
      app_config.developer_mode = cfg.serverBase?.developer_mode or false
      app_config.chinaMode = cfg.serverBase?.chinaMode
      app_config.satelliteMode = cfg.serverBase?.satelliteMode
      app_config.masterMode = if cfg.serverBase?.satelliteMode then false else (cfg.serverBase?.masterMode or false)
      app_config.createRNIIndex = cfg.serverBase?.createRNIIndex
      return app_config
  ctx.EXIT    = exit
  ctx.AVGS    = avgs
  ctx.DRYRUN = dryRun
  ctx.NOGEO = noGeo
  ctx.AVGSLAST = avgsLast
  ctx.DEPEND  = dmodule.depend
  ctx.PROVIDE = dmodule.provide
  ctx.PLSWAIT = (name)-> debug.info 'PLSWAIT',name
  ctx.USEI18N = useI18n
  ctx.SERVICE = service
  # ctx.BASE    = base
  ctx.GETTRANSACTION = (dbname,transactionOptions)->
    db = new MongoDb.Database()
    return db.db(dbname).getTransaction(transactionOptions)
  ctx.process = process
  ctx.global  = global
  ctx.setInterval = setInterval
  ctx.clearInterval = clearInterval
  ctx.setTimeout = setTimeout
  ctx.clearTimeout = clearTimeout
  ctx.console = console
  ctx.Date    = Date
  ctx.Buffer  = Buffer
  ctx.dnld_dir = cfg.imageStore?.dnld_dir
  if batchFile
    ctx.BATCH_FULL_PATH = batchFile
    ctx.BATCH_FILE_NAME = path.basename(batchFile)
  linkAppBase ctx

parseAvgs = (_avgs)->
  avgs = _avgs
  module.exports.avgs = avgs
  module.exports.argv = avgs
  debug.debug 'parseAvgs',avgs
  for a in avgs
    unless batchFile
      batchFile = a
    avgsLast = a
    
    a = (''+a).toLowerCase()
    if a in ['dryrun','dry']
      dryRun = true
    if a is 'prod'
      isProd = true
    if a is 'nogeo'
      noGeo = true
    if /debug/.test a
      isDebug = true
      debugParams = avgsLast.substr(6).split(',')
    if a is 'preload-model'
      preloadModel = true
    if a is 'no-db'
      noDB = true
    if a is 'no-mail-log'
      noMailLog = true
  # name conflicts with global name
  module.exports.debug = isDebug
  module.exports.debugParams = debugParams
  module.exports.avgsLast = avgsLast
  module.exports.dryRun = dryRun
  module.exports.isProd = isProd
  module.exports.noGeo = noGeo

#filename, eg: watchAndImportProperties -t treb -t treb
exports.executeBatch = executeBatch = (args...)->
  debug.debug 'executeBatch',args
  if args.length > 0
    lastParam = args[args.length - 1]
    if 'function' is typeof lastParam
      exit = lastParam
      args.pop()
  batchFile = null
  parseAvgs args if args.length >= 1
  _setDebugger() # reset debugger module
  debug.info 'executeBatch run batch',batchFile
  if (not noDB) and cfg.dbs
    getMongoDb (err)->
      return exit 1,err if err
      _runBatch(exit)
  else
    _runBatch(exit)

# TODO: involke cb when done instead of settimeout
_runBatch = (cb)->
  globalCtx = buildContext()#unless globalCtx
  # debug.debug 'globalCtx',globalCtx
  module.exports.coffeemate = {locals:globalCtx}
  includeLib batchFile, globalCtx,{isBatchFile:true,forceReload:true},cb

#excute batch from command line
if require.main is module
  executeBatch.apply null,(process.env.BATCH_PARAMS or process.argv.slice(2))
  # below is another call method. both are working
  #`executeBatch(...(process.env.BATCH_PARAMS ||process.argv.slice(2)))`
