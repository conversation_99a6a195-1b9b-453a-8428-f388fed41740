twilio = require 'twilio'
helperNumber = require './helpers_number'
helperFunction = require './helpers_function'
shouldSendMessage = helperFunction.shouldSendMessage

module.exports.getSendSMS = (config)->
  unless config?.twilio?.sid and config.twilio.authToken
    throw new Error('No twilio configuration')
    return null
  client = twilio(config.twilio.sid,config.twilio.authToken)

  sendSMS = (p,cb)->
    obj = {}
    bodyField = p.Body or p.body or p.msg or p.text
    unless obj.body = bodyField
      return cb new Error("No 'Body' field")
    
    # Handle multiple phone numbers
    phoneNumbers = []
    toField = p.To or p.to
    if toField
      phoneNumbers = if Array.isArray(toField) then toField else [toField]
    else
      return cb new Error("No 'To' field.")
    
    # Validate and normalize all phone numbers
    validPhoneNumbers = []
    for phoneNumber in phoneNumbers
      unless helperNumber.isCanadaPhoneNumber phoneNumber
        console.error "SMS does not support this phone number: #{phoneNumber}"
        continue
      validPhoneNumbers.push helperNumber.normalizePhoneNumberCA phoneNumber
    
    if validPhoneNumbers.length is 0
      return cb new Error("No valid phone numbers provided")
    
    # obj.from = helperNumber.normalize_phone_number p.From or p.from or '+19056142609'
    obj.messagingServiceSid = config?.twilio?.messagingServiceSid \
      or 'MG7514da80e8250311d97d7677c734ca35'

    if obj.body.length > 160 then obj.body = obj.body.substr(0,160)
    
    # Send SMS to each valid phone number
    failedCount = 0
    
    for phoneNumber in validPhoneNumbers
      # Create a new object for each SMS to avoid reference issues
      smsObj = {
        body: obj.body
        messagingServiceSid: obj.messagingServiceSid
        to: phoneNumber
      }
      
      msgKey = phoneNumber + smsObj.body
      {shouldSend,msg} = shouldSendMessage(msgKey,smsObj.body)
      if not shouldSend
        continue
      else
        smsObj.body = msg
      
      try
        result = await client.messages.create(smsObj)
      catch error
        console.error 'Twilio message error', error, 'original message', smsObj
        failedCount++
    
    # Call callback after all SMS are sent
    if failedCount > 0
      cb(new Error("SMS sending completed with #{failedCount} failures out of #{validPhoneNumbers.length} recipients"))
    else
      cb(null)
  
  sendSMS
