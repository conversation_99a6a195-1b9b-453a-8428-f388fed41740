# not used
util = require 'util'
fs = require 'fs'
mongodb = require 'mongodb'
ObjectID = mongodb.ObjectID
f = require('util').format
MongoClient = require('mongodb').MongoClient
DModule = require './dmodule'
RESOURCE_NAME = '__mongo_db_resource'
puts = console.log
verbose = 0
servers = {}
all_ready = false
expected_connections = 0

MongoDb =
  setup: (opts,cb)->
    verbose = opts?.verbose? or verbose
    DModule.depend RESOURCE_NAME,(err,handler)->
      if cb? then cb err,handler
      null
    if opts?.dbs?
      n = 0
      if opts.dbs.verbose? then verbose = opts.dbs.verbose
      if verbose > 2 then console.log opts.dbs
      for key,opt of opts.dbs
        if key isnt 'verbose'
          expected_connections++
          conn = opt.host+':'+opt.port+'/'+opt.name
          if opt.replSet?
            repl = [opt.host+':'+opt.port]
            for rsKey,rs of opt.replSet
              repl.push ((rs?.host or opt.host or '127.0.0.1')+':'+(rs?.port or opt.port or 27017))
            conn = repl.join(',')+'/'+opt.name+'?replicaSet='+opt.rsName;
          if !opt.user && !opt.ssl
              conn = 'mongodb://'+conn
          if opt.user
            conn = 'mongodb://'+encodeURIComponent(opt.user)+':'+encodeURIComponent(opt.password)+'@'+conn

          # TODO: load ssl Files by async means
          if opt?.ssl
            conn = 'mongodb://' + conn if conn.indexOf('mongodb://') < 0
            conn =  conn + '?ssl=true'
            srvOpt = {ssl:opt.ssl,sslValidate:opt.sslValidate}
            # http://mongodb.github.io/node-mongodb-native/2.0/api/Server.html
            for oKey in ['sslCA','sslCert','sslKey']
              if oVal = opt[oKey]
                srvOpt[oKey] = fs.readFileSync oVal
          else
            srvOpt = {}
          if opt?.socketOptions
            srvOpt = Object.assign srvOpt, opt.socketOptions
          srvOpt ?= {connectTimeoutMS:300000,socketTimeoutMS:0}
          srvOpt.w = 1
          # if opt?.authdb
          #   srvOpt.authSource = opt.authdb
          if opt.readPrimary
            puts "Read Primary #{key}"
            srvOpt.readPreference = mongodb.ReadPreference.PRIMARY
          else
            srvOpt.readPreference = mongodb.ReadPreference.NEAREST
          sname = key
          do (sname,conn, srvOpt, opt)->
            process.nextTick ->
              if verbose then console.log "Mongo Openning #{sname}"
              console.log conn
              console.log srvOpt
              MongoClient.connect conn, srvOpt, (err,db)->
                console.error "Mongo DB open error #{err}" if err
                if not err?
                  expected_connections--
                  servers[sname] = db
                  if verbose then console.log "Mongo Ready: #{sname} => #{db.databaseName}"
                  check_ready null
                else
                  console.error "Mongo DB open error #{err}"
                  throw err unless opt.ignoreError
    RESOURCE_NAME
  get_db: (sname,cb)->
    if not sname? then sname = '_default'
    db = servers[sname]
    if cb?
      if db
        cb null,db
      else
        cb new Error "DB not found #{sname}"
    db
  ObjectID: ObjectID
  ObjectId: ObjectID

setup_default_db = ->
  if not servers['_default']
    key = Object.keys servers
    servers['_default'] = servers[key[0]]
    console.log "Default DB:#{key[0]}"

check_ready = (err)->
  if all_ready or all_ready is 0 then return null
  if err
    console.log "MongoDB Setup Error #{err}"
    DModule.provide RESOURCE_NAME,err
    all_ready = 0 # never ready
  if expected_connections <= 0
    all_ready = true
    if verbose then console.log "MongoDB Setup Done"
    setup_default_db()
    DModule.provide RESOURCE_NAME,null,MongoDb
  null


module.exports = MongoDb

class Database
  constructor: (@dbname,@collname)->
    @ObjectID = ObjectID
    @ReadPreferencePrimary = new mongodb.ReadPreference mongodb.ReadPreference.PRIMARY
    @ReadPreferenceNearest = new mongodb.ReadPreference mongodb.ReadPreference.NEAREST
  db: (@dbname)-> @
  coll: (@collname)-> @
  newDatabase: (db,coll)-> new Database db,coll
  get_db: (dbname,cb)-> MongoDb.get_db dbname,cb
  get_coll: (dbname,collname,callback)->
    if not callback?
      callback = collname
      collname = dbname
      dbname = @dbname
    if not callback?
      callback = collname
      collname = @collname
    console.log "#{dbname} #{collname}"
    MongoDb.get_db dbname,(err,db)->
      if err then return callback err
      db.collection collname,callback

  q_normalize_id: (f)->
    if f
      if f.$query then f = f.$query
      if fid = f._id
        if ('string' is typeof fid) and /^[a-f0-9]{24}$/.test fid
          f._id = new ObjectID fid
          if verbose > 1 then puts "Search ObjectID #{f._id}"
        else if 'object' is typeof fid and fid['$in']
          fin = fid['$in']
          fids = []
          for id in fin
            if ('string' is typeof id) and /^[a-f0-9]{24}$/.test id #id.length is 24
              fids.push new ObjectID id
            else
              #console.log "ID: #{id}"
              fids.push id
          fid['$in'] = fids
          if verbose > 1 then puts "Search ObjectIDs (#{fids.length})"
      if fid = f.uid
        if ('string' is typeof fid) and /^[a-f0-9]{24}$/.test fid
          f.uid = new ObjectID fid
          if verbose > 1 then puts "Search ObjectID #{f.uid}"

    f

  execute: (method,arg)->
    self = this
    cb = if arg and arg.length > 1 then arg[arg.length - 1] else null
    if 'function' isnt typeof cb # no callback is set. Log to global_logger
      cb = (err)-> logger.error err,"Mongo Db Access"
    self.get_db self.dbname,(err,db)->
      if err? then return cb err
      db.collection self.collname, (err,coll)->
        if err? then return cb err
        f = self.q_normalize_id arg[0]
        if verbose > 1 then puts "DB: #{self.dbname or 'DefaultDB'} #{self.collname} #{method} #{JSON.stringify(arg[0])}"
        switch method
          when 'insert','update','save'
            if method is 'update'
              obj = self.q_normalize_id arg[1]
            else
              obj = arg[0]
            if sch = db.schemas?[self.collname] # schema not supported in this one yet
              sch.validate obj,(err,newObj)->
                if err
                  return cb new Error "Invalide Record:#{self.collname}:#{method}: #{err}"
                else
                  if method is 'update' then arg[1] = newObj else arg[0] = newObj
                  if verbose then puts "Valid:#{self.collname}:#{method}"
          when 'insertAll'
            if sch = db.schemas?[self.collname] # schema not supported in this one yet
              all = arg[0]
              i = 0
              for a in all
                sch.validate o,(err,newObj)->
                  if err
                    return cb new "Invalide Record:#{self.collname}:#{method}:#{i}: #{err}"
                  else
                    all[i] = newObj
                    if verbose then puts "Valid:#{self.collname}:#{method}:#{i}"
                i++
        if verbose then puts "#{self.dbname} #{self.collname} #{method}: #{util.inspect arg}"
        if method is 'find' and arg.length > 0 and 'function' is typeof arg[arg.length - 1]
          cb = arg.pop()
          cur = coll[method].apply coll,arg
          cb(null,cur)
        else
          coll[method].apply coll,arg

  # query [,fields [,sort [,limit [,skip]]]],callback
  findToArray: ->
    self = this
    arg = [].slice.call(arguments)
    old_callback = arg.pop()
    if 'function' isnt typeof old_callback or old_callback.length isnt 2
      throw new Error "Last parameter for findToArray must be a function(err,data)"
    self.get_db self.dbname,(err,db)->
      if err then return old_callback err
      db.collection self.collname, (err,coll)->
        if err then return old_callback err
        #console.dir arg
        if arg.length is 1 and 'object' isnt typeof arg[0]
          old_callback(new Error 'Bad Request',null)
        arg[0] = self.q_normalize_id arg[0]
        #if verbose then puts "find: #{util.inspect arg}"
        if verbose then puts "DB: #{self.dbname or 'DefaultDB'} #{self.collname} findToArray #{JSON.stringify(arg)}"
        query = arg.shift()
        #if (arg.length > 0) and (fields = arg.shift()) # when has fields, and fields is not null
        #  res = coll['find'].apply(coll,[query,fields])
        #else
        #  res = coll['find'].call(coll,query)
        #res = coll.find query
        #res = res.sort arg.shift() if arg.length > 0
        #res = res.limit arg.shift() if arg.length > 0
        #res = res.skip arg.shift() if arg.length > 0
        if arg.length > 0 # mongo drive 2.0 take options for skip limit fields
          res = coll.find query,arg[0]
        else
          res = coll.find query
        res.toArray (err,a)->
          old_callback(err,a)


Database_method_setup = ->
  #ensureIndex|
  for name in 'insertOne|insert|insertAll|insertMany|save|update|updateOne|updateMany|use|distinct|count|createIndex|dropIndex|remove|deleteOne|findOneAndReplace|findOneAndUpdate|findOneAndDelete|find|findOne|sort|toArray|aggregate|group'.split '|'
    do (name)->
      Database.prototype[name] = ->
        arg = [].slice.call(arguments)
        this.execute name,arg
  Database.prototype.ensureIndex = (fields,opt,cb)->
    if not cb?
      cb = opt
      opt = null
    self = @
    @get_db self.dbname,(err,db)->
      if err? then return cb err
      db.collection self.collname, (err,coll)->
        if err? then return cb err
        mycb = (err)->
          if err
            console.error "ensureIndex error for #{self.collname}"
            console.error fields
            console.error opt
            cb err
          else
            cb()
        if opt?
          coll.ensureIndex fields,opt,mycb
        else
          coll.ensureIndex fields,mycb
  null

Database_method_setup()

module.exports.Database = Database
