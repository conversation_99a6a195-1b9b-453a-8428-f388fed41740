###
  Logger:
    level:
      debug (DBG)(msg) :
      info  (INF)(msg) : log to info
      warn  (WRN)(msg) : log to error file too
      error (ERR)(err) : log to error file too
      critical (CRT)(err) : send sms & email for each kind
      access (ACC)(request-object,[lasting ms]):
        log to access file
      notfound (NOF)(request-object) :
        classified by 'lpathname', collect domain,count,referral.
        log to notfound file & write out to a classified file periodically
    Category:
      Global: write to real logger, with not domain, critical error in most case
      Request: attached to request, but only initialized when a warn or error
               is wroten.
    Secondary Functionality:
      - Timeout & report long running request
      - Blockout Attak IPs *
    Writeout:
      - 1 files
      - 2 databases
      - 3 console
      - 4 email *
      - 5 SMS *
    Files:
      access_log     : access log. file,console.
      notfound_log   : not found log. file,console,db.
      notfound_dump  : not found list memory dump. file.
      error_log      : for warn/error/critical log. file,console,db.
      info_log       : for all info/debug log. file,console.
    Configuration:
      path: '/folder_name/' (file output location, or false to suppress file output)
      console: true/false (false : don't output to console, unless file output
                            failed, or database write error)
      mongodb:
        db: 'database-name'
        error: 'error_collection'
        notfound: 'notfound_collection'
      log_depth: 20
      buffer:
        duration: 1000 (ms for internal buffering)
        size: 100 (buffer size, when over this limit will write out)
      requests
        timeout: 100 (s)
        limit: 10
        interval: 10000 (ms)
  * Future project
###

LVL_DBG = 1
LVL_INF = 2
LVL_WRN = 3
LVL_ERR = 4
LVL_CRT = 10

DEFAULT_DATETIME_FORMAT = "yyyy-mm-dd'T'HH:MM:ss.l"
DEFAULT_BUFFER_DURATION = 1000 # ms
DEFAULT_BUFFER_SIZE = 100 # records
DEFAULT_LOG_DEPTH = 20
DEFAULT_REQUEST_TIMEOUT = 100 * 1000 # ms
DEFAULT_REQUEST_WARN_LIMIT = 10 # when limit excessed, output a warning
DEFAULT_REQUEST_CHECK_INTERVAL = 10 * 1000 # ms
util = require 'util'
helpers = require './helpers'
require './date'
puts = console.log

puts "Logger Start: #{(new Date()).format('yyyy-mm-dd HH:MM')} (bug fix for Date for unknown reason.)"

verbose = 0
NOTFOUND_AS_ERROR = false
buf = []
requests = {}
whenfinished = null
request_timeout = DEFAULT_REQUEST_TIMEOUT
request_check_interval = DEFAULT_REQUEST_CHECK_INTERVAL
request_warn_limit = DEFAULT_REQUEST_CHECK_INTERVAL
request_output_level = LVL_WRN
fmt = null
fmt_start = null
buffer_size = DEFAULT_BUFFER_SIZE
buffer_duration = DEFAULT_BUFFER_DURATION
buffer_last_time = +new Date
to_console = true
f_access = null
f_error = null
f_log = null
f_notfound = null
f_notfound_dump = null
f_formInput = null

b_access = []
b_error = []
b_log = []
b_notfound = []
b_formInput = []

notfounds = {}

#proxy_remote_addr_header = 'X-Forwarded-For'
proxy_header = null #'X-Real-IP'



total_requests = 0
total_response_time = 0
total_not_found = 0
total_timeover = 0

reset_profile = ->
  total_requests = 0
  total_response_time = 0
  total_not_found = 0
  total_timeover = 0


# output functions
# file > db > console
# delete buffer
op_access = -> # f/c
  if b_access.length > 0
    ss = []
    for b in b_access
      ss.push (if b.f then '-' else '+') + b.s + '\n'
    s = ss.join('')
    if f_access
      f_access.write s
    if to_console
      console.log s
    b_access = []

op_log = -> # f/c
  if b_log.length > 0
    ss = []
    for b in b_log
      if 'string' is typeof b
        s = b
      else
        s = "#{b.t.format(DEFAULT_DATETIME_FORMAT)} Site(#{b.s}) Domain(#{b.d}) URL(#{b.u}) Type(#{b.tp}) Key(#{b.k}) Msg:[#{b.m}]\n"
        if b.st or b.e
          s += "Stack:#{b.st}\nExtra:#{b.e}\n"
      ss.push s
    s = ss.join(' ')
    if f_log
      f_log.write s
    if to_console # output to console anyway, if not to file
      console.log s
    b_log = []

op_error = -> # f/d/c
  if b_error.length > 0
    ss = []
    for b in b_error
      if 'string' is typeof b
        s = b
      else
        s = "#{b.t.format(DEFAULT_DATETIME_FORMAT)} Site(#{b.s}) Domain(#{b.d}) URL(#{b.u}) Type(#{b.tp}) Key(#{b.k})\nMsg:\n#{b.m}"
        if b.st or b.e
          s += "\nStack:#{b.st}\nExtra:#{b.e}"
      ss.push s
    s = ss.join(' ')
    if f_error
      f_error.write s
    if to_console # output to console anyway, if not to file
      console.error s
    # TODO: output to database
    b_error = []


op_notfound = -> # f/d/c
  if b_notfound.length > 0
    ss = []
    for b in b_notfound
      s = "#{b.u} @ #{b.t.format(DEFAULT_DATETIME_FORMAT)} REF(#{b.r})"
      # Categorize
      nf = (notfounds[b.u] or= {})                      # sort by URL
      nfr = (nf[b.r] or= {count:0,first:{t:b.t,h:b.h}}) # further sort by referral
      nfr.last = {t:b.t,h:b.h}
      nfr.count++
      ss.push ("\n" + s)
    s = ss.join('')
    if f_notfound
      f_notfound.write s
    if to_console # output to console anyway, if not to file
      console.error "NOT FOUND:"
      console.error s
      console.error "."
    b_notfound = []
  # TODO: output to database


op_notfound_long = -> # f/d/c
  if b_notfound.length > 0
    ss = []
    for b in b_notfound
      s = "#{b.u} @ #{b.t.format(DEFAULT_DATETIME_FORMAT)} REF(#{b.r})\n Headers:\n" + util.inspect(b.h) + '\n'
      # Categorize
      nf = (notfounds[b.u] or= {})                      # sort by URL
      nfr = (nf[b.r] or= {count:0,first:{t:b.t,h:b.h}}) # further sort by referral
      nfr.last = {t:b.t,h:b.h}
      nfr.count++
      ss.push s
    s = ss.join('')
    if f_notfound
      f_notfound.write s
    if to_console # output to console anyway, if not to file
      console.error "NOT FOUND:"
      console.error s
      console.error "END NOT FOUND."
    b_notfound = []
  # TODO: output to database

op_notfound_dump = -> # f2/d/c
  if Object.keys(notfounds).length > 0
    ss = []
    sort = []
    total = 0
    for url,nf of notfounds
      sort2 = []
      total2 = 0
      for ref,nfr of nf
        nfr.ref = ref
        sort2.push nfr
        total2 += nfr.count
      sort2.sort (a,b)-> b.count - a.count # bigger first
      nf.sorted = sort2
      nf.url = url
      nf.count = total2
      sort.push nf
      total += total2
    sort.sort (a,b)-> b.count - a.count

    s = (new Date()).format(DEFAULT_DATETIME_FORMAT)
    s = "NOT FOUNDS @ #{s} Total:#{total}"
    ss.push s
    for nf in sort
      ss.push "URL:#{nf.url} (#{nf.count})"
      for nfr in nf.sorted
        ss.push "  REF:#{nfr.ref} (#{nfr.count})"
        ss.push "    First:#{util.inspect nfr.first}"
        if nfr.count > 1
          ss.push "    Last:#{util.inspect nfr.last}"
    s = ss.join('\n')
    if f_notfound_dump
      f_notfound_dump.write "\n======= Not Found Dump for " + (new Date()).format(DEFAULT_DATETIME_FORMAT) + " =======\n" + s
    if to_console # output to console anyway, if not to file
      console.error "vvvvv NOT FOUND vvvvv"
      console.error s
      console.error "^^^^^ NOT FOUND ^^^^^"

op_formInput = ->
  if b_formInput.length > 0
    ss = []
    for b in b_formInput
      if 'string' is typeof b
        s = b
      else
        s = "#{b.t.format(DEFAULT_DATETIME_FORMAT)} Site(#{b.s}) Domain(#{b.d}) URL(#{b.u}) Type(#{b.tp}) Key(#{b.k})\nMsg:\n#{b.m}"
        if b.st or b.e
          s += "\nStack:#{b.st}\nExtra:#{b.e}"
      ss.push s
    s = ss.join(' ')
    if f_formInput
      f_formInput.write s
    if to_console
      console.log s
    b_formInput = []

_addArgsToArray = (args,ary)->
  for s in args
    if 'object' is typeof s
      s = util.inspect(s)
    else if s
      s = s.toString()
    else
      s = 'undefined'
    ary.push s

consoleErrorHandler = (args...)->
  _addArgsToArray(args,b_error)
  op_error()

consoleWarnHandler = (args...)->
  for s in args
    b_error.push 'WARN:'+s.toString() if s
  op_error()

consoleLogHandler = (args...)->
  _addArgsToArray(args,b_log)


consoleFormInputHandler = (args...)->
  _addArgsToArray(args,b_formInput)
  op_formInput()

# make debug use these logging facilities
debug = require './debug'
debug.setErrorOutputHandler consoleErrorHandler
debug.setOutputHandler consoleLogHandler
debug.setFormInputOutputHandler consoleFormInputHandler

# log_xxx put log into buffer
log_access = (finished,str)->
  b_access.push {f:finished,s:str}
  if b_access.length >= buffer_size then op_access()

log_notfound = (req,time)->
  wurl = req.host + req.url
  referer = req.referer() or ''
  headers = helpers.deepCopyObject req.headers,0
  b_notfound.push {u:wurl, t:time, r: referer, h:headers}
  if b_notfound.length >= buffer_size then op_notfound()

log_log = (site,domain,url,type,lvl,time,keyword,msg,stack,extra)->
  l = {s:site,d:domain,u:url,tp:type,l:lvl,t:time,k:keyword,m:msg,st:stack,e:extra}
  if lvl >= LVL_ERR # When error output error immediately
    b_error.push l
    op_error()
    op_log()
  else
    b_log.push l
  if b_log.length > buffer_size
    op_log()

before_exit = ->
  if verbose then puts "Log before Exit"
  op_error()
  op_log()
  op_access()
  op_notfound()
  op_notfound_dump()
  op_formInput()

buffer_writeout = ->
  # error is outputed promptly
  op_access()
  op_log()
  op_notfound()

timeout_error = ->

# periodly check total requests
# periodly check timeout
check_request_timeout = ->
  if verbose > 3 then puts "LOG: check requests"
  nt = +new Date
  t = nt - request_timeout
  reqs = Object.keys requests
  if reqs.length > request_warn_limit
    global_logger.log "Requests Limit : #{reqs.length} > #{request_warn_limit}"
  reqs.forEach (key)->
    req = requests[key]
    if req.startTime < t
      delete requests[key]
      err = new Error "Request Timeout: #{nt - req.startTime} ms #{key}"
      if req.logger?
        req.logger.error err
      else
        global_logger.error err, 'Timeout'
      total_timeover++
      if not req.resp.headersSent
        req.resp.statusCode = 500
        req.resp.setHeader "Connection","close" unless req.resp.finished
        req.resp.end "Timeout Error"
        timeout_error req
    null

# Logger for each HTTP request
class Logger
  constructor: (@req,@resp)->
    @logs = []

  log: (msg,tp='INF',lvl=1)->
    if 'object' is typeof msg
      if msg instanceof Error
        @logs.push {type:tp,lvl:lvl,t:new Date(),s:msg.message,stack:msg.stack,extra:msg.extra}
      else
        @logs.push {type:tp,lvl:lvl,t:new Date(),s:util.inspect(msg)}
    else
      @logs.push {type:tp,lvl:lvl,t:new Date(),s:msg}
    if lvl >= request_output_level
      @_output()

  debug: (msg)-> @log msg,'DBG',LVL_DBG # level 1
  info: (msg)-> @log msg,'INF',LVL_INF # level 2
  warn: (msg)-> @log msg,'WRN',LVL_WRN
  error: (err)-> @log err,'ERR',LVL_ERR
  critical: (err)-> @log err, 'CRT', LVL_CRT

  notfound: (req)->
    total_not_found++
    log_notfound req,new Date()
    if NOTFOUND_AS_ERROR then @error new Error "Not Found"



  _output: ->
    msg = []
    lvl = LVL_DBG
    type = 'DBG'
    t = new Date()
    stack = null
    extra = extra
    for l in @logs
      if lvl < l.lvl
        lvl = l.lvl
        type = l.type
      msg.push "#{l.type}@#{l.t.format(DEFAULT_DATETIME_FORMAT)}:#{l.s}" + (l.extra or '')
      stack = l.stack if l.stack
      extra = l.extra if l.extra
    @logs = []
    log_log @req.site?.name, @req.hostname, @req.url, type, lvl, t, null, msg.join('\n'), stack, extra

# the logger for framework
req_logger =  (req,resp,next)->
  total_requests++
  writeHead = resp.writeHead
  end = resp.end
  url = req.url
  statusCode = undefined
  resHeaders = undefined

  req.startTime = +new Date
  if not req.ip?
    if proxy_header
      req.ip = req.headers[proxy_header] or req.socket.remoteAddress
    else
      req.ip = req.socket.remoteAddress
  rkey = req.request_key = req.ip + ':' + req.hostname + ":" + req.url +  '@' + req.startTime
  requests[rkey] = req # put this request into hosting

  # log start
  if fmt_start
    log_access false,format(fmt_start, req, resp)
  else
    log_access false, (
      (if proxy_header then req.headers[proxy_header] else req.socket?.remoteAddress)
      + ' - - [' + (new Date()).format(DEFAULT_DATETIME_FORMAT) + ']'
      + ' "' + req.method + ' ' + url
      + ' HTTP/' + req.httpVersionMajor + '.' + req.httpVersionMinor + '" '
      + ' "' + (req.headers['referer'] || req.headers['referrer'] || '')
      + '" "' + (req.headers['user-agent'] || '')  + '"')

  req.logger = resp.logger = new Logger req,resp
  resp.orgSetHeader = resp.setHeader
  resp.setHeader = (name,value)->
    if resp.finished
      console.log "resp.setHeader: header sent"
      return
    if name is 'Content-Length'
      resp._log_contentLength = value
    else if name is 'Content-Type'
      resp._log_contentType = value
    resp.orgSetHeader name,value

  # proxy for statusCode
  resp.writeHead = (code,headers)->
    resp.writeHead = writeHead
    resp.writeHead code,headers
    statusCode = code
    resHeaders = headers or {}

  # proxy end to output a line to the provided logger
  resp.end = (chunk,encoding)->
    delete requests[req.request_key]
    resp.end = end
    resp.end chunk,encoding
    resp.responseTime = +new Date - req.startTime
    total_response_time += resp.responseTime
    if not resp._log_contentLength
      resp._log_contentLength = resHeaders?['Content-Length'] or '-'
    if not resp._log_contentType
      resp._log_contentType = resHeaders?['Content-Type'] or '-'
    # log end
    if fmt
      log_access true, format(fmt,req,resp,resHeaders)
    else
      log_access true, (
        (req.socket?.remoteAddress)
        + ' - - [' + (new Date()).format(DEFAULT_DATETIME_FORMAT) + ']'
        + ' "' + req.method + ' ' + url
        + ' HTTP/' + req.httpVersionMajor + '.' + req.httpVersionMinor + '" '
        + statusCode + ' ' + (resHeaders?['Content-Length'] || '-')
        + ' "' + (req.headers['referer'] || req.headers['referrer'] || '')
        + '" "' + (req.headers['user-agent'] || '') + res.responseTime + '"')
    if whenfinished? then whenfinished()
  next()

format = (str, req, res,resHeaders)->
    str.replace(':start-time', req.startTime)
    .replace(':host', req.host)
    .replace(':url', req.url)
    .replace(':method', req.method)
    .replace(':status', res.statusCode)
    .replace(':response-time', res.responseTime)
    .replace(':date2', new Date(req.startTime).format(DEFAULT_DATETIME_FORMAT))
    .replace(':date', new Date(req.startTime).format(DEFAULT_DATETIME_FORMAT,true))
    .replace(':referrer', (req.headers['referer'] or req.headers['referrer'] or ''))
    .replace(':http-version', req.httpVersionMajor + '.' + req.httpVersionMinor)
    .replace(':remote-addr', req.ip )
    .replace(':user-agent', req.headers['user-agent'] or '')
    .replace(':content-length', res._log_contentLength || '-')
    .replace(':content-type', res._log_contentType || '-')
    #.replace(/:req\[([^\]]+)\]/g, (_, header)-> req.headers[header])
    #.replace(/:res\[([^\]]+)\]/g, resHeaders?[header])

Path = require 'path'
module.exports = (opt,coffeemate)->
  opt or= {}
  verbose = opt.verbose if opt.verbose?
  fmt = opt.format if opt.format?
  fmt_start = opt.sformat if opt.sformat?
  to_console = opt.console if opt.console?
  proxy_header = opt.proxy_header?.toLowerCase()
  if opt.path?
    p = opt.path + '/'
    fs = require 'fs'
    foption = {flags: 'a',encoding: null, mode: 0o0600}
    f_access = fs.createWriteStream (p+'access_log'),foption
    f_error = fs.createWriteStream (p+'error_log'),foption
    f_log = fs.createWriteStream (p+'info_log'),foption
    f_notfound = fs.createWriteStream (p+'notfound_log'),foption
    f_notfound_dump = fs.createWriteStream (p+'notfound_dump'),foption
    f_formInput = fs.createWriteStream (p+'formInput_log'),foption
    to_console = false
    # console.error = consoleErrorHandler
    # console.log = consoleLogHandler
    # console.warn = consoleWarnHandler
  else
    to_console = true
  if bufopts = opt.buffer
    buffer_size = bufopts.size if bufopts.size > 0
    buffer_duration = bufopts.duration if bufopts.duration > 0
  if reqopts = opt.requests
    request_check_interval = reqopts.interval if reqopts.interval > 0
    request_timeout = reqopts.timeout if reqopts.timeout > 0
    request_warn_limit = reqopts.limit if reqopts.limit > 0
    if reqopts.level
      switch reqopts.level.toLowerCase()
        when 'err','error'
          request_output_level = LVL_ERR
        when 'wrn','warn'
          request_output_level = LVL_WRN
        when 'info','inf'
          request_output_level = LVL_INF
        when 'debug','dbg'
          request_output_level = LVL_DBG
  if verbose
    puts "Logger Buffer Size: #{buffer_size}"
    puts "Logger Buffer Duration: #{buffer_duration} ms"
    puts "Requests Check Interval : #{request_check_interval} ms"
    puts "Requests Timeout : #{request_timeout} ms"
    puts "Requests Warning Limit: #{request_warn_limit}"
  if coffeemate
    coffeemate.on 'exit',before_exit
  else
    process.on 'exit',before_exit
  setInterval check_request_timeout,request_check_interval
  setInterval buffer_writeout, buffer_duration
  req_logger

# general usage logger
module.exports.logger = global_logger =
  log: (msg,keyword,type='INF',lvl=LVL_INF)->
    if msg instanceof Error
      log_log(null,null,null,type,lvl,new Date(),keyword,msg.message,msg.stack,msg.extra)
    else
      log_log(null,null,null,type,lvl,new Date(),keyword,msg)
  error: (err,keyword)-> @log err,keyword,'ERR',LVL_ERR
  critical: (err,keyword)-> @log err,keyword,'CRT',LVL_CRT

module.exports.profile = ->
  r = [total_requests,total_response_time,total_not_found,total_timeover]
  reset_profile()
  r

module.exports.whenfinished = (cb)->
  whenfinished = ->
    if Object.keys(requests).length is 0
      cb()

module.exports.timeout_handler = (cb)-> timeout_error = cb
