<template lang="pug">
div.wrapper(@click="showSchool(bnd)",data-sub="school detail", :class="{'margin':showCtrl, 'selected':bnd.selected}")
  div.info-wrapper
    div.namePart
      div.heading
        span.nm(:class="{'full':!(showSchInfo || showMarker)}") {{bnd.nm}}
      div.small
        span.addr(v-show="bnd.addr") {{bnd.addr}}{{bnd.city?', '+bnd.city:''}}{{bnd.prov?', '+bnd.prov:''}}
        //- div(v-show="!bnd.addr && bnd.campus")
        //-   | {{_('Campus')}}&nbsp;:
        //-   span.addr(v-for="campus in bnd.campus")
        //-     |{{campus.city}}
        //-     |{{bnd.campus.length>1?',':''}}

        span.dis(v-show="bnd.dis") {{bnd.dis}}km
          //- div(v-show="bnd.firank")
          //-   p {{bnd.firank}}
          //-   p {{_('Fraser')}}/{{bnd.fitotal}}
          //- //- div(v-show="bnd.eqaorate")
          //- //-   p {{bnd.eqaorate}}
          //- //-   p {{_('EQAO')}}
          //- div(v-show="bnd.grd || bnd.gf")
          //-   p(v-if="bnd.grd") {{bnd.grd}}
          //-   p(v-else="bnd.gf") {{bnd.gf}}-{{bnd.gt}}
          //-   p  {{_('Grades','school-grade')}}
        //- div(v-show="bnd.filast5rank")
        //-   | {{_('Last 5 years')}}: {{bnd.filast5rank}}
    div.actions(v-if='!inDetail')
      span(v-if='(dispVar.isAdmin || dispVar.isRealGroup) && bnd.canExchange && !showSchInfo && !showMarker')
        span.fa.sprite16-14.sprite16-9-5.rmlist
        p.small {{_('Full Report')}}
      span.fa.fa-map-marker(v-show="showMarker",@click.stop.prevent="viewBoundary()")
        //- div.word {{_('Map')}}
      span.fa.fa-rmclose(v-show='showSchInfo',@click.stop="close()")
  div.school
    span(v-for='tag,k of bnd.tags',:style='{color:tag.textColor,background:tag.color}')
      | {{tag.nm}}
    //- span.point(v-if="bnd.hot")
    //-   //- i.fa.fa-check-square-o(v-if="bnd.hgh")
    //-   //- i.fa.fa-square-o(v-show="!bnd.hgh")
    //-   | {{_('Popular','school')}}
    //- span.grade(v-if="bnd.ele")
    //-   //- i.fa.fa-check-square-o(v-if="bnd.ele")
    //-   //- i.fa.fa-square-o(v-show="!bnd.ele")
    //-   | {{_('Elementary','school')}}
    //- span.grade(v-if="bnd.mid")
    //-   //- i.fa.fa-check-square-o(v-if="bnd.mid")
    //-   //- i.fa.fa-square-o(v-show="!bnd.mid")
    //-   | {{_('Middle','school')}}
    //- span.grade(v-if="bnd.hgh")
    //-   //- i.fa.fa-check-square-o(v-if="bnd.hgh")
    //-   //- i.fa.fa-square-o(v-show="!bnd.hgh")
    //-   | {{_('Secondary','school')}}
    //- span.grade(v-if="bnd.tp == 'university'")
    //-   | {{_('University','school')}}
    //- span.grade(v-if="bnd.tp == 'college'")
    //-   | {{_('College','school')}}
    //- span.point(v-if="bnd.c ||bnd.catholic ")
    //-   //- img.img-sm(src="/img/sch_catholic.png")
    //-   | {{_('Catholic','school')}}
    //- span.cata(v-if="bnd.campus && (bnd.campus.length == 1)")
    //-   | 1 {{_(" Campus",'school')}}
    //- span.cata(v-if="bnd.campus && (bnd.campus.length > 1)")
    //-   | {{bnd.campus.length}} {{_(" Campuses",'school')}}
    //- span.cata(v-if="bnd.eng")
    //-   | {{_("English",'school')}}
    //- span.cata(v-if="bnd.boarding")
    //-   | {{_('Boarding','school')}}
    //- span.cata(v-if="bnd.admSSAT")
    //-   | {{_('Required SSAT','school')}}
    //- span.cata(v-if="bnd.admIntvw")
    //-   | {{_('Required Interview','school')}}
    //- span.cata(v-if="bnd.ef")
    //-   //- img.img-sm(src="/img/sch_ef.png")
    //-   | {{_('Extended French','school')}}
    //- span.cata(v-if="bnd.fi")
    //-   //- img.img-sm(src="/img/sch_fi.png")
    //-   | {{_('French Immersion Program','school')}}
    //- span.cata(v-if="bnd.pri")
    //-   | {{_('Private','school')}}
    //- span.cata(v-if="bnd.chtr")
    //-   | {{_('Charter','school')}}
    //- span.cata(v-if="bnd.sprt")
    //-   | {{_('Separate','school')}}
    //- span.cata(v-if="bnd.frcp")
    //-   | {{_('Francophone','school')}}
    //- span.cata(v-if="bnd.idp")
    //-   | {{_('Independent','school')}}
    //- span.cata(v-if="bnd.frna")
    //-   | {{_('First Nation','school')}}
    //- span.cata(v-if="bnd.ib")
    //-   | {{_('IB','school')}}
    //- span.cata(v-if="bnd.ap")
    //-   | {{_('AP','school')}}
    //- span.cata(v-if="bnd.art")
    //-   | {{_('ART','school')}}
    //- span.cata(v-if="bnd.gif")
    //-   | {{_('Gifted','school')}}
    //- span.cata(v-if="bnd.sport")
    //-   | {{_('Sport','school')}}
    //- span.cata(v-if="bnd.ptst")
    //-   | {{_('Protestant','school')}}
    //- span.cata(v-if="bnd.sex")
    //-   | {{_(bnd.sex,'school')}}
      //- | {{_('French Immersion','school')}}
  div.small(v-show='bnd.keyFacts && bnd.keyFacts.length')
    div.rank(:class="{'pri':bnd.private}")
      div(v-for="(v,k) of bnd.keyFacts")
        p 
          span.bold {{v.val}}
          span(style='color:#999;font-size:12px',v-if='v.valTotal') /{{v.valTotal}}
            span.fa.size11(:class="{'fa-long-arrow-down':v.diffRank>0,'fa-long-arrow-up':v.diffRank<0}")
          template(v-if="v.isStyle2 && v.rating")
            span &nbsp;|&nbsp;
            span.bold {{v.rating}}

        p {{v.key}}{{v.grade ? '/' + v.grade : ''}}
          span.fa.fa-question-circle-o(v-if="v.alert",@click.stop="alertExplain(v.alert)")

  //- div.cata(style="font-size:11px;")
  div.controls(v-show="showCtrl&& bnd.tp!='university' && bnd.tp!='college'")
    div.ele(@click.stop.prevent="showProps('sale')")
      //- i.fa.fa-list-ul
      span {{_('SALE','property search')}}
    //- div.split
    div.ele.rental(@click.stop.prevent="showProps('lease')")
      //- i.fa.fa-list-ul
      span {{_('RENT','property search')}}
    //- div.split
    //- div.ele.detail(@click.stop.prevent="showSchool(bnd)")
    //-   i.fa.fa-info-circle
    //-   span {{_('Detail')}}
  div(style="display:none")
    span(v-for="(v,k) of strings") {{_(v.key, v.ctx)}}
</template>

<script>

export default {
  props:{
    dispVar:{
      type:Object,
      default:function () {
        return {
          isAdmin:false,
          isRealGroup:false,
          sessionUser:{}
        }
      }
    },
    channel:{
      type:String,
        default:'school-changed'
    },
    showMarker:{
      type:Boolean,
      default:false,
    },
    showCtrl:{
      type:Boolean,
      default:false
    },
    bnd:{
      type:Object,
      default:function () {
        return {}
      }
    },
    inDetail:{
      type:Boolean,
      default:false
    },
    showSchInfo:{
      type:Boolean,
      default:false
    },
    type:{
      type:String,
      default:'public'
    },
  },
  data () {
    return {
      strings:{
        sex:{key:"Gender", ctx:''},
        tuitn:{key:"Tuition", ctx:''},
        tuitnBoarding:{key:"Boarding Tuition", ctx:''},
        religion:{key:"Religion", ctx:''},
        grd:{key:"Grade", ctx:'school'},
        fndd:{key:"Founded", ctx:''},
        rating:{key:"Rating", ctx:''},
        fraser:{key:"Fraser Ranking", ctx:''},
        noResult:{key:"No Result", ctx:''},
        na:{key:"N/A", ctx:''},
        eqao:{key:"EQAO", ctx:''},
        aiRating:{key:"AI Rating", ctx:''},
        aiRank:{key:"AI Ranking", ctx:''},
        AI:["RealMaster uses Al to estimate a school's rating and ranking. This is a reference point only. Contact an agent for better insight into a full report of the school."],
        ALERT:['AI Rating & Ranking'],
      }
    };
  },
  mounted () {
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
  },
  methods: {
    close(){
      window.bus.$emit('close-school-info')
    },
    viewBoundary(){
      window.bus.$emit('view-boundary',this.bnd);
    },
    showSchool (bnd) {
      if (this.inDetail == true){
        return;
      }
      let url ='/1.5/school/public/detail?id='+bnd._id+'&redirect=1'
      if (bnd.private) {
        url ='/1.5/school/private/detail/'+bnd._id+'?redirect=1'
      } else if (bnd.tp=='college'||bnd.tp=='university') {
        if((bnd._id).indexOf('#')>-1){
          var id = bnd._id.split("#")
          url ='/1.5/school/university/detail/'+id[0]
        }else{
          url ='/1.5/school/university/detail/'+bnd._id
        }
      }
      if (vars.share) {
        url+='&share=1'
      }
      if (vars.bar) {
        url+='&bar=1'
      }
      var isEmbed = location.pathname.indexOf('embed') > -1;
      if (isEmbed) {
        return window.bus.$emit(this.channel?this.channel:'school-changed', bnd);
      }
      // alert(JSON.stringify(this.dispVar))
      if((this.dispVar.isApp || isEmbed)&& this.dispVar.sessionUser._id) {
        var cfg = {hide:false, title:this._('School')}
        RMSrv.getPageContent(url, '#callBackString', cfg, function(val) {
          try {
            if (/^cmd-redirect:/.test(val)) {
              var url = val.split('cmd-redirect:')[1];
              RMSrv.closeAndRedirectRoot(url);
              // return window.location = url;
            }
          } catch (e) {
            console.error(e);
          }
        });
      } else {
        // show school when not in prop, except houssmax
        // url = '/app-download'
        window.document.location.href = url;
      }
      // window.bus.$emit(this.channel?this.channel:'school-changed', bnd);
    },
    showProps(type){
      window.bus.$emit('school-prop',{sch:this.bnd, type:type})
    },
    alertExplain(key){
      RMSrv.dialogAlert(this._(this.strings[key][0]),this._(this.strings.ALERT[0]))
    }
  }
}
</script>


<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.fa-long-arrow-up{
  padding:0 3px;
  color:#2fa800;
}
.fa-long-arrow-down{
  padding:0 3px;
  color:#E03131;
}
.wrapper.margin{
  margin-bottom: 5px;
}
div.wrapper{
  background: white;
  padding: 0 0 10px;
  margin: 0;
  width: 100%;
  cursor: pointer;
}
.info-wrapper{
  padding: 10px 0 10px 15px;
}
.namePart{
  display: inline-block;
  width: calc(100% - 80px);
}
.actions{
  display: inline-block;
  width: 80px;
  text-align: center;
  /* padding-right: 10px; */
  vertical-align: top;
}
.heading .nm{
  font-size: 17px;
  font-weight: bold;
  display: inline-block;
  /* align-items: center;
  display: flex;
  overflow: hidden; */
}
.small{
  font-size: 11px;
  color:#666;
  line-height: 16px;
}
.small.rank{
  padding-bottom: 7px;
}
.small.rank .padding{
  padding-left: 10px;
}
.small .dis{
  color: #F0951C;
}
.small .addr{
  margin-right:10px;
}
.rank{
  display: flex;
  overflow-x: scroll;
  flex-wrap: nowrap;
  justify-content: space-between;
  padding: 5px 15px 0;
}
.rankDiv {
  flex: 1;
  width: 25%;
}
.rank > div {
  flex: 1;
  width: 43%;
  min-width: 43%;
}
.rank > div:last-child {
  flex: 0;
  width: 14%;
  min-width: 14%;
}
.rank.pri > div {
  flex: 1;
  width: 40%;
  min-width: 40%;
}
.rank.pri > div:last-child {
  flex: 0;
  width: 20%;
  min-width: 20%;
}
.rank > div p{
  font-size: 17px;
  color: #000;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0;
  line-height: 24px;
}
.rank > div p:last-child{
  color: #6f6f6f;
  font-size: 12px;
  line-height: 14px;
}
.school{
  font-size:11px;
  border-right: 15px solid transparent;
  display: flex;
  flex-shrink: 1;
  overflow: auto;
  padding: 0 15px 10px 15px;
  justify-content: flex-start;
}
.img-sm{
  height: 22px;
  width: 22px;
  vertical-align: bottom;
}
.school > span{
  border-radius: 1px;
  white-space: nowrap;
  padding: 0px 7px;
  font-size: 12px;
  margin: 1px 4px 1px 0;
}
.school > span:not(:first-child){
  /*margin-left: 5px;*/
}
.school .grade{
  color: #40BC93;
  background: #E9FAE3;
}
.school .cata{
  color: #2B8EEC;
  background: #D4DFF5;
}
.school .point{
  color: #E03131;
  background: #FFEEE7;
}
.actions .fa{
  font-size: 19px;
  position: absolute;
  top: 3px;
  padding: 10px;
  right: 3px;
  color: #b5b5b5;
}
.actions .rmlist{
  font-size: 16px;
  padding: 4px 8px 4px 8px;
  position: inherit;
  color: #428bca;
}
.actions .fa:hover{
  /* border: 1px solid #e03131; */
  border-radius: 3px;
  /* background: white; */
}
.actions .pull-right{
  /* text-align: center;
  margin-top: -5px; */
  position: absolute;
  top: 10px;
  right: 10px;
}
.actions .pull-right .word{
  font-size: 11px;
  line-height: 11px;
  color: #666;
  margin-top: -4px;
}
.controls{
  color: #3B7DEE;
  padding: 0 15px;
  /* text-align: center;
  border-top: 1px solid #f0eeee; */
}
.controls > div{
  padding: 15px 20px 0 0;
  display: inline-block;
  font-size: 15px;
  font-weight: bold;
}
.controls > .split{
  height: 100%;
  width: 1px;
  padding: 0;
  display: inline;
  border-left: 1px solid #f0eeee;
}
.controls .fa{
  font-size: 13px;
  padding-right: 4px;
}
.actions .fa-map-marker {
  color: #e03131;
}
.fa-question-circle-o{
  margin: 0px 5px 0px 5px;
  vertical-align: text-bottom;
  font-size: 14px;
  color: #777;
}
.bold{
  font-weight: bold;
}
.size11{
  font-size: 11px;
}
</style>
