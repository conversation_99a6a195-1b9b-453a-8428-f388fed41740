###
*  Description:    fetch Cornerstone listings from Bridge RESO API
*  New config:     ./start.sh -t batch -n carDownload -cmd 'lib/batchBase.coffee mlsImport/cornerstone/carDownload.coffee -t token'
*  Author:         <PERSON>, <PERSON>, Maggie
*  Run duration:   forever
*  Run frequency:  forever
# note:
1.[historial data] When it's the first time to run this script, or it run after 30 days,
  it will fetch all the data in initialQueryBack() using replication API.
  merge openHouse,propertyRoom,propertyUnitType into properties every 2000 properties
2.[routine] After the initial query, it will fetch the data using getResourceAsync API every 15 minutes,
  if the fetch data is more than 10000, it will record the last fetch timestamp and
  fetch the data again.
###
debug = DEBUG()
config = CONFIG(['car'])
{ getHash } = require '../../lib/helpers_string'
{ sleep } = require '../../lib/helpers_function'
{ updateResourceRequestLog, getLastResourceRequestInfo, ignorableErrorReso, processPropertyMedia } = require '../../libapp/impCommon'
filterEmptyFieldsNSaveStats = require('../../libapp/impCommon').getEmptyFilterNStatsFunction()
{ deepCopyObject, isEmpty } = require '../../lib/helpers_object'
SpeedMeter = require('../../lib/speed')
{ createBridgeResoClient } = require('../../libapp/reso')
{
  processKillSignal,
  RUNNING_NORMAL,
  RUNNING_ERROR,
  RUNNING_WARNING,
  getProcessHandler,
  HAS_RUNNING_PROCESS
} = INCLUDE('libapp.processHelper')
yargs = require('yargs')(AVGS)
yargs.option('token', { alias: 't', description: 'Bridge API token', required: false})
ONE_YEAR_SECONDS = 365*24*3600
THIRTY_DAYS_SECONDS = 30*24*3600
ProcessStatusCol = COLLECTION 'vow','processStatus'
MlsImportKeysCol = COLLECTION 'rni','mls_import_keys'
CarRawCol = COLLECTION 'rni','mls_car_raw_records'
CarRawCol.createIndex { mt: 1 }, { background: true }
CarRawCol.createIndex { _mt: 1 }, { background: true }
CarRawCol.createIndex { ListingKeyNumeric: 1 }, { background: true }
CarMasterCol = COLLECTION 'rni','mls_car_master_records'
CarMasterCol.createIndex { mt: 1 }, { background: true }
CarMasterCol.createIndex { _mt: 1 }, { background: true }
CarMasterCol.createIndex { ListingKeyNumeric: 1 }, { background: true }
CarLogCol = COLLECTION 'rni','mls_car_logs'
CarLogCol.createIndex { id:1, _mt:-1 }, { background:true }
CarLogCol.createIndex { id:1, hash:-1 }, { background:true }
CarLogCol.createIndex { sid:1, _mt:-1 }, { background:true }
CarLogCol.createIndex { _mt:-1 }, { expireAfterSeconds:ONE_YEAR_SECONDS, background:true }
CarOpenHouseCol = COLLECTION 'rni','mls_car_openhouses'
CarOpenHouseCol.createIndex { _mt: -1 }, { background:true }
CarOpenHouseCol.createIndex { ListingId: 1, OpenHouseStartTime: 1 }, { background:true }
CarPropertyRoomCol = COLLECTION 'rni','mls_car_property_rooms'
CarPropertyRoomCol.createIndex { _mt: -1 }, { background:true }
CarPropertyRoomCol.createIndex { ListingId: 1, BridgeModificationTimestamp: 1 }, { background:true }
CarMemberCol = COLLECTION 'rni','mls_car_members'
CarMemberCol.createIndex { _mt: -1 }, { background:true }
CarMemberCol.createIndex { ListingId: 1, BridgeModificationTimestamp: 1 }, { background:true }
CarOfficeCol = COLLECTION 'rni','mls_car_offices'
CarOfficeCol.createIndex { _mt: -1 }, { background:true }
CarOfficeCol.createIndex { ListingId: 1, BridgeModificationTimestamp: 1 }, { background:true }
CarPropertyUnitTypeCol = COLLECTION 'rni','mls_car_property_unit_types'
CarPropertyUnitTypeCol.createIndex { _mt: -1 }, { background:true }
CarPropertyUnitTypeCol.createIndex { ListingId: 1, BridgeModificationTimestamp: 1 }, { background:true }
CarPropertyGreenVerificationsCol = COLLECTION 'rni','mls_car_property_green_verifications'
CarPropertyGreenVerificationsCol.createIndex { _mt: -1 }, { background:true }
CarPropertyGreenVerificationsCol.createIndex { ListingId: 1, BridgeModificationTimestamp: 1 }, { background:true }
CarPropertyCommercialDoorCol = COLLECTION 'rni','mls_car_property_commercial_doors'
CarPropertyCommercialDoorCol.createIndex { _mt: -1 }, { background:true }
CarPropertyCommercialDoorCol.createIndex { ListingId: 1, BridgeModificationTimestamp: 1 }, { background:true }
CarPropertyAuxiliaryBuildingCol = COLLECTION 'rni','mls_car_property_auxiliary_buildings'
CarPropertyAuxiliaryBuildingCol.createIndex { _mt: -1 }, { background:true }
CarPropertyAuxiliaryBuildingCol.createIndex { ListingId: 1, BridgeModificationTimestamp: 1 }, { background:true }
CarPhotoQueue = COLLECTION 'rni','mls_car_photo_queue'
CarPhotoQueue.createIndex {priority:-1},{background:true}
CarPhotoQueue.createIndex {dlShallEndTs:-1},{background:true}
FieldStatsCol = COLLECTION 'rni','mls_field_stats'
CarDupLog = COLLECTION 'rni','mls_car_dup_log'
CarDupLog.createIndex { _mt:-1 }, { expireAfterSeconds:ONE_YEAR_SECONDS, background:true }

BRIDGE_API_TOKEN = yargs.argv.token
PROCESS_STATUS_ID = 'CAR_PropertyDownload'
FIELD_STATS_ID = 'CAR'
AVG_RUNNING_MS = 5 * 60 * 1000 # 5 minutes
SLEEP_MS = 15 * 60 * 1000 # 15 minutes
REPLICATION_MODE_THRESHOLD = 30 * 24 * 60 * 60 * 1000 # 30 days
OPEN_HOUSE_THRESHOLD = 30 * 24 * 60 * 60 * 1000 # 30 days
FETCH_TIMEOUT = 10 *60 * 1000 # 10 minutes
DEFAULT_QUERY_TIMESTAMP = '2022-06-01T00:00:00Z' # oldest 2022-06-06T18:51:17.285Z
# DEFAULT_QUERY_TIMESTAMP = '2024-11-21T01:00:00Z' # for testing
PREFIX = 'CAR'
DOWNLOAD_NM = 'CarDownload'
MLS_ID_FIELD = 'ListingId'
LAST_MT_FIELD = 'BridgeModificationTimestamp'
RESOURCE_NAME_PROPERTY = 'property'
RESOURCE_TO_BE_MERGED = ['openHouse','propertyRoom','propertyUnitType']
RESOURCE_NO_MERGE=['member','office','propertyGreenVerification','propertyCommercialDoor','propertyAuxiliaryBuilding']
MAX_FETCH_COUNT = 10000
GET_RESOURCE_MAX_RETRIES = 3
ACTIVE_PROP_CHECK_MAX_RETRIES = 3
GET_RESOURCE_WAIT_TIME = 5 * 60 * 1000 # 5 minutes
ACTIVE_PROP_CHECK_HOUR = 2 # Run at 2 AM
ACTIVE_PROP_CHECK_INTERVAL = 24 * 60 * 60 * 1000 # 24 hours in milliseconds
DB_BATCH_SIZE = 100 # Process 100 properties at a time for active property sync
currentLastListingId = null # Global variable to track last processed ListingId
DCNT_THRESHOLD = 3

openHouseSet = new Set()
propertyRoomSet = new Set()
propertyUnitTypeSet = new Set()


RESOURCE_SET_MAPPING = {
  openHouse: openHouseSet
  propertyRoom: propertyRoomSet
  propertyUnitType: propertyUnitTypeSet
}

RESOURCE_MAPPING={
  property:{typeName:'PROPERTY'},
  openHouse:{col:CarOpenHouseCol,resoKey:'OpenHouseKey',typeName:'OPEN_HOUSE'},
  propertyRoom: {col:CarPropertyRoomCol,resoKey:'RoomKey',typeName:'PROPERTY_ROOM'},
  member: {col:CarMemberCol,resoKey:'MemberKey',typeName:'MEMBER'},
  office: {col:CarOfficeCol,resoKey:'OfficeKey',typeName:'OFFICE'},
  propertyUnitType: {col:CarPropertyUnitTypeCol,resoKey:'UnitTypeKey',typeName:'PROPERTY_UNIT_TYPE'},
  propertyGreenVerification: {col:CarPropertyGreenVerificationsCol,resoKey:'GreenBuildingVerificationKey',typeName:'PROPERTY_GREEN_VERIFICATION'},
  propertyCommercialDoor: {col:CarPropertyCommercialDoorCol,resoKey:'PropertyCommercialDoorsKey',typeName:'PROPERTY_COMMERCIAL_DOOR'},
  propertyAuxiliaryBuilding: {col:CarPropertyAuxiliaryBuildingCol,resoKey:'PropertyAuxiliaryBuildingKey',typeName:'PROPERTY_AUXILIARY_BUILDING'},
}


{
  handleProcessStatus,
  handleExit,
  checkRunningProcess
} = getProcessHandler {
  ProcessStatusCol,
  id: PROCESS_STATUS_ID,
  filePath: BATCH_FILE_NAME,
  exitFn: EXIT
}
processKillSignal PROCESS_STATUS_ID, handleExit

if not BRIDGE_API_TOKEN
  if config.car?
    BRIDGE_API_TOKEN = config.car.token
  if not BRIDGE_API_TOKEN
    throw new Error('Please provide the Bridge API token using -t or in the config file.')
BridgeResoClient =  createBridgeResoClient({ dataset: 'itso', token: BRIDGE_API_TOKEN })
fieldNullCounts = {}
savePropertyCounts = 0
speedMeter = SpeedMeter.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}


saveProperty = (property)->
  # get hash
  propertyCopy = Object.assign {},property
  delete propertyCopy['BridgeModificationTimestamp']
  hash = getHash(JSON.stringify(propertyCopy))
  # Check if the hash matches the last record
  lastLog = await CarLogCol.findOne({id: property.ListingKeyNumeric},
    {projection: {hash: 1,BridgeModificationTimestamp:1}, sort: {_mt: -1}})
  # if hash is the same as the last log, only update and directly return 0, won't save to the log
  if (hash is lastLog?.hash)
    speedMeter.check {sameHash:1}
    if (lastLog.BridgeModificationTimestamp?) and (property.BridgeModificationTimestamp?) and (lastLog.BridgeModificationTimestamp isnt property.BridgeModificationTimestamp)
      debug.info "same hash #{hash} as the last log, only updating the BridgeModificationTimestamp", property.BridgeModificationTimestamp
      await CarLogCol.updateOne {_id:lastLog._id},{$set:{BridgeModificationTimestamp:property.BridgeModificationTimestamp}}
    await CarDupLog.insertOne {
      data:property,
      id:property.ListingKeyNumeric,
      hash:hash,
      BridgeModificationTimestamp:property.BridgeModificationTimestamp
    }
    return 0
  # If the hash is different, save it to the log
  mt = new Date(property[LAST_MT_FIELD])
  insertRet = await CarLogCol.insertOne {
    id:property.ListingKeyNumeric,
    hash:hash,
    sid: property[MLS_ID_FIELD],
    data:property,
    mt: mt,
    BridgeModificationTimestamp:property.BridgeModificationTimestamp
  }
  speedMeter.check {saveLog:1}

  property.mt = mt
  # await CarRawCol.updateOne { _id: PREFIX + property.ListingId },
  #   { $set: propertyCopy, $setOnInsert: { ts: new Date() }},
  #   { upsert: true }
  # note: use replaceOne to ensures deleted fields are properly removed
  # save property to raw collection
  id = PREFIX + property.ListingId
  ts = if property.ts then property.ts else new Date()
  # change @odata.id into { "odataID": "" }
  if property["@odata.id"]?
    property["odataID"] = property["@odata.id"]
    delete property["@odata.id"]
  await CarRawCol.replaceOne(
    { _id: id},
    { ...property,ts: ts },
    { upsert: true }
  )
  # save property to master collection, delete empty fields
  propertyCopyFilterd = await filterEmptyFieldsNSaveStats(property,FieldStatsCol,FIELD_STATS_ID)
  await replaceMasterRecord(id,propertyCopyFilterd,ts,mt)
  # TODO: save photos to local storage in the future
  return property


replaceMasterRecord=(id,propertyCopy,ts,mt)->
  fieldsToReserve = RESOURCE_TO_BE_MERGED.concat(['DCnt'])
  # Step 1: Retrieve the old property
  oldProperty = await CarMasterCol.findOne {_id:id}
  # Step 2: Filter and retain specified fields
  reservedFields = {}
  if oldProperty?
    for field in fieldsToReserve
      if oldProperty[field]?
        reservedFields[field] = oldProperty[field]
  # Step 3: Process media and get the processed document and fields to unset
  {processedMedia, fieldsToUnset} = processPropertyMedia(propertyCopy)
  # Step 4: Merge reserved fields with new property
  newProperty = Object.assign {}, processedMedia, reservedFields, {ts: ts, mt:mt}
  # Step 5: Perform `replaceOne`
  await CarMasterCol.replaceOne {_id: id}, newProperty, {upsert: true}


getAndUpsertResourceReplicationAndMergeAsync = ({
  queryTs, # @required. The start timestamp for resource fetching
  resourceName, # @required
  needMerge = false, # Whether to merge the resource into the property
  queryTs2 = null, # The end timestamp for resource fetching
  expand = null, # BridgeReso query $expand
  isProp = false,
  lastResoRequestTs = null
}) ->
  debug.debug "running getAndUpsertResourceReplicationAndMergeAsync arguments:", arguments
  filterTs = queryTs
  countTotal = 0
  lastResoRequestTsNew = lastResoRequestTs
  lastResoRequestTsDate = new Date(lastResoRequestTs)
  if lastResoRequestTsDate
    filterTs = lastResoRequestTsDate
  if queryTs2
    filter="BridgeModificationTimestamp ge #{filterTs.toISOString()} and BridgeModificationTimestamp le #{queryTs2.toISOString()}"
  else
    filter="BridgeModificationTimestamp ge #{filterTs.toISOString()}"
  params = {
    $top: 2000,
    $filter: filter
  }
  if expand
    params.$expand = expand
  debug.info "params", params
  resourceType=RESOURCE_MAPPING[resourceName].typeName
  collection=RESOURCE_MAPPING[resourceName].col
  resoKey = RESOURCE_MAPPING[resourceName].resoKey
  resources = await BridgeResoClient.getResourceReplicationAsync(
    BridgeResoClient.resourceTypes[resourceType],
    params,
    FETCH_TIMEOUT
  )
  lastTs = new Date()
  if needMerge
    propSet = new Set()
  loop
    count = 0
    for resource in resources.value
      if isProp
        await saveProperty resource
      else
        await collection.updateOne { _id: resource[resoKey] },
          { $set: resource, $setOnInsert: { ts: new Date() }},
          { upsert: true }
        speedMeter.check {"save#{resourceName}":1}
        if needMerge
          if resource.ListingKeyNumeric
            if 'number' is typeof resource.ListingKeyNumeric
              propSet.add(resource.ListingKeyNumeric)
            else
              debug.warn "resource #{resourceName} #{resource[resoKey]} ListingKeyNumeric is not number but #{resource.ListingKeyNumeric}"
          else
            debug.warn "resource #{resourceName} #{resource[resoKey]} has no ListingKeyNumeric"
      lastTs=resource.BridgeModificationTimestamp
      count++
    if needMerge and (propSet.size > 0)
      debug.debug "propSet.size, resource", propSet.size, resourceName
      await mergeResourceAndProperties(propSet, resourceName)
      propSet.clear()
    countTotal += count
    cfg = {
      MlsImportKeysCol:MlsImportKeysCol,
      nextTs: lastTs,
      resourceName:resourceName,
      count:count,
      done:false,
      downloadName:DOWNLOAD_NM
    }
    isDone = (resources['@odata.count'] is 0) or (not resources['@odata.nextLink'])
    if isDone
      cfg.done = true
      lastResoRequestTsNew = null
      await updateResourceRequestLog cfg
      break  
    await updateResourceRequestLog cfg
    lastResoRequestTsNew = lastTs
    resources = await BridgeResoClient.getResourceByNextLinkAsync resources['@odata.nextLink'], FETCH_TIMEOUT
    debug.info "getResourceByNextLinkAsync nextLink", resources['@odata.nextLink']
  return [countTotal,lastResoRequestTsNew]


_getAndUpsertResourceReplicationAndMerge = ({
  queryTs, # @required. The start timestamp for resource fetching
  resourceName, # @required
  needMerge = false, # Whether to merge the resource into the property
  queryTs2 = null, # The end timestamp for resource fetching
  expand = null, # BridgeReso query $expand
  isProp=false
}) ->
  countTotal = 0
  retryCount = 0
  lastResoRequestTs = queryTs
  # get lastResoRequestTs from db
  logInDB = await getLastResourceRequestInfo(MlsImportKeysCol,resourceName,DOWNLOAD_NM)
  if logInDB?.next
    lastResoRequestTs = logInDB.next
  while lastResoRequestTs
    try
      [count,lastResoRequestTs] = await getAndUpsertResourceReplicationAndMergeAsync({
        queryTs: queryTs,
        resourceName: resourceName,
        needMerge: needMerge,
        queryTs2: queryTs2,
        expand: expand,
        isProp: isProp,
        lastResoRequestTs:lastResoRequestTs,
      })
      debug.info "Updated #{count} #{resourceName}"
      countTotal += count
      retryCount = 0  # Reset retry count on success
    catch error
      debug.error error
      if ignorableErrorReso(error)
        logInDB = await getLastResourceRequestInfo(MlsImportKeysCol,resourceName,DOWNLOAD_NM)
        if logInDB?.next
          lastResoRequestTs = logInDB.next
      else
        retryCount++
        await sleep(GET_RESOURCE_WAIT_TIME)
      if retryCount > GET_RESOURCE_MAX_RETRIES
        debug.error "Failed #{retryCount} times for #{resourceName}. Exiting loop."
        throw error
  return countTotal


initialQueryBack = (queryTs) ->
  nextQueryTs = new Date()
  processNextTs = Date.now() + (AVG_RUNNING_MS * 3)
  try
    await handleProcessStatus {
      status: RUNNING_NORMAL,
      startTs: Date.now(),
      nextTs: processNextTs
    }
    debug.info "step1: get properties"
    # NOTE: ListOffice,CoListOffice request will throw error 400
    # ERROR:mlsImport/cornerstone/carDownload.coffee,mlsImport/cornerstone/carDownload.coffee,2024-12-09T11:55:30.794 mlsImport/cornerstone/carDownload.coffee _getAndUpsertProperties 843 Error: Call Bridge API failed: https://api.bridgedataoutput.com/api/v2/OData/itso/Property?%24top=200&%24skip=0&%24expand=ListAgent%2CCoListAgent%2CBuyerAgent%2CCoBuyerAgent%2CListOffice%2CCoListOffice&%24filter=BridgeModificationTimestamp+ge+2024-12-08T05%3A18%3A12.388Z&%24orderby=BridgeModificationTimestamp+asc 400 {"error":{"code":400,"message":"Invalid field in $expand parameter: ListOffice"}} at BridgeReso.getResourceAsync (/opt/rmappweb/src/libapp/bridgeReso.coffee:42:17)
    propExpand='ListAgent,CoListAgent,BuyerAgent,CoBuyerAgent' #ListOffice,CoListOffice
    count = await _getAndUpsertResourceReplicationAndMerge({
      queryTs:queryTs,
      resourceName:RESOURCE_NAME_PROPERTY,
      needMerge:false,
      expand:propExpand,
      isProp:true
    })
    debug.info "updated #{count} properties"

    debug.info "step2: get openHouse, propertyRoom, propertyUnitType and merge"
    for resourceName in RESOURCE_TO_BE_MERGED
      queryTsNew = queryTs
      thresholdDate = new Date(Date.now() - OPEN_HOUSE_THRESHOLD)# openHouse only get 30days data
      if (resourceName is 'openHouse') and (queryTs < thresholdDate)
        queryTsNew = thresholdDate
      count = await _getAndUpsertResourceReplicationAndMerge({
        queryTs:queryTsNew,
        resourceName:resourceName,
        needMerge:true,
        queryTs2:nextQueryTs
      })
      debug.info "updated #{count} #{resourceName}"

    debug.info "step3: get other resources"
    for resourceName in RESOURCE_NO_MERGE
      count = await _getAndUpsertResourceReplicationAndMerge({
        queryTs:queryTs,
        resourceName:resourceName,
        needMerge:false,
        queryTs2:nextQueryTs
      })
      debug.info "updated #{count} #{resourceName}"
    await MlsImportKeysCol.updateOne { _id: PROCESS_STATUS_ID },
    { $set: { next: nextQueryTs }},
    { upsert: true }
    debug.info "sleep 15 minutes, then start mainLoop"
    await sleep(SLEEP_MS)
    await mainLoop nextQueryTs
  catch error
    handleExit error


getAndUpsertPropertiesAsync = (queryTs) ->
  queryTs = new Date(queryTs) if (typeof queryTs is 'string') or (typeof queryTs is 'number')
  countTotal = 0
  params = {
    $top: 200,
    $skip: 0,
    $expand: 'ListAgent,CoListAgent,BuyerAgent,CoBuyerAgent', #ListOffice,CoListOffice
    $filter: "BridgeModificationTimestamp ge #{queryTs.toISOString()}",
    $orderby: "BridgeModificationTimestamp asc"
  }
  lastResoRequestTsNew = queryTs
  loop
    count = 0
    debug.info "get properties with params", params
    properties = await BridgeResoClient.getResourceAsync BridgeResoClient.resourceTypes.PROPERTY, params
    for property in properties.value
      await saveProperty property
      lastResoRequestTsNew = property.BridgeModificationTimestamp
      count++
    countTotal += count
    cfg = {
      MlsImportKeysCol:MlsImportKeysCol,
      nextTs: lastResoRequestTsNew,
      resourceName:RESOURCE_NAME_PROPERTY,
      count:count,
      done:false,
      downloadName:DOWNLOAD_NM
    }
    isDone = (properties.value.length < params.$top)
    if isDone
      cfg.done = true
      await updateResourceRequestLog cfg
      lastResoRequestTsNew = null
      break
    await updateResourceRequestLog cfg
    params.$filter="BridgeModificationTimestamp ge #{lastResoRequestTsNew}"
  return [countTotal,lastResoRequestTsNew]


_getAndUpsertProperties = (queryTs, propSet) ->
  countTotal = 0
  retryCount = 0
  resourceName = RESOURCE_NAME_PROPERTY
  lastResoRequestTs = queryTs
  logInDB = await getLastResourceRequestInfo(MlsImportKeysCol,resourceName,DOWNLOAD_NM)
  if logInDB?.next
    lastResoRequestTs = logInDB.next

  while lastResoRequestTs
    try
      [count, lastResoRequestTs] = await getAndUpsertPropertiesAsync(lastResoRequestTs)
      debug.info "Updated #{count} records for #{resourceName}"
      debug.info "lastResoRequestTs", lastResoRequestTs
      countTotal += count
      retryCount = 0  # Reset retry count on success
    catch error
      debug.error error
      if ignorableErrorReso(error)
        logInDB = await getLastResourceRequestInfo(MlsImportKeysCol,resourceName,DOWNLOAD_NM)
        if logInDB?.next
          lastResoRequestTs = logInDB.next
      else
        retryCount++
        await sleep(GET_RESOURCE_WAIT_TIME)
      if retryCount > GET_RESOURCE_MAX_RETRIES
        debug.error "Failed #{retryCount} times for #{resourceName}. Exiting loop."
        throw error
  return countTotal



getAndSaveResourcesAsync = ({resourceName, queryTs, lastResoRequestTs, propSet=null}) ->
  debug.info "running getAndSaveResourcesAsync #{resourceName}, queryTs:#{queryTs}, lastResoRequestTs:#{lastResoRequestTs}"
  filterTs = queryTs
  countTotal = 0
  lastResoRequestTsNew = lastResoRequestTs
  lastResoRequestTsDate = new Date(lastResoRequestTs)
  if lastResoRequestTsDate
    filterTs = lastResoRequestTsDate
  thresholdDate = new Date(Date.now() - OPEN_HOUSE_THRESHOLD)# openHouse only get 30days data
  if (resourceName is 'openHouse') and (filterTs < thresholdDate)
    filterTs = thresholdDate
  params = {
    $top: 200,  #v2 the page size
    $skip: 0,
    $filter: "BridgeModificationTimestamp ge #{filterTs.toISOString()}",
    $orderby: "BridgeModificationTimestamp asc"
  }
  debug.info "params", params
  resourceType = RESOURCE_MAPPING[resourceName].typeName
  collection = RESOURCE_MAPPING[resourceName].col
  resoKey = RESOURCE_MAPPING[resourceName].resoKey
  loop
    count = 0
    debug.info "getResourceAsync #{resourceName}, params:", params
    resources = await BridgeResoClient.getResourceAsync BridgeResoClient.resourceTypes[resourceType], params, FETCH_TIMEOUT
    for resource in resources.value
      await collection.updateOne { _id: resource[resoKey] },
        { $set: resource, $setOnInsert: { ts: new Date() }},
        { upsert: true }
      lastResoRequestTsNew = resource.BridgeModificationTimestamp
      speedMeter.check {"save#{resourceName}":1}
      if propSet
        if resource.ListingKeyNumeric
          if 'number' is typeof resource.ListingKeyNumeric
            propSet.add(resource.ListingKeyNumeric)
          else
            debug.warn "resource #{resourceName} #{resource[resoKey]} ListingKeyNumeric is not number but #{resource.ListingKeyNumeric}"
        else
          debug.warn "resource #{resourceName} #{resource[resoKey]} has no ListingKeyNumeric"
      count++
    countTotal += count
    cfg ={
      MlsImportKeysCol:MlsImportKeysCol,
      nextTs: lastResoRequestTsNew,
      resourceName: resourceName,
      count: count,
      done: false,
      downloadName: DOWNLOAD_NM
    }
    isDone = (resources.value.length < params.$top)
    if isDone
      cfg.done = true
      await updateResourceRequestLog cfg
      lastResoRequestTsNew = null
      break
    await updateResourceRequestLog cfg
    params.$filter = "BridgeModificationTimestamp ge #{lastResoRequestTsNew}"
  return [countTotal, lastResoRequestTsNew]


buildQueryForResource = (resourceName, propId) ->
  if resourceName in RESOURCE_TO_BE_MERGED
    if 'string' is typeof propId
      propId = parseInt propId
    else if 'number' is typeof propId
      # do nothing
    else
      throw new Error "Unknown propId type: #{typeof propId}"
    query = { ListingKeyNumeric: propId }
  else
    throw new Error "Unknown resource name: #{resourceName}"
  return query


mergeResourceAndProperties = (propSet,resourceName) ->
  count = 0
  debug.info "running mergeResourceAndProperties #{resourceName}"
  if not propSet
    debug.info "resourceName: #{resourceName} propSet is null, return 0"
    return count
  # using propId to get prop and resource, then merge resource into property
  propArray = [...propSet]
  collection = RESOURCE_MAPPING[resourceName].col
  for propId in propArray
    query = buildQueryForResource(resourceName, propId)
    prop = await CarMasterCol.findOne query
    if prop
      resources = await collection.findToArray { ListingKeyNumeric: propId },{sort:{BridgeModificationTimestamp:1}}
      set = {}
      set[resourceName] = resources
      await CarMasterCol.updateOne { _id: prop._id },
        { $set: set },
        { upsert: true }
      count++
      debug.debug "done mergeResourceAndPropertie resourceName:#{resourceName} _id:#{prop._id}"
      speedMeter.check {"merge#{resourceName}":1}
    else
      debug.error "property #{propId} not found"
      speedMeter.check {"merge#{resourceName}NotFound":1}
  count


_getAndSaveResources = (resourceName, queryTs, propSet) ->
  countTotal = 0
  retryCount = 0
  lastResoRequestTs = queryTs
  logInDB = await getLastResourceRequestInfo(MlsImportKeysCol,resourceName,DOWNLOAD_NM)
  if logInDB?.next
    lastResoRequestTs = logInDB.next
  while lastResoRequestTs
    try
      [count, lastResoRequestTs] = await getAndSaveResourcesAsync({
        resourceName: resourceName,
        queryTs: queryTs,
        lastResoRequestTs: lastResoRequestTs,
        propSet: propSet
      })
      debug.info "Updated #{count} records for #{resourceName}"
      countTotal += count
      retryCount = 0  # Reset retry count on success
    catch error
      debug.error error
      if ignorableErrorReso(error)
        logInDB = await getLastResourceRequestInfo(MlsImportKeysCol, resourceName,DOWNLOAD_NM)
        if logInDB?.next
          lastResoRequestTs = logInDB.next
      else
        retryCount++
        await sleep(GET_RESOURCE_WAIT_TIME)
      if retryCount > GET_RESOURCE_MAX_RETRIES
        debug.error "Failed #{retryCount} times for #{resourceName}. Exiting loop."
        throw error
  return countTotal


getAllResources = (queryTs) ->
  # get openHouse,propertyRoom,propertyUnitType
  for resourceName in RESOURCE_TO_BE_MERGED
    await _getAndSaveResources(resourceName, queryTs, RESOURCE_SET_MAPPING[resourceName])

  # get other resources
  for resourceName in RESOURCE_NO_MERGE
    await _getAndSaveResources(resourceName, queryTs, null)


###*
# Get next batch of properties from database
# @returns {Object} Properties and checkpoint information
###
getNextBatchFromDb = ->
  debug.info "Getting next batch of active/conditional properties from database..."
  
  query = {
    MlsStatus: {$in: ['Active', 'Conditional']}
  }
  
  # If we have a last position, start after it
  if currentLastListingId
    query.ListingId = {$gt: currentLastListingId}
    
  # Get next batch
  props = await CarMasterCol.findToArray(
    query,
    {
      projection: {
        ListingId: 1,
        MlsStatus: 1
      },
      sort: {ListingId: 1},
      limit: DB_BATCH_SIZE
    }
  )
  
  if props.length is 0
    return {
      props: {},
      lastId: null,
      hasMore: false
    }

  # Convert to dictionary and get last key
  dbProps = {}
  lastId = null
  
  for prop in props
    if prop.ListingId
      dbProps[prop.ListingId] = prop.MlsStatus
      lastId = prop.ListingId
      
  debug.info "Found #{Object.keys(dbProps).length} properties in current batch"
  return {
    props: dbProps,
    lastId: lastId,
    hasMore: props.length is DB_BATCH_SIZE
  }

###*
# Fetch properties from RESO API and update their statuses in one step
# @param {Object} dbProps Dictionary of properties from database with ListingId as key and MlsStatus as value
# @returns {number} Number of properties updated
###
fetchAndUpdatePropertyStatuses = (dbProps) ->
  debug.info "Fetching and updating properties from RESO API..."
  updatedCount = 0
  listingIds = Object.keys(dbProps)
  
  # Build filter for all IDs in this batch
  recordIds = listingIds.map((id) -> "'#{id}'").join(',')
  filters = "ListingId in (#{recordIds})"
  params = {
    $top: listingIds.length,
    $filter: filters
  }
  debug.info "fetchAndUpdatePropertyStatuses params", params
  
  try
    resources = await BridgeResoClient.getResourceReplicationAsync(
      BridgeResoClient.resourceTypes.PROPERTY,
      params,
      FETCH_TIMEOUT
    )
    
    # Create map of returned properties
    resoProps = {}
    for property in resources.value
      if property.ListingId
        resoProps[property.ListingId] = property.MlsStatus
        await CarMasterCol.updateOne(
          {
            _id: PREFIX + property.ListingId,
            $or:[
              {MlsStatus: 'Delete'},
              {DCnt: {$exists: true}}
            ]
          },
          {
            $set: {MlsStatus: property.MlsStatus},
            $unset: {DCnt: 1}
          }
        )
    
    # Update statuses for all properties
    for listingId in listingIds
      resoStatus = resoProps[listingId]
      
      # Property no longer in RESO - mark as deleted
      if not resoStatus?
        debug.info "Property #{listingId} no longer in RESO, marking as deleted"
        updateSet = {}
        prop = await CarMasterCol.findOne {ListingId: listingId}
        updateSet.DCnt = (prop?.DCnt or 0) + 1
        if updateSet.DCnt > DCNT_THRESHOLD
          updateSet.MlsStatus = 'Delete'
          speedMeter.check {deactivatedProperty: 1}
          updatedCount++
        await CarMasterCol.updateOne(
          {ListingId: listingId},
          {$set: updateSet}
        )

  catch error
    debug.error "Error processing properties", error
    throw error
    
  debug.info "Updated #{updatedCount} properties"
  return updatedCount

###*
# Process active property sync
###
processActivePropertySync = ->
  try
    startTs = new Date()
    totalChecked = 0
    totalUpdated = 0
    
    # Reset the global lastListingId at the start of each daily check
    currentLastListingId = null
    
    loop
      # Get next batch of properties
      {props: dbProps, lastId, hasMore} = await getNextBatchFromDb()
      
      if Object.keys(dbProps).length > 0
        # Fetch and update properties in one step
        updatedCount = await fetchAndUpdatePropertyStatuses(dbProps)
        
        # Update global lastListingId
        currentLastListingId = lastId
        
        totalChecked += Object.keys(dbProps).length
        totalUpdated += updatedCount
        
        debug.info "Completed batch status sync",
          batchSize: Object.keys(dbProps).length
          updatedInBatch: updatedCount
          totalChecked: totalChecked
          totalUpdated: totalUpdated
          lastId: lastId
        
        # If no more properties to check or no more in current batch, break the loop
        if not hasMore
          break
          
      else
        break
        
    # Log final completion
    debug.info "Completed all property status sync",
      startTs: startTs
      endTs: new Date()
      durationMs: new Date().getTime() - startTs.getTime()
      totalChecked: totalChecked
      totalUpdated: totalUpdated
      
  catch error
    debug.error "Error in processActivePropertySync", error
    throw error

###
# Calculate time until next 2 AM
###
getTimeUntilNextRun = ->
  now = new Date()
  nextRun = new Date(now)
  nextRun.setHours(ACTIVE_PROP_CHECK_HOUR, 0, 0, 0)
  if nextRun <= now
    nextRun.setDate(nextRun.getDate() + 1)
  return nextRun.getTime() - now.getTime()

###
# Start property status sync worker that runs daily to sync active/deleted statuses
###
startPropertyStatusSyncWorker = ->
  debug.info "Starting property status sync worker..."
  
  while true
    # Wait until next 2 AM
    timeUntilNextRun = getTimeUntilNextRun()
    nextRunTime = new Date(Date.now() + timeUntilNextRun)
    debug.info "Sleeping until next property status sync at #{nextRunTime}"
    await sleep(timeUntilNextRun)
    
    try
      await processActivePropertySync()
    catch error
      debug.error "Error in property status sync worker, will try again at next 2 AM run", error
      await handleProcessStatus {
        status: RUNNING_ERROR,
        error,
        nextTs: Date.now() + getTimeUntilNextRun()
      }


  
mainLoop = (queryTs) ->
  while true
    try
      nextQueryTs = new Date()
      # get all resources
      await getAllResources(queryTs)
      # get properties after open houses, so that we can make sure the property exits
      count = await _getAndUpsertProperties queryTs
      debug.info "updated #{count} properties"
      # merge open houses,propertyRoom,propertyUnitType and properties
      for resourceName in RESOURCE_TO_BE_MERGED
        count = await mergeResourceAndProperties RESOURCE_SET_MAPPING[resourceName], resourceName
        RESOURCE_SET_MAPPING[resourceName].clear()
        debug.info "merged #{resourceName} into #{count} properties"

      await MlsImportKeysCol.updateOne { _id: PROCESS_STATUS_ID },
      { $set: { next: nextQueryTs }},
      { upsert: true }
      await handleProcessStatus {
        status: RUNNING_NORMAL,
        nextTs: (Date.now() + SLEEP_MS + AVG_RUNNING_MS)
      }
      queryTs = nextQueryTs
    catch error
      debug.error error
      await handleProcessStatus {
        status: RUNNING_ERROR,
        error,
        nextTs: (Date.now() + SLEEP_MS + AVG_RUNNING_MS)
      }
    debug.info "sleep 15 minutes, then run mainLoop again"
    await sleep(SLEEP_MS)

 
main = () ->
  isForce = AVGS.includes('force')
  hasRunningProcess = await checkRunningProcess()
  if (not isForce) and hasRunningProcess
    return EXIT HAS_RUNNING_PROCESS
  importKey = await MlsImportKeysCol.findOne { _id: PROCESS_STATUS_ID }
  if (not importKey?.next)
    queryTs = new Date(DEFAULT_QUERY_TIMESTAMP)
    await initialQueryBack queryTs
  else if (Date.now() - importKey.next > REPLICATION_MODE_THRESHOLD)
    queryTs = importKey.next
    await initialQueryBack queryTs
  else
    queryTs = importKey.next
    processNextTs = Date.now() + AVG_RUNNING_MS
    await handleProcessStatus {
      status: RUNNING_NORMAL,
      startTs: Date.now(),
      nextTs: processNextTs
    }
    # Start both workers
    startPropertyStatusSyncWorker()
    await mainLoop queryTs



main()



