###
*  Description:    fetch Edmonton listings from Bridge RESO API
*  New config:     ./start.sh -t batch -n raeDownload -cmd "lib/batchBase.coffee mlsImport/edmonton/raeDownload.coffee [-t token force]"
*  Author:         <PERSON>, <PERSON>
*  Run duration:   forever
*  Run frequency:  forever
###
fs = require 'fs'
path = require 'path'
debug = DEBUG()
config = CONFIG(['rae'])
{ getHash } = require '../../lib/helpers_string'
{ sleep } = require '../../lib/helpers_function'
SpeedMeter = require('../../lib/speed')
impReadableHelper = require('../../libapp/impReadableHelper')
{ updateResourceRequestLog, getLastResourceRequestInfo, ignorableErrorReso, processPropertyMedia } = require '../../libapp/impCommon'
filterEmptyFieldsNSaveStats = require('../../libapp/impCommon').getEmptyFilterNStatsFunction()
{ createBridgeResoClient } = require('../../libapp/reso')

createHash = require('crypto').createHash
{
  processKillSignal,
  RUNNING_NORMAL,
  RUNNING_ERROR,
  RUNNING_WARNING,
  getProcessHandler,
  HAS_RUNNING_PROCESS
} = INCLUDE('libapp.processHelper')

yargs = require('yargs')(AVGS)
yargs.option('token', { alias: 't', description: 'Bridge API token'})

ONE_YEAR_SECONDS = 365*24*3600
THIRTY_DAYS_SECONDS = 30*24*3600

ProcessStatusCol = COLLECTION 'vow','processStatus'
MlsImportKeysCol = COLLECTION 'rni','mls_import_keys'
RaeRawCol = COLLECTION 'rni','mls_rae_raw_records'
RaeRawCol.createIndex { mt: 1 }, { background: true }
RaeRawCol.createIndex { _mt: 1 }, { background: true }
RaeMasterCol = COLLECTION 'rni','mls_rae_master_records'
RaeMasterCol.createIndex { mt: 1 }, { background: true }
RaeMasterCol.createIndex { _mt: 1 }, { background: true }
RaeLogCol = COLLECTION 'rni','mls_rae_logs'
RaeLogCol.createIndex { id:1, _mt:-1 }, { background:true }
RaeLogCol.createIndex { id:1, hash:-1 }, { background:true }
RaeLogCol.createIndex { sid:1, _mt:-1 }, { background:true }
RaeLogCol.createIndex { _mt:-1 }, { expireAfterSeconds:ONE_YEAR_SECONDS, background:true }
RaeOpenHouseCol = COLLECTION 'rni','mls_rae_openhouses'
RaeOpenHouseCol.createIndex { _mt: -1 }, { expireAfterSeconds:THIRTY_DAYS_SECONDS, background:true }
RaeOpenHouseCol.createIndex { ListingId: 1, OpenHouseStartTime: 1 }, { background:true }
# RaePhotoQueue = COLLECTION 'rni','mls_rae_photo_queue'
# RaePhotoQueue.createIndex {priority:-1},{background:true}
# RaePhotoQueue.createIndex {dlShallEndTs:-1},{background:true}
FieldStatsCol = COLLECTION 'rni','mls_field_stats'
RaeDupLog = COLLECTION 'rni','mls_rae_dup_log'
RaeDupLog.createIndex { _mt: 1 }, { expireAfterSeconds:ONE_YEAR_SECONDS, background:true }

BRIDGE_API_TOKEN = yargs.argv.token
PROCESS_STATUS_ID = 'RAE_PropertyDownload'
FIELD_STATS_ID = 'EDM'
AVG_RUNNING_MS = 5 * 60 * 1000 # 5 minutes
SLEEP_MS = 15 * 60 * 1000 # 15 minutes
FETCH_TIMEOUT = 10 *60 * 1000 # 10 minutes
REPLICATION_MODE_THRESHOLD = 30 * 24 * 60 * 60 * 1000 # 30 days
OPEN_HOUSE_THRESHOLD = 30 * 24 * 60 * 60 * 1000 # 30 days
DEFAULT_QUERY_TIMESTAMP = '2022-11-20T01:00:00Z' # oldest '2022-11-29T17:13:54.469Z'
# DEFAULT_QUERY_TIMESTAMP = '2024-11-30T01:00:00Z' # only for testing
PREFIX = 'EDM'
DOWNLOAD_NM = 'RaeDownload'
MLS_ID_FIELD = 'ListingId'
LAST_MT_FIELD = 'BridgeModificationTimestamp'
RESOURCE_NAME_PROPERTY = 'property'
RESOURCE_NAME_OPEN_HOUSE = 'openHouse'
MAX_FETCH_COUNT = 10000
GET_RESOURCE_MAX_RETRIES = 3
GET_RESOURCE_WAIT_TIME = 5 * 60 * 1000 # 5 minutes
RAE_GLOBAL_KEY = 'RAE_GLOBAL'
PROP_EXPAND_FIELDS = 'ListAgent,CoListAgent,BuyerAgent,CoBuyerAgent'
jsonRelativePath = '../../../docs/Dev_import/edm/edm_prop_date_fields.json'
jsonAbsolutePath = path.join __dirname, jsonRelativePath

RESOURCE_MAPPING={
  property:{typeName:'PROPERTY'},
  openHouse:{resoKey:'OpenHouseKey',typeName:'OPEN_HOUSE'},
}

{
  handleProcessStatus,
  handleExit,
  checkRunningProcess
} = getProcessHandler {
  ProcessStatusCol,
  id: PROCESS_STATUS_ID,
  filePath: BATCH_FILE_NAME,
  exitFn: EXIT
}

processKillSignal PROCESS_STATUS_ID, handleExit

speedMeter = SpeedMeter.createSpeedMeter {
  intervalTriggerCount: 1000,
  intervalCallback: (speedMeter) ->
    debug.info speedMeter.toString()
}

if not BRIDGE_API_TOKEN
  if config.rae?
    BRIDGE_API_TOKEN = config.rae.token
  if not BRIDGE_API_TOKEN
    throw new Error('Please provide the Bridge API token using -t or in the config file.')

BridgeResoClient =  createBridgeResoClient({ dataset: 'rae', token: BRIDGE_API_TOKEN })

saveProperty = (property)->
  # get hash
  propertyCopy = Object.assign {},property
  delete propertyCopy['BridgeModificationTimestamp']
  hash = getHash(JSON.stringify(propertyCopy))

  # Check if the hash matches the last record
  lastLog = await RaeLogCol.findOne({id: property.ListingKeyNumeric},
    {projection: {hash: 1,BridgeModificationTimestamp:1}, sort: {_mt: -1}})
  # if hash is the same as the last log, only update and directly return 0, won't save to the log
  if (hash is lastLog?.hash)
    speedMeter.check {sameHash:1}
    if (lastLog.BridgeModificationTimestamp?) and (property.BridgeModificationTimestamp?) and (lastLog.BridgeModificationTimestamp isnt property.BridgeModificationTimestamp)
      debug.info "same hash #{hash} as the last log, only updating the BridgeModificationTimestamp", property.BridgeModificationTimestamp
      await RaeLogCol.updateOne {_id:lastLog._id},{$set:{BridgeModificationTimestamp:property.BridgeModificationTimestamp}}
    await RaeDupLog.insertOne {
      data:property,
      id:property.ListingKeyNumeric,
      hash:hash,
      BridgeModificationTimestamp:property.BridgeModificationTimestamp
    }
    return 0
  # If the hash is different, save it to the log
  mt = new Date(property[LAST_MT_FIELD])
  insertRet = await RaeLogCol.insertOne {
    id:property.ListingKeyNumeric,
    hash:hash,
    sid: property[MLS_ID_FIELD],
    data:property,
    mt: mt,
    BridgeModificationTimestamp:property.BridgeModificationTimestamp
  }
  speedMeter.check {saveLog:1}

  property.mt = mt
  # save property to raw collection
  id = PREFIX + property.ListingId
  ts = if property.ts then property.ts else new Date()
  if property["@odata.id"]?
    property["odataID"] = property["@odata.id"]
    delete property["@odata.id"]
  await RaeRawCol.replaceOne(
    { _id: id},
    { ...property,ts: ts },
    { upsert: true }
  )
  # save property to master collection,convert to readable and delete empty fields
  debug.debug "saveProperty", property
  readableProperty = await impReadableHelper.edmConvertToReadable(property,MlsImportKeysCol)
  debug.debug "readableProperty", readableProperty
  readablePropertyFilterd = await filterEmptyFieldsNSaveStats(readableProperty,FieldStatsCol,FIELD_STATS_ID)
  debug.debug "readablePropertyFilterd", readablePropertyFilterd
  await replaceMasterRecord(id,readablePropertyFilterd,ts,mt)
  # TODO: save photos to local storage in the future
  return property


replaceMasterRecord=(id,propertyCopy,ts,mt)->
  # Step 1: Retrieve the old property
  oldProperty = await RaeMasterCol.findOne {_id:id}
  # Step 2: Filter and retain specified fields
  reservedFields = {}
  if oldProperty?
    field = RESOURCE_NAME_OPEN_HOUSE
    if oldProperty[field]?
      reservedFields[field] = oldProperty[field]
    # Step 3: Process media and get the processed document and fields to unset
  {processedMedia, fieldsToUnset} = processPropertyMedia(propertyCopy)
  # Step 3: Merge reserved fields with new property
  newProperty = Object.assign {}, processedMedia, reservedFields, {ts: ts, mt:mt}
  # Step 4: Perform `replaceOne`
  await RaeMasterCol.replaceOne {_id: id}, newProperty, {upsert: true}


getAndUpsertPropertiesReplicationAsync = (queryTs) ->
  lastResoRequestTs = queryTs
  countTotal = 0
  params = {
    $top: 2000,
    $expand: 'ListAgent,CoListAgent,BuyerAgent,CoBuyerAgent',
    $filter: "BridgeModificationTimestamp ge #{queryTs.toISOString()}"
  }
  debug.info "getAndUpsertPropertiesReplicationAsync", params
  properties = await BridgeResoClient.getResourceReplicationAsync(
    BridgeResoClient.resourceTypes.PROPERTY,
    params,
    FETCH_TIMEOUT
  )
  loop
    count = 0
    for property in properties.value
      saveProperty property
      lastResoRequestTs = property[LAST_MT_FIELD]
      count++
    countTotal += count
    cfg={
      MlsImportKeysCol:MlsImportKeysCol,
      nextTs:lastResoRequestTs,
      resourceName:RESOURCE_NAME_PROPERTY,
      count:count,
      done:false,
      downloadName:DOWNLOAD_NM
    }
    isDone = (properties['@odata.count'] is 0) or (not properties['@odata.nextLink'])
    if isDone
      cfg.done = true
      await updateResourceRequestLog cfg
      lastResoRequestTsNew = null
      break
    await updateResourceRequestLog cfg
    properties = await BridgeResoClient.getResourceByNextLinkAsync properties['@odata.nextLink']
    debug.info "getResourceByNextLinkAsync nextLink", properties['@odata.nextLink']
  return [countTotal,lastResoRequestTsNew]



getAndUpsertResource = ({
  queryTs, 
  resourceName, 
  getResourceAsync, 
  thresholdDate = null, 
  propSet = null
}) ->
  countTotal = 0
  retryCount = 0
  logInDB = await getLastResourceRequestInfo(MlsImportKeysCol, resourceName, DOWNLOAD_NM)

  if logInDB?.next and thresholdDate? and logInDB.next > thresholdDate
    lastResoRequestTs = logInDB.next
  else if thresholdDate?
    lastResoRequestTs = thresholdDate
  else if logInDB?.next
    lastResoRequestTs = logInDB.next
  else
    lastResoRequestTs = queryTs

  while lastResoRequestTs
    try
      if propSet?
        [count, lastResoRequestTs] = await getResourceAsync(lastResoRequestTs,resourceName,propSet)
      else if getResourceAsync is getAndUpsertResourceAsync
        [count, lastResoRequestTs] = await getResourceAsync(lastResoRequestTs,resourceName)
      else
        [count, lastResoRequestTs] = await getResourceAsync(lastResoRequestTs)
      
      debug.info "Updated #{count} records for #{resourceName}"
      countTotal += count
      retryCount = 0
    catch error
      debug.error error
      if ignorableErrorReso(error)
        logInDB = await getLastResourceRequestInfo(MlsImportKeysCol, resourceName, DOWNLOAD_NM)
        if logInDB?.next
          lastResoRequestTs = logInDB.next
      else
        retryCount++
        await sleep(GET_RESOURCE_WAIT_TIME)
      if retryCount > GET_RESOURCE_MAX_RETRIES
        debug.error "Failed #{retryCount} times for #{resourceName}. Exiting loop."
        throw error
  return countTotal


_getAndUpsertProperties = (queryTs, propSet) ->
  await getAndUpsertResource({queryTs, resourceName:RESOURCE_NAME_PROPERTY, getResourceAsync:getAndUpsertResourceAsync})

_getOpenHouses = (queryTs, propSet) ->
  thresholdDate = new Date(Date.now() - OPEN_HOUSE_THRESHOLD)
  await getAndUpsertResource({queryTs, resourceName:RESOURCE_NAME_OPEN_HOUSE, getResourceAsync:getAndUpsertResourceAsync, thresholdDate, propSet})

_getAndUpsertPropertiesReplicationAsync = (queryTs) ->
  await getAndUpsertResource({queryTs, resourceName:RESOURCE_NAME_PROPERTY, getResourceAsync:getAndUpsertPropertiesReplicationAsync})


getAndUpsertResourceAsync = (queryTs,resourceName,propSet='') ->
  countTotal = 0
  params = {
    $top: 200,
    $skip: 0,
    $filter: "BridgeModificationTimestamp ge #{queryTs.toISOString()}"
    $orderby: "BridgeModificationTimestamp asc"
  }
  if resourceName is RESOURCE_NAME_PROPERTY
    params.$expand = PROP_EXPAND_FIELDS
  resourceType = RESOURCE_MAPPING[resourceName].typeName
  loop
    count = 0
    debug.info "get #{resourceName} with params", params
    resources = await BridgeResoClient.getResourceAsync BridgeResoClient.resourceTypes[resourceType], params, FETCH_TIMEOUT
    @resourceTypes = @RESOURCE_TYPE
    for resource in resources.value
      if resourceName is RESOURCE_NAME_PROPERTY
        await saveProperty resource
      else if resourceName is RESOURCE_NAME_OPEN_HOUSE
        await RaeOpenHouseCol.updateOne { _id: resource.OpenHouseKey },
          { $set: resource, $setOnInsert: { ts: new Date() } },
          { upsert: true }
        if resource.ListingKeyNumeric
          if 'number' is typeof resource.ListingKeyNumeric
            propSet.add(resource.ListingKeyNumeric)
          else
            debug.warn "#{resourceName} #{resource.OpenHouseKey} ListingKeyNumeric is not a number but #{resource.ListingKeyNumeric}"
      lastResoRequestTsNew = resource.BridgeModificationTimestamp
      speedMeter.check {"save#{resourceName}":1}
      count++
    countTotal += count
    
    cfg = {
      MlsImportKeysCol: MlsImportKeysCol,
      nextTs: lastResoRequestTsNew,
      resourceName: resourceName,
      count: count,
      done: false,
      downloadName: DOWNLOAD_NM
    }
    
    isDone = (resources.value.length < params.$top)
    if isDone
      cfg.done = true
      await updateResourceRequestLog cfg
      lastResoRequestTsNew = null
      break
    await updateResourceRequestLog cfg
    params.$filter = "BridgeModificationTimestamp ge #{lastResoRequestTsNew}"
  
  return [countTotal, lastResoRequestTsNew]


getEDMGlobal = (jsonFilePath) ->
  new Promise (resolve, reject) ->
    fs.readFile jsonFilePath, 'utf8', (err, data) ->
      if err
        debug.error 'Error reading JSON file:', err
        reject err
        return
      try
        jsonData = JSON.parse data
        debug.debug 'jsonData:', jsonData
        resolve jsonData
      catch parseError
        debug.error 'Error parsing JSON file:', parseError
        reject parseError


initialQueryBack = (queryTs) ->
  # save EDM global json to gFieldMap
  edmGlocalJson = await getEDMGlobal(jsonAbsolutePath)
  document = {
    gFieldMap:
        Property: edmGlocalJson
  }
  try 
    await MlsImportKeysCol.updateOne {_id: RAE_GLOBAL_KEY}, {$set: document}, {upsert: true}
    debug.info 'RAE_GLOBAL created'
  catch updateError
    debug.error 'Error updating RAE_GLOBAL:', updateError

  nextQueryTs = new Date()
  processNextTs = Date.now() + (AVG_RUNNING_MS * 3)
  try
    await handleProcessStatus {
      status: RUNNING_NORMAL,
      startTs: Date.now(),
      nextTs: processNextTs
    }
    debug.info "initialQueryBack: get properties"
    count = await _getAndUpsertPropertiesReplicationAsync queryTs
    debug.info "updated #{count} properties"

    await MlsImportKeysCol.updateOne { _id: PROCESS_STATUS_ID },
    { $set: { next: nextQueryTs }},
    { upsert: true }
    debug.info "sleep 15 minutes, then start mainLoop"
    await sleep(SLEEP_MS)
    await mainLoop nextQueryTs
  catch error
    handleExit error


buildQueryForResource = (propId) ->
  if 'string' is typeof propId
    propId = parseInt propId
  else if 'number' is typeof propId
    # do nothing
  else
    throw new Error "Unknown propId type: #{typeof propId}"
  query = { ListingKeyNumeric: propId }
  return query


mergeOpenHousesAndProperties = (propSet) ->
  count = 0
  debug.info "running mergeOpenHousesAndProperties"
  propArray = [...propSet]
  debug.debug "propArray", propArray
  for propId in propArray
    query = buildQueryForResource(propId)
    prop = await RaeMasterCol.findOne query
    if prop
      resources = await RaeOpenHouseCol.findToArray { ListingKeyNumeric: propId },{sort:{BridgeModificationTimestamp:1}}
      set = {}
      set[RESOURCE_NAME_OPEN_HOUSE] = resources
      await RaeMasterCol.updateOne { _id: prop._id },
        { $set: set },
        { upsert: true }
      count++
      debug.debug "done mergeResourceAndPropertie openhouse _id:#{prop._id}"
      speedMeter.check {"mergeOpenHouse}":1}
    else
      debug.error "property #{propId} not found"
      speedMeter.check {"mergeOpenHouseNotFound":1}
  count

mainLoop = (queryTs) ->
  propSet = new Set()
  while true
    try
      nextQueryTs = new Date()
      # get OpenHouses
      openHouseCount = await _getOpenHouses queryTs, propSet
      debug.info "updated #{openHouseCount} open houses"

      # get properties after open houses, so that we can make sure the property exits
      count = await _getAndUpsertProperties queryTs
      debug.info "updated #{count} properties"

      # merge open houses and properties
      mergedCount = await mergeOpenHousesAndProperties propSet
      propSet.clear()
      debug.info "merged OpenHouse into #{count} properties"

      await MlsImportKeysCol.updateOne { _id: PROCESS_STATUS_ID },
      { $set: { next: nextQueryTs }},
      { upsert: true }

      await handleProcessStatus {
        status: RUNNING_NORMAL,
        nextTs: (Date.now() + SLEEP_MS + AVG_RUNNING_MS)
      }
      queryTs = nextQueryTs
    catch error
      debug.error error
      await handleProcessStatus {
        status: RUNNING_ERROR,
        error,
        nextTs: (Date.now() + SLEEP_MS + AVG_RUNNING_MS)
      }
    debug.info "sleep 15 minutes, then run mainLoop again"
    await sleep SLEEP_MS
    


main = () ->
  isForce = AVGS.includes('force')
  hasRunningProcess = await checkRunningProcess()
  if (not isForce) and hasRunningProcess
    return EXIT HAS_RUNNING_PROCESS

  importKey = await MlsImportKeysCol.findOne { _id: PROCESS_STATUS_ID }

  if (not importKey?.next)
    queryTs = new Date(DEFAULT_QUERY_TIMESTAMP)
    await initialQueryBack queryTs
  else if (Date.now() - importKey.next > REPLICATION_MODE_THRESHOLD)
    queryTs = importKey.next
    await initialQueryBack queryTs
  else
    queryTs = importKey.next
    processNextTs = Date.now() + AVG_RUNNING_MS
    await handleProcessStatus {
      status: RUNNING_NORMAL,
      startTs: Date.now(),
      nextTs: processNextTs
    }
    await mainLoop queryTs

main()


###
OpenHouse:
  BridgeModificationTimestamp:  2024-08-28T00:36:04.395Z
  FeedTypes
  ListingId:  ********
  ListingKey: 4524dce0da31795e40d3f8168c2bb2ce
  ListingKeyNumeric:  225316652
  LivestreamOpenHouseURL: null
  ModificationTimestamp:  2024-08-27T21:35:55.700Z
  OpenHouseDate:  2024-09-01
  OpenHouseEndTime: 2024-09-01T21:00:00.000Z
  OpenHouseId:  1430963
  OpenHouseKeyc:  7501af2b6158e1aaa48f597c4ef214a
  OpenHouseKeyNumeric:  1430963
  OpenHouseMethod:  null
  OpenHouseRemarks: Come take a walkthrough of this beautiful 2 storey located in a cul-de-sac just steps away from schools and walking trails.
  OpenHouseStartTime: 2024-09-01T19:00:00.000Z
  OpenHouseStatus:  Active
  OriginalEntryTimestamp: 2024-08-27T21:35:55.700Z

Property:
  PhotosChangeTimestamp:  2024-08-31T00:07:08.868Z
  PhotosCount:  12
###