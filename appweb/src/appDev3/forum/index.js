var forum = {
  data () {
    return {
      filterType: '',
      showSearchbar:false,
      search:'',
      datas:[
        'isCip',
        'isApp',
        'isLoggedIn',
        'lang',
        'forumAdmin',
        'sessionUser',
        'reqHost',
        'shareHost',
        'newsAdmin',
        'globalTags',
        'isVipRealtor',
        'canWebComment',
        'isAdmin',
        'isRealtor',
        'edmAdmin',
        'coreVer',
        'isEmailVerified',
        'userGroups'
      ],
      dispVar: {
        isLoggedIn:  false,
        lang:        'zh',
        isApp:       true,
        isCip:       false,
        forumAdmin: false,
        sessionUser: '',
        newsAdmin:false,
        globalTags:[],
        isVipRealtor:false,
        canWebComment: false,
        isAdmin: false,
        isRealtor:false,
        edmAdmin: false,
        userGroups:[]
      },
      forumLangs:[],
      flagPost:null,
      allPosts:[],
      posts_more: false,
      qna:[],
      qna_more: false,
      todayChoices: [],
      todayChoices_more: false,
      news:[],
      news_more: false,
      curCity: {},
      exCities: null,
      curPost: {},
      loading: false,
      displayPage: 'all',
      pgNum:1,
      scrollElement: null,
      waiting: false,
      refreshing: false,
      postid: null,
      tag: null,
      src: null,
      gid: null,
      gnm:'',
      tagList:[],
      showMask: false,
      showMenu: false,
      title:'RealMaster',
      blkUids:{},
      blkCmnts:{},
      showedForumTerms:true,
      showReportForm:false,
      reportForm:{
        postId:null,
        title: 'Report',
        feedurl:'/1.5/form/forminput',
        userForm:{
          m:'',
          // ueml:'<EMAIL>',
          tp:'feedback',
          subjectTp:'Forum Complaint',
          formid:'system',
          id:'',
          violation:'',
          tl:''
        }
      },
      menuMapping:{
        all:'News',
        topic: 'Topic',
        my_post: 'My Post',
        my_reply: 'My Reply',
        my_group: 'My Group',
        my_favourite: 'My Favourite',
        post_admin: 'My Admin Posts',
      },
      form:'news'
    };
  },
  beforeMount(){
    this.getTagList();
  },
  mounted () {
    this.$getTranslate(this);
    var self = this;

    // 检测设备类型，设置isApp标识
    if (typeof RMSrv === 'undefined' || !RMSrv) {
      self.dispVar.isApp = false;
    }

    // if(RMSrv && RMSrv.setBarColor){
    //   RMSrv.setBarColor({name:'mainTheme'})
    // }
    if (!localStorage.showedForumTerms) {
      self.showedForumTerms = false;
      setTimeout(()=>{
        self.showMask = true;
      },100);
    }
    try {
      this.blkUids = JSON.parse(localStorage.getItem('blkUids')) || {};
      this.blkCmnts = JSON.parse(localStorage.getItem('blkCmnts')) || {};
    } catch (error) {
      return window.bus.$emit('flash-message', error.toString());
    }

    // 只在app端启用下拉刷新功能
    self.$nextTick(function () {
      if (self.dispVar.isApp && typeof $ !== 'undefined') {
        $('#forum-containter').xpull({
         'callback':function(){
            self.reloadPosts();
         }
        });
      }
    });

    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var self = this;
    self.getPageData(self.datas, {}, true);
    window.bus.$on('pagedata-retrieved', function (d) {
      self.dispVar = Object.assign(self.dispVar, d);
      if (self.dispVar.sessionUser.splang) {
        self.forumLangs = self.dispVar.sessionUser.splang;
      }
      if(vars.tag){
        self.filterByTag(vars.tag)
      } else if (vars.city) {
        self.curCity = city;
      } else if (vars.section) {
        if (vars.group) {
          self.gid = vars.group;
          self.gnm = vars.gnm;
        }
        self.selectFilter(vars.section);
      } else {
        self.selectFilter('all');
      }
      if(vars.page && vars.postid){
        while (self.pgNum < vars.page) {
          self.pgNum+=1;
          var params = self.getSearchParmas();
          params.page = self.pgNum
          self.getAllPost(params);
        }
      }
      if(vars.flagModalFromHome){
        return self.getForumDetail(vars.postid,'flagModalFromHome')
      }
      if (vars.postid) {
        self.showPostView(vars.postid, vars.src, vars.gid);
      }
    });
    bus.$on('forum-view-close', function(d) {
      self.postid = null;
      function findPost(post) {
        return post._id == d._id;
      }
      var post = self.allPosts.find(findPost);
      if (post) {
        post.vc = post.vc+1;
        if (post.vcapp) {
          post.vcapp = post.vcapp+1;
        }
        post.cc = d.cc;
        post.vt = d.vt;
        post.fav = d.fav;
        post.cmntl = d.cmntl;
        post.realtorOnly = d.realtorOnly;
        post.hasUpdate = false;
      }
      if (d.readStatus=='read'&&self.displayPage!='my_group') {
        var postindex =self.allPosts.findIndex(findPost)
        self.allPosts.splice(postindex,1);
      }
      if (d.newPostId) {
        self.showPostView(d.newPostId,d.src,d.gid);
      }
      if (d.tag) {
        self.tag = d.tag;
        self.reloadPosts();
      }
      if (d.loadPosts) {
        self.reloadPosts();
      }
    });
  },
  methods: {
    scrollPostIntoView(){
      var currentIndex = 0;
      this.allPosts.forEach((post,idx)=>{
        if(post._id == vars.postid){
          return currentIndex = idx;
        }
      });
      if(currentIndex > 0){
        document.getElementsByClassName('forum-summary-card')[currentIndex].scrollIntoView();
        this.scrollElement = document.getElementById('forum-containter');
        var scrollTop = this.scrollElement.scrollTop;
        this.scrollElement.scrollTop = scrollTop-89;
      }
    },
    getTranslate:function(txt) {
      if(txt){
        return TRANSLATES[txt] || txt;
      }
    },
    clickMask() {
      this.showMask = false;
      this.showMenu = false;
      this.toggleFlagModal(false);
      this.closeSearchbar();
    },
    searchForums() {
      this.allPosts = [];
      this.showSearchbar = false;
      this.showMask = false;
      this.getAllPost(this.getSearchParmas());
    },
    openSearchBar() {
      this.showSearchbar = true;
      this.showMask = true;
    },
    closeSearchbar() {
      this.showSearchbar = false;
      this.showMask = false;
    },
    handleForumLangs(lang) {
      var forumLangs = this.forumLangs;
      var idx = forumLangs.indexOf(lang);
      if (idx > -1) {
        forumLangs.splice(idx,1);
      } else {
        forumLangs.push(lang);
      }
    },
    _agreeTerms() {
      localStorage.showedForumTerms = true;
      this.showedForumTerms = true;
      this.showMask = false;
    },
    agreeTerms() {
      var self = this;
      if (!self.dispVar.isLoggedIn) {
        self._agreeTerms();
        return;
      }
      axios.post('/1.5/forum/updateSplang', {splang:self.forumLangs})
      .then((ret)=>{
        ret = ret.data
        if (ret.ok) {
          self._agreeTerms();
        } else {
          return window.bus.$emit('flash-message', ret.e);
        }
      }).catch(()=> {
        console.error(err.status+':'+err.statusText);
      });
    },
    displayBlockName(user) {
      var lang = this.dispVar.lang || 'en';
      if (lang == 'en') {
        return user.nm_en || user.nm || user.nm_zh;
      } {
        return user.nm_zh || user.nm || user.nm_en;
      }
    },
    back(){
      // NOTE: open detail and close and back freezes on android, maybe caused by ajax cmds;
      // window.history.back();
      if (vars.isPopup) {
        return window.rmCall(':ctx::cancel');
      }
      var url = vars.d || '/1.5/index';
      this.goTo(url)
    },
    dropDownClicked() {
      this.showMenu = true;
      this.showMask = true;
    },
    getTagList(cb) {
      var self = this;
      axios.get('/1.5/forum/tags?type=all')
      .then((ret)=>{
        ret = ret.data
        if (ret.ok) {
          var tagList = ret.tags || [];
          tagList.sort((a,b)=>{
            if(!a.sort) return 1;
            if(!b.sort) return -1;
            return a.sort - b.sort;
          });
          self.tagList = tagList;
          if (cb)
            cb()
        } else {
          return window.bus.$emit('flash-message', ret.e);
        }
      }).catch(()=> {
        console.error(err.status+':'+err.statusText);
      });
    },
    filterByTag(tag) {
      if (['property','post','sch'].indexOf(tag)>=0) {
        this.src = tag;
        this.tag = null;
      } else {
        this.src = null;
        this.tag = tag;
      }
      trackEventOnGoogle(this.form,'filterByTag',this.src,this.tag)
      this.reloadPosts();
    },
    clearTag(type) {
      switch (type) {
        case 'all':
          this.tag = null;
          this.src = null;
          this.curCity = {};
          break;
        case 'tag':
          this.tag = null;
          break;
        case 'gid':
          this.gid = null;
          break;
        case 'src':
          this.src = null;
          break;
        case 'city':
          this.curCity = {};
          break;
      }
      this.reloadPosts();
    },
    goTo(url) {
      window.location.href = url;
    },
    goToEdit() {
      var url;
      url = "/1.5/forum?section="+this.displayPage;
      if(vars.d)
        url = url+'&d='+vars.d;
      var url ='/1.5/forum/edit?d='+encodeURIComponent(url);
      trackEventOnGoogle(this.form,'goToEdit')
      this.goTo(url);
    },
    getSearchParmas() {
      var params = {};
      var self = this;
      if (self.search) {
        params.search = self.search;
      }
      if(['my_post','topic','my_reply','my_favourite','my_reply','my_group'].indexOf(self.displayPage)>-1) {
        params.category = self.displayPage;
      }  else if(self.displayPage=='post_admin') {
        params.src = "post";
      }
      if (this.curCity && this.curCity.o) {
        params.city = this.curCity.o;
      }
      if (this.gid) {
        params.gid = this.gid;
      }
      if (this.curCity && this.curCity.p) {
        params.prov = this.curCity.p;
      }
      if (this.curCity && this.curCity.cnty) {
        params.cnty = this.curCity.cnty;
      }
      if(this.tag)
        params.tag = this.tag;
      if (['property','post','psch','sch'].indexOf(this.src)>=0 ) {
        params.src = this.src;
      }
      if(vars.page && vars.postid && (self.pgNum == vars.page)){
        params.scroll = true;
      }
      return params;
    },
    clearModals() {
      this.filterType = '';
    },
    close() {
      this.showMask = false;
      this.showMenu = false;
      this.toggleFlagModal(false);
    },
    selectFilter (param) {
      var self = this;
      self.title = self.menuMapping[param];
      self.close();
      self.allPosts = [];
      self.displayPage = self.filterType;
      self.filterType = '';
      this.tag = null;
      this.src = null;
      // self.gid = null;
      this.curCity = {};
      self.displayPage = param;
      trackEventOnGoogle(this.form,'selectFilter',param)
      self.getAllPost(self.getSearchParmas());
    },
    goToForumList(src) {
      var url = "/1.5/forum/list?src="+src;
      if (['sticky','qna','post'].includes(src)){
        url+="&city=" + this.curCity.o + '&prov=' + this.curCity.p;
      }
      if (src=='post_admin')
        url = "/1.5/forum/list?src=post";
      window.location.href=url
    },
    getForumDetail(postid,from) {
      var self = this;
      trackEventOnGoogle(this.form,'forumDetail')
      axios.post('/1.5/forum/detail/' + postid,{})
      .then(ret=>{
        ret = ret.data
        if (ret.ok) {
          if(from == 'flagModalFromHome'){
            ret.post.author={}
            ret.post.author.isAdmin = vars.authorIsAdmin || false
            if(vars.authorId){
              ret.post.author._id = vars.authorId
            }
            self.toggleFlagModal(ret.post)
          }
        }
      })
      .catch(err=>{
        self.loading = false;
        console.error(err.status+':'+err.statusText);
      })
    },
  },
  computed:{
    computedWith(){
      let w = window.innerWidth*0.78-24;
      return w+'px';
    },
    hasTag:function() {
      return this.tag||this.gid||['property','post','sch','psch'].indexOf(this.src)>=0||this.curCity.o||this.curCity.p||this.curCity.cnty
    },
    noTag: function() {
      return {
        tag: this.tag,
        property: this.src=='property',
        psch: this.src=='psch',
        sch: this.src=='sch',
        topic: this.displayPage == 'topic',
        city: this.curCity.o,
        gid: this.gid
      }
    }
  },
};
initUrlVars();
var app = Vue.createApp(forum);
app.mixin(rmsrvMixins);
app.mixin(pageDataMixins);
app.mixin(forumMixins);
app.mixin(forumCommonMixins);
app.component('forum-summary-card', ForumSummaryCard);
app.component('report-forum-form', ReportForumForm);
app.component('flash-message', flashMessage);
app.component('block-popup', BlockPopup);
app.component('check-notification', checkNotification);
app.config.globalProperties.$filters = {currency:filters.currency}
trans.install(app,{ref:Vue.ref})
app.mount('#forumHome');