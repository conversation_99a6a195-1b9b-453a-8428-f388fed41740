

function scrollBar(){
  var navLinksContainer = document.querySelector('.list-nav-container');
  if (!navLinksContainer) {
    return;
  }
  var navLinkSelected = document.querySelector('.list-nav-link.selected');
  var navLinkActive = document.querySelector('.list-nav-active');
  var boundData = navLinkSelected.getBoundingClientRect();
  var scroll = 0;
  scroll = boundData.left + (boundData.width)/2 - window.innerWidth/2;
  navLinksContainer.scrollLeft = scroll;
  navLinkActive.style.left = boundData.left + (boundData.width)/2 - 15 +'px';
};
function goBack(d){
  d ? document.location.href = d : window.history.back();
};
function goMapView () {
  if (window.showMapView){
    window.showMapView();
  }else{
  }
};
function goNotesMap () {
  let url = '/1.5/map/notesMap';
  let cfg = {toolbar:false};
  RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
    if (val == ':cancel') {
      return;
    }
    try {
    } catch (e) {
      console.error(e);
    }
  });
};
function gotoStatistic(){
  let url = '/1.5/notes/statistic';
  let cfg = {toolbar:true};
  RMSrv.getPageContent(url,'#callBackString', cfg, function(val){
    if (val == ':cancel') {
      return;
    }
    try {
    } catch (e) {
      console.error(e);
    }
  });
};
window.addEventListener('load',()=>{
  scrollBar();
})
var baseMixins={
  mounted(){
    // if(RMSrv && RMSrv.setBarColor){
    //   RMSrv.setBarColor({name:'mainTheme'})
    // }
  },
  methods:{
    serializeData: function (cfg={}) {
      if ('object' !== typeof cfg.data) {
        return '';
      }
      var obj = cfg.data;
      var prefix = cfg.prefix;
      var str = "";
      for (var key in obj) {
        if (str != "") {
          str += "&";
        }
        var value = obj[key];//handle value is 0
        if (value == null || value == undefined) {
          value = null;
        }
        str += prefix+'-'+key + "=" + encodeURIComponent(value);
      }
      return str;
    },
    getTranslate:function(txt) {
      return TRANSLATES[txt] || txt;
    },
    confirmVip: function (lang, optTip) {
      optTip = optTip?optTip:"vipTip";
      var tip = this.getTranslate(optTip);
      var later = this.getTranslate('Later');
      var seemore = this.getTranslate('SeeMore');
      function _doShowVip(idx) {
        var url = 'https://www.realmaster.ca/membership'
        if (idx+'' == '2') {
          RMSrv.showInBrowser(url);
        }
      }
      return RMSrv.dialogConfirm(tip, _doShowVip, "VIP", [later, seemore]);
    },
  },
};