var notesMap = {
  data(){
    return{
      mapObj: null,
      notes:[],
      curProps:[],
      noteMarkerGroupName:'notes',
      showPreview: false,
      curMarkId: '',
      mapDispMode:'ROADMAP',
      cMarker:0
    }
  },
  mounted(){
    let self = this;
    self.$getTranslate(self);
    if (!window.bus) {
      console.error('global bus is required!');
      return;
    }
    var bus = window.bus
    self.initGmap();
    // REF: webMap.vue
    bus.$on(self.noteMarkerGroupName + "MarkerClicked",(ids)=>{
      self.markerClick(ids)
    });
    bus.$on('close-preview',function(d){
      self.showPreview = false;
    });
  },
  methods:{
    markerClick(ids){
      var self = this;
      var id = ids[0];
      // 再次点击已点击的标记点则关闭房源卡片
      if (self.showPreview && self.curMarkId.length != 0 && self.curMarkId == id){
        self.showPreview = false;
        return
      }
      let index = self.notes.findIndex((value)=>{
        return value._id == id
      })
      self.curProps = self.notes[index].props;
      self.curMarkId = id;
      self.showPreview = true;
    },
    getIconChar(l) {
      if (l.isProj) {
        l.saletp_en = ['Sale'];
        l.ptype2_en = [(l.tp1||''),(l.tp2||'')];
        if (!(l.tp1 || l.tp2)) {
          return 'P';
        }
        if (/Office|Retail/.test(l.tp1)) {
          l.pclass = ['b'];
        }
      }
      var saletp = l.saletp_en || l.saletp || '';
      var ptype2 = l.ptype2_en || l.ptype2;
      if (saletp.indexOf('Sale') > -1) {
        var word = "none", ptype2 = ptype2.join(',');
        // SDCBTL
        if (/Semi-/.test(ptype2)) {
          word = 'S';
        } else if (/Detached|Det\sComm/.test(ptype2)) {
          word = 'D';
        } else if (/Apt|Apartment|Condo/.test(ptype2)) {
          word = 'C';
        } else if (/Att|Townhouse/.test(ptype2)) {
          word = 'T';
        } else if (l.ptype == 'Commercial') {
          word = 'B';
        }
      } else {
        word = 'L';
      }
      return word;
    },
    getLabelFunc(objs){
      var self = this;
      if (objs.length && objs.length > 1) {
        return objs.length+'';
      }
      var tp = this.getIconChar(objs[0]);
      if (tp == 'none') {
        tp = ''
      }
      if (tp) {
        tp = self.$_(tp,'map label char');
      }
      var prop = objs[0];
      if (prop.isProj) {
        prop.lpr = prop.lpf;
      }
      return tp+filters.propPrice(prop.lp || prop.lpr);
    },
    getNoteIcon(objs, selected){
      let url = '/img/mapmarkers';
      let itemCount = {
        url: `${url}/none-default.png`,
        scaledSize:[24,24],
        addedStyle:'display: flex;align-items: center;justify-content: center;'
      }
      if (!selected && objs && objs[0].props.length > 1){
        return itemCount
      }
      if (selected && objs && objs[0].props.length > 1){
        itemCount.url = `${url}/none-sel.png`;
        return itemCount
      }
      if (!selected && objs && objs[0].props.length == 1){
        return `${url}/price.png`;
      }
      if (selected && objs && objs[0].props.length == 1){
        return `${url}/sel-price.png`;
      }
    },
    zoomIn(){
      let self = this;
      self.mapObj.zoomIn();
    },
    zoomOut(){
      let self = this;
      self.mapObj.zoomOut();
    },
    locateMe(){
      var self = this;
      self.mapObj.locateMe(null,(loc) => {
        if (!self.cMarker) {
          var pos = new mapboxgl.LngLat(loc[1],loc[0]);
          var el = document.createElement('img');
          el.setAttribute('src', '/img/mapmarkers/umarker.png');
          el.setAttribute('style','width:25px;height:25px;z-index:2');
          self.cMarker = new mapboxgl.Marker({element:el,anchor:'bottom'}).setLngLat(pos).addTo(self.mapObj.gmap);
        }
      });
    },
    setMapTypeId(){
      var self = this;
      if (!self.mapObj) {
        return;
      }
      if (self.mapDispMode == 'ROADMAP') {
        self.mapDispMode = 'HYBRID';
      } else {
        self.mapDispMode = 'ROADMAP';
      }
      return self.mapObj.setMapTypeId(self.mapDispMode);
    },
    addLatLngToNotes(props=[]){
      let noteProp=[];
      for(let i=0,len=props.length;i<len;i++){
        let tempProps = {props:[]};
        let index = noteProp.findIndex((value)=>{
          return value.lat.toString() == props[i].lat.toString() && value.lng.toString() == props[i].lng.toString()
        });
        if(index == -1){
          tempProps._id = props[i]._id;
          tempProps.props.push(props[i]);
          tempProps.lat = props[i].lat;
          tempProps.lng = props[i].lng;
          tempProps.label = {text:this.getLabelFunc(tempProps.props),color:'white',fontSize:'10px'};
          noteProp.push(tempProps)
        }else{
          let indexProp = noteProp[index].props.findIndex((value)=>{
            return value._id == props[i]._id
          });
          if(indexProp == -1){
            noteProp[index].props.push(props[i]);
            noteProp[index].label = {text:this.getLabelFunc(noteProp[index].props),color:'white',fontSize:'10px'};
          }
        }
      }
      return noteProp
    },
    getNotesData(){
      let self = this;
      fetchData('/1.5/notes/savedList',{body:{model:'map'}},(err,ret)=>{
        if (err || !ret.ok) {
          let msg = err? self.$_('Error'):self.$_(ret.e);
          return bus.$emit('flash-message',msg);
        }
        this.notes = this.addLatLngToNotes(ret.result.propList);
        self.setMarkersPot();
      });
    },
    setMarkersPot(){
      let self = this;
      self.mapObj.fitBounds(self.notes);
      self.mapObj.setMarkers(self.noteMarkerGroupName, self.notes, self.getNoteIcon,null,null);
    },
    initGmap () {
      var self = this,bus = window.bus,style;
      var address, geocoder, lat, ll, lng, map, marker, opts, msgQueue;
      map = void 0;
      marker = void 0;
      opts = void 0;
      geocoder = void 0;
      address = "Mississauga, ON, Canada";
      msgQueue = [];
      self.sendMsg = (event, obj) => {
        if(bus){
          bus.$emit(event,obj)
        }else if (self) {
            self.$emit(event,obj);
          return ;
        } else {
          return msgQueue.push({
            e: event,
            m: obj
          });
        }
      };
      var opt = {
        bndsChanged: self.bndsChanged,
        mapTypeControl: true,
        sendMsg: self.sendMsg,
        defaultIDName: '_id',
        vueSelf: self,
        hfHeight: 44,
        defaultCmarkerIcon:true,
      };
      self.mapObj = self.getMapObject(opt);
      self.mapObj.init('id_d_map');
      setTimeout(() => {
        self.getNotesData();
      }, 500);
    },
    goBackNotes(){
      window.rmCall(':ctx::cancel');
    }
  }
}
initUrlVars();
var app = Vue.createApp(notesMap);
trans.install(app,{ref:Vue.ref});
app.mixin(map_mixins);
app.component('preview-info', previewInfo);
app.component('flash-message', flashMessage);
app.mount('#notesMap');