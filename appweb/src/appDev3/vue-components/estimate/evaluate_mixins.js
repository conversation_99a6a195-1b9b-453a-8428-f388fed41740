var evaluateMixins = {
  created :  function () {
  },
  data:function() {
    return {
      numFields:['range','range_r','last','last_r','st_num','bdrms','bthrms','br_plus','reno','gr','tax','sqft','sqft1','sqft2','depth','front_ft'],
      fields:['range','lp','range_r','last','last_r','city', 'cnty','lng','lat','prov','addr','reno','st','st_num','cmty','mlsid','bdrms','bthrms','tp','br_plus','gr','tax','sqft','thumbUrl','unt','sqft1','sqft2','depth','front_ft','lotsz_code','irreg'],
    }
  },
  mounted() {
  },
  methods : {
    getFormatAddr(addr, prop) {
      if (addr)   {
        return addr;
      } else if (!prop.lat || !prop.lng) {
        return '';
      } else {
        return prop.lat.toString().substr(0,7) + ',' + prop.lng.toString().substr(0,7);
      }
    },
    goBack() {
      window.history.back();
    },
    getGoolgeStreeViewImg(dispVar, prop, cb){
      var self = this;
      var url = dispVar.streetView+'&location='+prop.lat+','+prop.lng;
      var metaurl = dispVar.streetViewMeta+'&location='+prop.lat+','+prop.lng;
      self.$http.get(metaurl).then(function(ret) {
        console.log(ret);
        if(ret.body.status=='OK') {
          cb(url);
        } else {
          cb(null);
        }
      },function(){
        cb(null);
      });
    },
    removeHist(e, uid, uaddr, histid, msg, cb) {
      e.stopPropagation();
      var self = this;
      var _do = function(idx) {
        if (idx+'' != '2') {
          return;
        }
        var params = {uid: uid, uaddr:uaddr}
        if (histid)
          params.id = histid
        fetchData('/1.5/evaluation/delete', {body:params},function(err,ret) {
          if (err){
            console.log(err)
            self.msg = "error";
          }else{
            if (ret.ok == 1) {
              cb();
            } else {
              console.log(ret.e)
              self.msg = "error";
            }
          }
        })
      };

      RMSrv.dialogConfirm(msg,
        _do,
        this.getTranslate('Message'),
        [this.getTranslate('Cancel'),this.getTranslate('Confirm')]
      );
    },
    formatTs(ts) {
      return formatDate(ts);
    },

    getIds(list) {
      var ids = [];
      for (let el of list) {
        ids.push(el._id);
      }
      return ids;
    },
    // goToEvaluate(reEvaluate) {
    //   if (this.share || !this.dispVar.isApp) {
    //     document.location.href='/app-download';
    //     return;
    //   }
    //   if (reEvaluate) {
    //     var url = '/1.5/evaluation/comparables.html?nobar=1&inframe=1';
    //     url = url + '&' + this.buildUrlFromProp();
    //     url += "&reEval=1"
    //     if (this.inclIds) {
    //       url += '&ids=' + this.inclIds.join(',')
    //     }
    //     if (this.rentalInclIds) {
    //       url += '&rentalids=' + this.rentalInclIds.join(',')
    //     }
    //     url = RMSrv.appendDomain(url);
    //     RMSrv.openTBrowser(url, {nojump:true, title:this._('Evaluation Conditions','evaluation')});
    //     return;
    //   }
    //   var url = '/1.5/evaluation/evaluatePage.html?1=1';
    //   if (vars.fromMls) {
    //     url += '&fromMls=1';
    //   }
    //   if (this.fromHist || this.fromMls) {
    //     url = RMSrv.appendDomain(url);
    //     RMSrv.closeAndRedirectRoot(url);
    //   } else {
    //     url = url +'&nobar=1&inframe=1';
    //     url = RMSrv.appendDomain(url);
    //     RMSrv.openTBrowser(url, {nojump:true, title:this._('Evaluation Conditions','evaluation')});
    //   }
    // },
    appendDomain(url){
      var location = window.location.href;
      var arr = location.split("/");
      var domain = arr[0] + "//" + arr[2];
      url = domain + url;
      return url;
    },

    openHistPage(uaddr, addr) {
      var url = '/1.5/evaluation/histPage.html?uaddr=' + uaddr+'&inframe=1';
      if(this.fromMls) {
        url = url + '&fromMls=1';
      }
      if (this.share) {
        url = url + '&share=1';
      } else {
        url =  url + '&nobar=1';
      }
      url = this.appendDomain(url);
      if(this.dispVar.isApp) {
        var title = addr.trim(25)
        RMSrv.openTBrowser(url, {nojump:true, title:title});
      } else {
        window.document.location.href = url;
      }
    },
    getMaxDist() {
      var self = this;
      self.$http.get('/1.5/evaluation/maxDistRange?type='+self.prop.tp).then(function(ret){
        if(ret) {
          ret = ret.data;
          self.max_dist_range = ret.max_dist_range;
        }
      },function(){});
    },
    // openPropPage(prop) {
    //   if (this.dispVar.isApp) {
    //     let url = '/1.5/prop/detail/inapp?id='+ prop._id+'&mode=map&inframe=1&showShareIcon=1';
    //     url = this.appendDomain(url);
    //     RMSrv.openTBrowser(url,{nojump:true, title:this._('RealMaster')});
    //   } else {
    //     let url = '/1.5/prop/detail?id='+ prop._id+'&inframe=1&noeval=1';
    //     if(!vars.share) {
    //       url = url+ '&nobar=1'
    //     }
    //     url = this.appendDomain(url);
    //     window.location.href = url;
    //   }
    // },
    compareDifference(prop) {
      var self = this;
      if (typeof prop.bdrms == 'number' && typeof self.prop.bdrms == 'number') {
        prop.bdrms_diff = prop.bdrms - self.prop.bdrms
      }
      if (typeof prop.bthrms == 'number' && typeof self.prop.bthrms == 'number') {
        prop.bthrms_diff = prop.bthrms - self.prop.bthrms
      }
      if (typeof prop.gr == 'number' && typeof self.prop.gr == 'number') {
         prop.gr_diff = prop.gr - self.prop.gr
      }
      if (prop.lotsz_code == self.prop.lotsz_code && typeof prop.depth == 'number' && typeof prop.front_ft == 'number' && typeof self.prop.depth == 'number' && typeof self.prop.front_ft == 'number') {
        prop.front_ft_diff = Math.round(prop.front_ft - self.prop.front_ft);
        prop.size_diff = parseInt(prop.front_ft * prop.depth - self.prop.front_ft * self.prop.depth);
      }
      if (typeof prop.sqft == 'number' && typeof self.prop.sqft == 'number') {
        prop.sqft_diff = parseInt(prop.sqft - self.prop.sqft)
      } else if (typeof prop.sqft1 == 'number' && typeof prop.sqft2 == 'number' && typeof self.prop.sqft == 'number') {
        prop.sqft1_diff = parseInt(prop.sqft1 - self.prop.sqft)
        prop.sqft2_diff = parseInt(prop.sqft2 - self.prop.sqft)
      } else if (typeof prop.sqft1 == 'number' && typeof prop.sqft2 == 'number' && typeof self.prop.sqft1 == 'number'&&typeof self.prop.sqft2 == 'number') {
        prop.sqft1_diff = parseInt(prop.sqft1 - self.prop.sqft1)
        prop.sqft2_diff = parseInt(prop.sqft2 - self.prop.sqft2)
      }
      if (prop.st && prop.st == self.prop.st && prop.prov == self.prop.prov && prop.city == self.prop.city) {
        prop.sameStreet = true;
      }
      if ((vars.fromMls || self.prop.mlsid) && !prop.sameStreet && prop.cmty && prop.cmty == self.prop.cmty && prop.prov == self.prop.prov && prop.city == self.prop.city) {
        prop.sameCmty = true;
      }
      return prop;
    },
    openPropModal(prop) {
      var url = '/1.5/evaluation/listing.html?lat='+prop.lat+'&lng='+prop.lng + '&inframe=1';
      if (!vars.share) {
          url = url +'&nobar=1';
      }
      url = RMSrv.appendDomain(url);
      if (this.dispVar.isApp) {
        var title = prop.addr.trim(25);
        RMSrv.openTBrowser(url, {nojump:true, title:prop.addr});
      } else {
        window.location.href = url;
      }
    },
    // getReno(reno) {
    //   var reno_options = [
    //     {text:'Poor',value:1},
    //     {text:'Below Average',value:2},
    //     {text:'Average',value:3},
    //     {text:'Above Average',value:4},
    //     {text:'Very Good',value:5},
    //   ]
    //   for (let i of reno_options)
    //     if (i.value == reno)
    //       return this._(i.text,'evaluation');
    //   return this._('Average','evaluation');
    // },
    getPropCnt:function(addr, cb) {
      var self = this;
      if (!addr.uaddr && !(addr.lat && addr.lng)) {
        return;
      }
      fetchData('/1.5/evaluation/propcnt', {body:addr},function(err,ret) {
        if (err){
          console.log(err)
          self.msg = "error";
        }else{
          cb(ret);
        }
      })
    },
    getPropFromVars(vars) {
      if (!vars)
        vars = window.vars;
      var prop = {};
      for (let key of this.fields) {
        if (vars[key]){
          if (key == 'thumbUrl') {
            prop[key] = decodeURIComponent(vars[key])
          } else if(this.numFields.indexOf(key) >=0 ) {
            prop[key] = Number(vars[key]);
          } else {
            prop[key] = vars[key];
          }
        }
      }
      return prop;
    },
    buildUrlFromProp() {
      var arr = []
      for (let key of this.fields) {
        if (this.prop[key]) {
          if (key == 'thumbUrl') {
            arr.push(key+'='+encodeURIComponent(this.prop.thumbUrl))
          } else {
            arr.push(key+'='+this.prop[key])
          }
        }
      }
      return arr.join('&')
    }
  }
};
// export default evaluateMixins;
