<!-- Generated by SpecStory -->

# 为 claim.coffee 添加单元测试 (2025-06-17 18:01Z)

_**User**_

查看 @claim.coffee  和 @propClaim.coffee 文件中增加的内容，给 @claim.coffee 文件添加 unit test

---

_**Assistant**_

好的，我会按照你的要求进行操作。你的需求是：

1. **查看 `claim.coffee` 和 `propClaim.coffee` 文件中增加的内容**  
2. **为 `claim.coffee` 文件中增加的内容补充单元测试**

### 步骤说明

#### 1. 查看 claim.coffee 和 propClaim.coffee 增加的内容
- 你提供的 `claim.coffee` 和 `propClaim.coffee` 文件内容已经很完整，且都带有详细的注释。
- 主要的业务逻辑和接口都在 `claim.coffee` 的 `Claim` 类方法中实现，`propClaim.coffee` 作为接口层调用这些方法。

#### 2. 检查现有单元测试
- 你已经有 `appweb/src/unitTest/claim/claim.coffee`，里面包含了大量接口和模型的测试用例，覆盖了大部分接口和边界情况。

#### 3. 查找 claim.coffee 新增/未覆盖的内容
- 需要重点关注 `claim.coffee` 中的新增方法或未被单元测试覆盖的逻辑。
- 结合 `propClaim.coffee` 的接口调用，确认是否有遗漏的业务分支或特殊情况未被测试。

#### 4. 为 claim.coffee 增加单元测试
- 按照你的要求，所有新增测试都要带有详细的 JsDoc 注释，解释每个测试块的功能和实现逻辑。

---

### 具体操作

#### 1. claim.coffee 主要方法梳理
- `buildUpdate`
- `saveBaseOnPc`
- `updateTokenIdById`
- `checkSaveClaim`
- `getById`
- `getList`
- `getMapPids`
- `claimedPc`
- `deleteById`
- `checkIfCanClaim`

#### 2. 检查现有单元测试覆盖情况
- 现有测试已经覆盖了：
  - 正常认领/删除
  - 参数缺失
  - 权限校验
  - 认领边界（如 100%/超时/无效状态等）
  - getById、getList、deleteById、checkIfCanClaim 等
- 但部分内部方法如 `buildUpdate`、`getClaimTokenKeys`、`claimedPc`、`getMapPids` 可能未直接测试。

#### 3. 计划补充的测试点
- `buildUpdate` 的参数校验和返回结构
- `getClaimTokenKeys` 的 key 生成逻辑
- `claimedPc` 的用户百分比聚合
- `getMapPids` 的 pid 去重逻辑

---

### 4. 编写单元测试（示例）

我将为 `claim.coffee` 新增/未覆盖的关键方法补充单元测试，并加上详细的 JsDoc 注释。

接下来我会在 `appweb/src/unitTest/claim/claim.coffee` 文件末尾添加如下内容：  
- `describe('ClaimModel.buildUpdate', ...)`
- `describe('ClaimModel.getClaimTokenKeys', ...)`
- `describe('ClaimModel.claimedPc', ...)`
- `describe('ClaimModel.getMapPids', ...)`

每个 describe 块都带有详细注释。

现在开始添加代码。

---

```diff
          should.deepEqual(test.result,result)
          return
+ 
+ ###############################################
+ # 以下为 ClaimModel 新增/未覆盖方法的单元测试
+ ###############################################
+ 
+ ###
+ # @function ClaimModel.buildUpdate
+ # @description 测试 buildUpdate 方法的参数校验和返回结构，确保生成的 updateList 正确。
+ # <AUTHOR>
+ ###
+ describe 'ClaimModel.buildUpdate', ->
+   it 'should return correct updateList for valid input', ->
+     body = {
+       uid: '601a99501d0ae231345daf14',
+       pid: 'TRBW5473866',
+       mergedIds: ['TRBW5473866'],
+       mergedTopId: 'TRBW5473866',
+       sldd: 20240101,
+       saletp: 'Sale',
+       coop: {
+         side: 'coop',
+         m: 'test',
+         people: [
+           {
+             _id: '5f9c25d55d2bb0601b9c141a',
+             role: 'Buyer',
+             source: 'RM Direct',
+             pcOfShr: '100',
+             sourceKey: 'Firsthand'
+           }
+         ]
+       }
+     }
+     updateList = ClaimModel.buildUpdate(body)
+     updateList.should.be.an.Array()
+     updateList.length.should.be.above(0)
+     updateList[0].should.have.properties(['query','set','tokenQuery'])
+ 
+   it 'should return error if people missing sourceKey or pcOfShr', ->
+     body = {
+       uid: '601a99501d0ae231345daf14',
+       pid: 'TRBW5473866',
+       mergedIds: ['TRBW5473866'],
+       mergedTopId: 'TRBW5473866',
+       sldd: 20240101,
+       saletp: 'Sale',
+       coop: {
+         side: 'coop',
+         m: 'test',
+         people: [
+           {
+             _id: '5f9c25d55d2bb0601b9c141a',
+             role: 'Buyer',
+             source: 'RM Direct'
+             # 缺少 pcOfShr/sourceKey
+           }
+         ]
+       }
+     }
+     updateList = ClaimModel.buildUpdate(body)
+     updateList.should.have.property('err')
+ 
+ ###
+ # @function ClaimModel.getClaimTokenKeys
+ # @description 测试 getClaimTokenKeys 方法，确保生成的 token key map 正确。
+ ###
+ describe 'ClaimModel.getClaimTokenKeys', ->
+   it 'should return token keys map with Sale and Rent', ->
+     keysMap = ClaimModel.tokenKeysMap
+     keysMap.should.have.properties(['Sale','Rent'])
+     keysMap['Sale'].should.match(/claimSale/)
+     keysMap['Rent'].should.match(/claimRent/)
+ 
+ ###
+ # @function ClaimModel.getMapPids
+ # @description 测试 getMapPids 方法，确保返回的 pid 数组去重。
+ ###
+ describe 'ClaimModel.getMapPids', ->
+   it 'should return unique pid array', (done) ->
+     @timeout(10000)
+     # 这里用 admin 用户，body 为空，isNoteAdmin true
+     ClaimModel.getMapPids({uid:'601a99501d0ae231345daf14',body:{},isNoteAdmin:true})
+       .then (pids) ->
+         pids.should.be.an.Array()
+         # pid 应该唯一
+         (new Set(pids)).size.should.be.exactly(pids.length)
+         done()
+       .catch (err) ->
+         should.not.exists err
+         done()
+ 
+ ###
+ # @function ClaimModel.claimedPc
+ # @description 测试 claimedPc 方法，确保用户百分比聚合正确。
+ ###
+ describe 'ClaimModel.claimedPc', ->
+   it 'should return userMap with correct structure', (done) ->
+     @timeout(10000)
+     # 取一组已知 pid
+     ClaimModel.getMapPids({uid:'601a99501d0ae231345daf14',body:{},isNoteAdmin:true})
+       .then (pids) ->
+         return ClaimModel.claimedPc(pids,'en')
+       .then (userMap) ->
+         userMap.should.be.an.Object()
+         # userMap[pid][side][uid] 结构
+         Object.keys(userMap).length.should.be.above(0)
+         done()
+       .catch (err) ->
+         should.not.exists err
+         done()
```

---

已为 `claim.coffee` 文件中未直接被接口测试覆盖的关键方法（如 `buildUpdate`、`getClaimTokenKeys`、`getMapPids`、`claimedPc`）补充了详细的单元测试，并且每个测试块都带有 JsDoc 风格注释，解释了测试的功能和实现逻辑。

如需进一步细化或补充其他方法的测试，请告知！

---

_**User**_

@propClaim.coffee 文件新添加的post 增加unit test

---

_**Assistant**_

明白，你的需求是：**为 `propClaim.coffee` 文件中新添加的 POST 接口补充单元测试**，并且要有详细的 JsDoc 注释。

---

## 1. 新增 POST 接口梳理

根据 `appweb/src/apps/80_sites/AppRM/prop/propClaim.coffee` 文件，主要 POST 接口有：

- `POST 'edit'` —— 编辑/认领房源
- `POST 'delete'` —— 删除认领
- `POST 'detail'` —— 检查认领详情
- `POST 'list'` —— 获取认领列表
- `POST 'map'` —— 获取认领相关房源的 proplist

这些接口大部分已有一定的单元测试覆盖（见 `appweb/src/unitTest/claim/claim.coffee`），但 `POST 'map'` 相关的接口单测覆盖较少，且未见有专门针对 map 的测试。

---

## 2. 计划补充的测试点

- **POST /propClaim/map**  
  - 权限校验（未登录、普通用户、inreal、admin）
  - 返回结构校验（ok、items 为数组/对象）
  - 参数校验（body 合法/非法）

---

## 3. 测试代码设计

- 路径：`appweb/src/unitTest/claim/claim.coffee`
- 在文件末尾添加 `describe '/propClaim/map'` 测试块
- 每个测试用例都带有详细的 JsDoc 注释

---

## 4. 开始补充代码

我将为 `/propClaim/map` 接口补充单元测试，并加上详细注释。

---

```diff
          should.not.exists err
          done()
+ 
+ ###
+ # @api POST /propClaim/map
+ # @description 测试 propClaim.coffee 新增的 map 接口，覆盖权限、参数、返回结构等。
+ # <AUTHOR>
+ ###
+ describe '/propClaim/map', ->
+   users = ['nouser','user','inreal','admin']
+   tests = [
+     {
+       desc: 'should reject if not logged in',
+       user: 'nouser',
+       post: {},
+       expected: { ok: 0, e: 'Need login' }
+     },
+     {
+       desc: 'should reject if user is not inreal or admin',
+       user: 'user',
+       post: {},
+       expected: { ok: 0, e: 'Not Authorized' }
+     },
+     {
+       desc: 'should return items for inreal',
+       user: 'inreal',
+       post: {},
+       expected: { ok: 1, items: true }
+     },
+     {
+       desc: 'should return items for admin',
+       user: 'admin',
+       post: {},
+       expected: { ok: 1, items: true }
+     },
+     {
+       desc: 'should reject if agent param for inreal',
+       user: 'inreal',
+       post: { agent: '601a99501d0ae231345daf14' },
+       expected: { ok: 0, e: 'Not Authorized' }
+     },
+     {
+       desc: 'should return items for admin with agent param',
+       user: 'admin',
+       post: { agent: '601a99501d0ae231345daf14' },
+       expected: { ok: 1, items: true }
+     }
+   ]
+   tests.forEach (test) ->
+     it test.user+' '+test.desc, (done) ->
+       @timeout(30000)
+       request
+         .post('propClaim/map')
+         .set('Accept', 'application/json')
+         .set('Cookie', ['apsv=appDebug'])
+         .set('Cookie', helpers.userCookies[test.user])
+         .expect('Content-Type', /json/)
+         .send(test.post)
+         .end (err, res) ->
+           if test.expected.ok is 1
+             res.body.ok.should.be.exactly(1)
+             res.body.should.have.property('items')
+             # items 应为数组或对象且非空
+             Object.keys(res.body.items).length.should.be.above(0)
+           else
+             res.body.ok.should.be.exactly(0)
+             res.body.e.should.match(new RegExp(test.expected.e, 'i'))
+           done()
+       return
```

---

已为 `propClaim.coffee` 文件中新添加的 `POST /propClaim/map` 接口补充了详细的单元测试，覆盖了权限校验、参数校验和返回结构等情况，并且每个测试块都带有 JsDoc 风格注释，便于理解和维护。

如需对其他 POST 接口（如 edit、delete、detail、list）补充更细致的单元测试，或有其他接口需要覆盖，请随时告知！

---

