<!-- Generated by SpecStory -->

# 如何使用MCP连接本地数据库 (2025-05-06 01:28Z)

_**User**_

怎么使用mcp连接数本地据库

---

_**Assistant**_

我来帮您解释如何使用 MCP (MySQL Connector/Python) 连接本地数据库。

以下是一个基本的示例代码，包含详细的注释：

```python
import mysql.connector
from mysql.connector import Error

def connect_to_database():
    """
    连接到本地MySQL数据库的函数
    
    Returns:
        connection: 数据库连接对象
    """
    try:
        # 建立数据库连接
        connection = mysql.connector.connect(
            host='localhost',      # 数据库主机地址
            user='your_username',  # 数据库用户名
            password='your_password', # 数据库密码
            database='your_database'  # 数据库名称
        )
        
        if connection.is_connected():
            print("成功连接到MySQL数据库")
            return connection
            
    except Error as e:
        print(f"连接数据库时出错: {e}")
        return None

def execute_query(connection, query):
    """
    执行SQL查询的函数
    
    Args:
        connection: 数据库连接对象
        query: 要执行的SQL查询语句
    """
    try:
        cursor = connection.cursor()
        cursor.execute(query)
        result = cursor.fetchall()
        return result
    except Error as e:
        print(f"执行查询时出错: {e}")
    finally:
        if cursor:
            cursor.close()

# 使用示例
if __name__ == "__main__":
    # 连接到数据库
    conn = connect_to_database()
    
    if conn:
        # 执行查询示例
        query = "SELECT * FROM your_table"
        results = execute_query(conn, query)
        
        # 打印结果
        for row in results:
            print(row)
            
        # 关闭连接
        conn.close()
```

使用说明：

1. 首先需要安装 MySQL Connector/Python：
```bash
pip install mysql-connector-python
```

2. 在代码中需要修改以下参数：
   - `host`: 数据库服务器地址（本地使用 'localhost'）
   - `user`: 数据库用户名
   - `password`: 数据库密码
   - `database`: 要连接的数据库名称

3. 主要功能：
   - `connect_to_database()`: 建立数据库连接
   - `execute_query()`: 执行SQL查询

注意事项：
1. 确保MySQL服务已经启动
2. 确保用户名和密码正确
3. 确保数据库已经创建
4. 建议使用环境变量或配置文件存储数据库凭证，而不是直接写在代码中

如果您需要更详细的示例或有特定的查询需求，请告诉我，我很乐意提供更多帮助。

---

