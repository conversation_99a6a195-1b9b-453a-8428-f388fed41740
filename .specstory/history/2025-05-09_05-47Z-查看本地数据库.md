<!-- Generated by SpecStory -->

# 查看本地数据库 (2025-05-09 05:47Z)

_**User**_

查看一下我的本地数据库

---

_**Assistant**_

让我帮您查看一下本地数据库的内容。

首先，我需要了解当前工作目录的结构。


---

<details>
            <summary>Listed current directory • **8** results</summary>
        
| Name |
|-------|
| 📁 `appweb` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |
| 📁 `rmconfig` |
| 📄 `.DS_Store` |
| 📁 `.cursor` |
| 📄 `MlsStatusCSV.csv` |
| 📄 `check.js` |

</details>

---

我看到您的工作目录中有几个文件和文件夹。让我们进一步查看一下 `appweb` 目录，因为从之前的信息中我看到有一些数据库相关的文件。


---

<details>
            <summary>Listed directory appweb • **10** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `src` |
| 📁 `extra` |
| 📁 `docs` |
| 📄 `.cursorrules` |
| 📄 `.DS_Store` |
| 📁 `n5` |
| 📄 `package-lock.json` |
| 📁 `fileStorage` |
| 📁 `batch` |

</details>

---

让我们继续深入查看 `src` 目录，因为通常数据库相关的代码会放在这里。


---

<details>
            <summary>Listed directory appweb/src • **58** results</summary>
        
| Name |
|-------|
| 📁 `lib` |
| 📁 `unitTest` |
| 📁 `stats` |
| 📁 `model` |
| 📄 `package.json` |
| 📁 `mlsImport` |
| 📁 `libapp` |
| 📁 `coffee4client` |
| 📁 `extra` |
| 📁 `import` |
| 📁 `keys` |
| 📁 `batch` |
| 📁 `bin` |
| 📄 `.gitignore` |
| 📁 `.nyc_output` |
| 📁 `unitTestJs` |
| 📁 `coverage` |
| 📁 `built` |
| 📁 `examples` |
| 📁 `webDev` |
| 📁 `appDev3` |
| 📁 `webDev3` |
| 📁 `webpack` |
| 📄 `.DS_Store` |
| 📁 `webroot` |
| 📁 `webSettingDatas` |
| 📁 `migrate` |
| 📄 `s3uploadError.log` |
| 📁 `node_modules` |
| 📄 `package-lock.json` |
| 📁 `universityimport` |
| 📁 `transfiles` |
| 📁 `themes` |
| 📁 `test` |
| 📁 `theme_unused` |
| 📁 `style` |
| 📁 `shareImgTemplate` |
| 📁 `services` |
| 📁 `react_components` |
| 📄 `readMe.md` |
| 📁 `quebec` |
| 📁 `mls_unused` |
| 📁 `migrateDone` |
| 📁 `microService` |
| 📁 `mediafile` |
| 📁 `logincheck` |
| 📁 `inter.node` |
| 📁 `coopimport` |
| 📁 `emailTemplate` |
| 📄 `coffeelint.json` |
| 📁 `appsTest` |
| 📁 `apps_common` |
| 📁 `appImgServer` |
| 📁 `appLogger` |
| 📁 `apps` |
| 📄 `.babelrc` |
| 📄 `abbr.csv` |
| 📁 `appBatch` |

</details>

---

我看到这是一个相当大的项目，让我们查看一下 `model` 目录，因为这里通常包含数据库模型定义。


---

<details>
            <summary>Listed directory appweb/src/model • **65** results</summary>
        
| Name |
|-------|
| 📄 `properties.coffee` |
| 📄 `school.coffee` |
| 📄 `schoolCensus.coffee` |
| 📄 `showing.coffee` |
| 📄 `staticRMlisting.coffee` |
| 📄 `statistics.coffee` |
| 📄 `sysNotify.coffee` |
| 📄 `user_email.coffee` |
| 📄 `011_userWatch.coffee` |
| 📄 `012_propStats.coffee` |
| 📄 `020_geoCoderLocal.coffee` |
| 📄 `claim.coffee` |
| 📄 `crm.coffee` |
| 📄 `estimated.coffee` |
| 📄 `forum.coffee` |
| 📄 `project.coffee` |
| 📄 `propNotes.coffee` |
| 📄 `010_user.coffee` |
| 📄 `brokerage.coffee` |
| 📄 `token.coffee` |
| 📄 `userAccessControl.coffee` |
| 📄 `condo.coffee` |
| 📄 `floorplans.coffee` |
| 📄 `boundary.coffee` |
| 📄 `transit.coffee` |
| 📄 `vcode.coffee` |
| 📄 `weather.coffee` |
| 📄 `shortUrl.coffee` |
| 📄 `saveCMA.coffee` |
| 📄 `propertyBoundary.coffee` |
| 📄 `processStatus.coffee` |
| 📄 `moderation.coffee` |
| 📄 `group.coffee` |
| 📄 `equifax.coffee` |
| 📄 `advertisementAll.coffee` |
| 📄 `actionCode.coffee` |
| 📄 `abTest.coffee` |
| 📄 `stigmatized.coffee` |
| 📄 `propFav.coffee` |
| 📄 `webMenuRegistry.coffee` |
| 📄 `snsReply.coffee` |
| 📄 `transaction.coffee` |
| 📄 `trebKey.coffee` |
| 📄 `userOpLogs.coffee` |
| 📄 `wecard.coffee` |
| 📄 `manualImport.coffee` |
| 📄 `namingRules.coffee` |
| 📄 `register.coffee` |
| 📄 `rmListingContact.coffee` |
| 📄 `021_provAndCity.coffee` |
| 📄 `SysData.coffee` |
| 📄 `addrCache.coffee` |
| 📄 `advertisement.coffee` |
| 📄 `blockPhone.coffee` |
| 📄 `community.coffee` |
| 📄 `condoComplex.coffee` |
| 📄 `coopHouse.coffee` |
| 📄 `crime.coffee` |
| 📄 `evaluation.coffee` |
| 📄 `formInput.coffee` |
| 📄 `geoCache.coffee` |
| 📄 `geoCoder.coffee` |
| 📄 `limiter.coffee` |
| 📄 `020_geoCoderRemote.coffee` |
| 📄 `009_pushNotify.coffee` |

</details>

---

根据我的观察，这是一个使用 CoffeeScript 编写的房地产相关应用程序。主要的数据库模型包括：

1. 用户相关：
   - `010_user.coffee` - 用户模型
   - `user_email.coffee` - 用户邮箱
   - `userAccessControl.coffee` - 用户访问控制

2. 房产相关：
   - `properties.coffee` - 房产属性
   - `condo.coffee` - 公寓
   - `floorplans.coffee` - 平面图
   - `propNotes.coffee` - 房产备注
   - `propFav.coffee` - 收藏的房产

3. 其他重要模型：
   - `school.coffee` - 学校信息
   - `transit.coffee` - 交通信息
   - `weather.coffee` - 天气信息
   - `forum.coffee` - 论坛
   - `crm.coffee` - 客户关系管理

这个数据库结构看起来是一个完整的房地产管理系统，包含了用户管理、房产管理、学校信息、交通信息等多个模块。

您想了解哪个具体模块的详细信息吗？我可以帮您深入查看任何特定模型的具体实现。

---

