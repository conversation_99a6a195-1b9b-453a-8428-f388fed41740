<!-- Generated by SpecStory -->

# 代码审查与修改建议 (2025-05-19 06:42Z)

_**User**_

revire @Branch (Diff with Main Branch) 的代码，提出修改建议，确认一下修改之后有没有出现的新问题

---

_**Assistant**_

我来帮您检查代码并提出修改建议。首先让我们查看一下代码库的结构。


---

<details>
            <summary>Listed current directory • **9** results</summary>
        
| Name |
|-------|
| 📁 `src` |
| 📁 `appweb` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |
| 📁 `rmconfig` |
| 📄 `.DS_Store` |
| 📁 `.cursor` |
| 📄 `MlsStatusCSV.csv` |
| 📄 `check.js` |

</details>

---

让我们先看看主要的源代码目录。


---

<details>
            <summary>Listed directory src • **1** results</summary>
        
| Name |
|-------|
| 📁 `utils` |

</details>

---

<details>
            <summary>Listed directory src/utils • **0** results</summary>
        
No results found
</details>

---

我注意到代码库似乎比较空，让我们看看是否有其他重要的目录。


---

<details>
            <summary>Listed directory appweb • **10** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `src` |
| 📁 `docs` |
| 📄 `.DS_Store` |
| 📁 `extra` |
| 📄 `.cursorrules` |
| 📁 `n5` |
| 📄 `package-lock.json` |
| 📁 `fileStorage` |
| 📁 `batch` |

</details>

---

让我们检查一下主要的源代码目录。


---

<details>
            <summary>Listed directory appweb/src • **58** results</summary>
        
| Name |
|-------|
| 📁 `lib` |
| 📁 `libapp` |
| 📁 `model` |
| 📁 `coffee4client` |
| 📁 `coverage` |
| 📁 `.nyc_output` |
| 📁 `unitTestJs` |
| 📁 `built` |
| 📄 `package.json` |
| 📁 `unitTest` |
| 📄 `.DS_Store` |
| 📁 `batch` |
| 📁 `stats` |
| 📁 `mlsImport` |
| 📁 `extra` |
| 📁 `import` |
| 📁 `keys` |
| 📁 `bin` |
| 📄 `.gitignore` |
| 📁 `examples` |
| 📁 `webDev` |
| 📁 `appDev3` |
| 📁 `webDev3` |
| 📁 `webpack` |
| 📁 `webroot` |
| 📁 `webSettingDatas` |
| 📁 `migrate` |
| 📄 `s3uploadError.log` |
| 📁 `node_modules` |
| 📄 `package-lock.json` |
| 📁 `universityimport` |
| 📁 `transfiles` |
| 📁 `themes` |
| 📁 `test` |
| 📁 `theme_unused` |
| 📁 `style` |
| 📁 `shareImgTemplate` |
| 📁 `services` |
| 📁 `react_components` |
| 📄 `readMe.md` |
| 📁 `quebec` |
| 📁 `mls_unused` |
| 📁 `migrateDone` |
| 📁 `microService` |
| 📁 `mediafile` |
| 📁 `logincheck` |
| 📁 `inter.node` |
| 📁 `coopimport` |
| 📁 `emailTemplate` |
| 📄 `coffeelint.json` |
| 📁 `appsTest` |
| 📁 `apps_common` |
| 📁 `appImgServer` |
| 📁 `appLogger` |
| 📁 `apps` |
| 📄 `.babelrc` |
| 📄 `abbr.csv` |
| 📁 `appBatch` |

</details>

---

我注意到这是一个相当大的项目，让我们先看看主要的应用代码。


---

<details>
            <summary>Listed directory appweb/src/apps • **2** results</summary>
        
| Name |
|-------|
| 📁 `80_sites` |
| 📁 `20_commons` |

</details>

---

让我们看看最近的修改记录。


---

```bash
cd appweb && git diff main | cat
```

```
diff --git a/docs/Change_requirements/20250514_update_school_universit.ai.md b/d
ocs/Change_requirements/2025
0514_update_school_universit.ai.md
                            new file mode 100644
index 00000000..8f482b98
--- /dev/null
+++ b/docs/Change_requirements/20250514_update_school_universit.ai.md
@@ -0,0 +1,425 @@
+<!-- Generated by SpecStory -->
+
+# 学校名称对比与映射 (2025-05-15 15:32:55)
+
+_**User**_
+
+将两部分学校名字进行对比，有差异的放在schoolNameMap中，key是现在schoolNameMap里面的内容，value是schoolNameM
ap外面的内容
                       +
+---
+
+_**Assistant**_
+
+我来帮您对比两部分学校名字的差异，并生成相应的映射关系。我会仔细分析每个学校名称，找出需要映射的部分。
+
+让我先解释一下我要做什么：我会对比第一部分(schoolNameMap中的内容)和第二部分(外部内容)的学校名称，找出那些名
称不完全相同但明显指代同一所学校的情况。
        +
+分析后，我发现以下学校名称需要进行映射：
+
+---
+
+```diff
+  
+  schoolNameMap = {
+-   St Michael's Choir S
+- St. Augustine Catholic High School
+- St Robert Catholic HS
+- St. Theresa Of Lisieux Catholic High School
+- École Secondaire Toronto Ouest
+- Collège Français Secondaire
+- Ursula Franklin Academy
+- Bayview Secondary School
+- Iroquois Ridge High School
+- Pierre Elliott Trudeau High School
+- Abbey Park High School
+- White Oaks SS
+- Markville Secondary School
+- Bur Oak Secondary School
+- Unionville High School
+- A Y Jackson Secondary School
+- Cardinal Carter Academy For The Arts
+- Bloor Collegiate Institute
+- Oakville Trafalgar High School
+- York Mills Collegiate Institute
+- John Fraser Secondary School
+- Richmond Hill High School
+- Riverdale Collegiate Institute
+- The Woodlands Secondary School
+- Ésc Saint-Frère-André
+- Earl Haig Secondary School
+- Ésc Renaissance
+- Leaside High School
+- Ésc Sainte-Trinité
+- Milliken Mills High School
+- Humberside Collegiate Institute
+- Malvern Collegiate Institute
+- London Central Secondary School
+- William Lyon Mackenzie Collegiate Institute
+- Bishop Macdonell Catholic Secondary School
+- Earl Of March Secondary School
+- Nelson High School
+- Sir John A Macdonald Secondary School
+- Markham District High School
+- Lawrence Park Collegiate Institute
+- Agincourt Collegiate Institute
+- Thornhill Secondary School
+- Dr Norman Bethune Collegiate Institute
+- Richview Collegiate Institute
+- Our Lady Queen Of The World Catholic Academy
+- Father Michael Mcgivney Catholic Academy
+- Dr. G. W. Williams Secondary School
+- North Toronto Collegiate Institute
+- Thornlea Secondary School
+- École Secondaire Catholique Béatrice-Desloges
+- Lo-Ellen Park Secondary School
+- Colonel By Secondary School
+- École Secondaire Catholique Jeanne-Lajoie, Pavillo
+- Garth Webb Secondary School
+- École Secondaire Norval-Morrisseau
+- École Secondaire Catholique De Casselman
+- Northern Secondary School
+- Richmond Green Secondary School
+- Father John Redmond Catholic Secondary School And Regional Arts Centre
+- Bishop Allen Academy
+- Corpus Christi Secondary School
+- Hodan Nalayeh Secondary School
+- Alexander Mackenzie High School
+- Merivale High School
+- École Secondaire Publique Maurice-Lapointe
+- École Secondaire Étienne-Brûlé
+- Thomas A. Blakelock High School
+- St. Brother Andre Catholic High School
+- Etobicoke Collegiate Institute
+- Tecumseh Vista Academy - Secondary
+- Centennial Collegiate And Vocational Institute
+- Bill Crothers Secondary School
+- Bishop Paul Francis Reding SS
+- Aurora High School
+- St. Francis Xavier Secondary School
+- Ésc Père-René-De-Galinée
+- Stephen Lewis Secondary School
+- Cameron Heights Collegiate Institute
+- Anderson Collegiate And Vocational Institute
+- Saint Paul Catholic High School
+- St. Thomas Aquinas Secondary School
+- Belle River District High School
+- Ésc Saint-Charles-Garnier
+- St David Catholic Secondary School
+- Cardinal Carter SS
+- Monarch Park Collegiate Institute
+- Donald A Wilson Secondary School
+- École Secondaire Le Caron
+- Sacred Heart High School
+- Bill Hogarth Secondary School
+- Newmarket High School
+- Satec @ W A Porter Collegiate Institute
+- St. Joseph Secondary School
+- Sydenham High School
+- École Secondaire Catholique Le Relais
+- West Carleton Secondary School
+- Nepean High School
+- St. John Henry Newman Catholic Secondary School
+- Cardinal Carter Catholic
+- Pickering High School
+- }
+- St. Michael's Choir School
+- St. Augustine Catholic High School
+- St. Robert Catholic High School
+- St. Theresa Of Lisieux Catholic High School
+- École Secondaire Toronto Ouest
+- Collège Français Secondaire
+- Ursula Franklin Academy
+- Bayview Secondary School
+- Iroquois Ridge High School
+- Pierre Elliott Trudeau High School
+- Abbey Park High School
+- White Oaks Secondary School(south Campus)
+- Markville Secondary School
+- Bur Oak Secondary School
+- Unionville High School
+- A Y Jackson Secondary School
+- Cardinal Carter Academy For The Arts
+- Bloor Collegiate Institute
+- Oakville Trafalgar High School
+- York Mills Collegiate Institute
+- John Fraser Secondary School
+- Richmond Hill High School
+- Riverdale Collegiate Institute
+- The Woodlands Secondary School
+- Ésc Saint-Frère-André
+- Earl Haig Secondary School
+- Ésc Renaissance
+- Leaside High School
+- Ésc Sainte-Trinité
+- Milliken Mills High School
+- Humberside Collegiate Institute
+- Malvern Collegiate Institute
+- London Central Secondary School
+- William Lyon Mackenzie Collegiate Institute
+- Bishop Macdonell Catholic Secondary School
+- Earl Of March Secondary School
+- Nelson High School
+- Sir John A Macdonald Secondary School
+- Markham District High School
+- Lawrence Park Collegiate Institute
+- Agincourt Collegiate Institute
+- Thornhill Secondary School
+- Dr Norman Bethune Collegiate Institute
+- Richview Collegiate Institute
+- Our Lady Queen Of The World Catholic Academy
+- Father Michael Mcgivney Catholic Academy
+- Dr. G. W. Williams Secondary School
+- North Toronto Collegiate Institute
+- Thornlea Secondary School
+- École Secondaire Catholique Béatrice-Desloges
+- Lo-Ellen Park Secondary School
+- Colonel By Secondary School
+- École Secondaire Catholique Jeanne-Lajoie, Pavillo
+- Garth Webb
+- École Secondaire Norval-Morrisseau
+- École Secondaire Catholique De Casselman
+- Northern Secondary School
+- Richmond Green Secondary School
+- Father John Redmond Catholic Secondary School And Regional Arts Centre
+- Bishop Allen Academy
+- Corpus Christi Secondary School
+- Hodan Nalayeh Secondary School
+- Alexander Mackenzie High School
+- Merivale High School
+- École Secondaire Publique Maurice-Lapointe
+- École Secondaire Étienne-Brûlé
+- Thomas A. Blakelock High School
+- St. Brother Andre Catholic High School
+- Etobicoke Collegiate Institute
+- Tecumseh Vista Academy - Secondary
+- Centennial Collegiate And Vocational Institute
+- Bill Crothers Secondary School
+- Bishop P. F. Reding Secondary School
+- Aurora High School
+- St. Francis Xavier Secondary School
+- Ésc Père-René-De-Galinée
+- Stephen Lewis Secondary School
+- Cameron Heights Collegiate Institute
+- Anderson Collegiate And Vocational Institute
+- Saint Paul Catholic High School
+- St. Thomas Aquinas Secondary School
+- Belle River District High School
+- Ésc Saint-Charles-Garnier
+- St David Catholic Secondary School
+- Cardinal Carter Catholic High School
+- Monarch Park Collegiate Institute
+- Donald A Wilson Secondary School
+- École Secondaire Le Caron
+- Sacred Heart High School
+- Bill Hogarth Secondary School
+- Newmarket High School
+- Satec @ W A Porter Collegiate Institute
+- St. Joseph Secondary School
+- Sydenham High School
+- École Secondaire Catholique Le Relais
+- West Carleton Secondary School
+- Nepean High School
+- St. John Henry Newman
+- Cardinal Carter Catholic
+- Pickering High School
++   'St Michael\'s Choir S': 'St. Michael\'s Choir School',
++   'St Robert Catholic HS': 'St. Robert Catholic High School',
++   'White Oaks SS': 'White Oaks Secondary School(south Campus)',
++   'Garth Webb Secondary School': 'Garth Webb',
++   'Bishop Paul Francis Reding SS': 'Bishop P. F. Reding Secondary School',
++   'Cardinal Carter SS': 'Cardinal Carter Catholic High School',
++   'St. John Henry Newman Catholic Secondary School': 'St. John Henry Newman'
++ }
+  
+  ###*
+```
+
+---
+
+我已经创建了一个包含所有需要映射的学校名称的对象。让我解释一下每个映射：
+
+1. `'St Michael's Choir S'` -> `'St. Michael's Choir School'`
+   - 将简写名称映射到完整名称
+
+2. `'St Robert Catholic HS'` -> `'St. Robert Catholic High School'`
+   - 将 "HS" 简写映射到完整的 "High School"
+
+3. `'White Oaks SS'` -> `'White Oaks Secondary School(south Campus)'`
+   - 将 "SS" 简写映射到完整的校区名称
+
+4. `'Garth Webb Secondary School'` -> `'Garth Webb'`
+   - 将完整名称映射到简短版本
+
+5. `'Bishop Paul Francis Reding SS'` -> `'Bishop P. F. Reding Secondary School'
`
+   - 将带有完整名字的版本映射到使用缩写的版本
+
+6. `'Cardinal Carter SS'` -> `'Cardinal Carter Catholic High School'`
+   - 将简写版本映射到完整的官方名称
+
+7. `'St. John Henry Newman Catholic Secondary School'` -> `'St. John Henry Newm
an'`
+   - 将完整正式名称映射到简短版本
+
+这些映射关系将帮助系统在处理学校名称时正确匹配不同版本的学校名称。其他学校名称都是完全匹配的，所以不需要包
含在映射中。
                      +
+---
+
+_**User**_
+
+参考 @update_adj_factors.coffee 的处理，先检查schoolName是否在PrivateSchoolNameMap中，查找priva
te表，并更新
+
+---
+
+_**Assistant**_
+
+我明白了，我需要参考`update_adj_factors.coffee`的处理方式，先检查私立学校，然后再处理公立学校。我来修改代码
。
                           +
+---
+
+```diff
+  }
+  
++ # 初始化数据库集合
+  SchoolMergedCol = COLLECTION 'vow', 'sch_rm_merged'
+  SchoolRankCol = COLLECTION 'vow', 'sch_rm_rank'
++ PrivateSchoolCol = COLLECTION 'vow', 'private_schools'  # 添加私立学校集合
+  
+  {mergeArrayByYearmark,generateSchoolName,delSchoolCommWord} = INCLUDE 'libapp
.schoolHelper'
+```
+
+```diff
+    'Bishop Paul Francis Reding SS': 'Bishop P. F. Reding Secondary School',
+    'Cardinal Carter SS': 'Cardinal Carter Catholic High School',
+-   'St. John Henry Newman Catholic Secondary School': 'St. John Henry Newman'
++   'St. John Henry Newman Catholic Secondary School': 'St. John Henry Newman',
+    'Danforth Collegiate and Technical Institute': 'Danforth Collegiate And Tec
hnical Institute',
+-   'St Aloysius Gonzaga Secondary School': 'St. Aloysius Gonzaga Secondary Sch
ool',
++   'St Aloysius Gonzaga Secondary School': 'St. Aloysius Gonzaga Secondary Sch
ool'
+  }
+- 
+  
+  PrivateSchoolNameMap = {
+```
+
+```diff
+    'Hamilton District Christian High School':'Hamilton District Christian High
'
+  }
++ 
+  ###*
+   * 将百分比转换为整数(放大10000倍)
+```
+
+```diff
+    return name
+  
++ ###*
++  * 查找匹配的学校信息
++  * @param {string} schoolName - 学校名称
++  * @returns {Promise<Object>} - 匹配的学校信息或null
++ ###
++ findMatchedSchool = (schoolName) ->
++   # 首先检查是否为私立学校
++   if privateSchoolName = PrivateSchoolNameMap[schoolName]
++     try
++       privateSchool = await PrivateSchoolCol.findOne(
++         { nm: { $regex: privateSchoolName, $options: 'i' } },
++         { projection: { _id: 1, nm: 1 } }
++       )
++       
++       if privateSchool
++         return {
++           _id: privateSchool._id
++           nm: privateSchool.nm
++           isPrivate: true
++         }
++     catch err
++       debug.error "查询私立学校 #{privateSchoolName} 时出错:", err
++ 
++   # 如果不是私立学校或私立学校未找到，则查询公立学校
++   # 检查是否需要映射学校名称
++   if mappedName = schoolNameMap[schoolName]
++     schoolName = mappedName
++ 
++   optimizedName = optimizeSchoolName(schoolName)
++   
++   query = 
++     $or: [
++       { nm: schoolName },
++       { nm: { $regex: schoolName, $options: 'i' } }
++     ]
++   
++   if optimizedName and optimizedName isnt schoolName.toLowerCase()
++     query.$or.push { nm: { $regex: optimizedName, $options: 'i' } }
++ 
++   # 在sch_rm_merged中查找匹配的学校
++   matchedSchool = await SchoolMergedCol.findOne(query, { projection: { _id: 1
, nm: 1} })
++   
++   if matchedSchool
++     return {
++       _id: matchedSchool._id
++       nm: matchedSchool.nm
++       isPrivate: false
++     }
++   
++   return null
++ 
+  ###*
+   * 处理学校数据
+```
+
+```diff
+      speedMeter.check {processSchool:1}
+      schoolName = school['school name']
+-     optimizedName = optimizeSchoolName(schoolName)
+      
+      # 查询匹配学校
+-     query = 
+-       $or: [
+-         { nm: schoolName },
+-         { nm: { $regex: schoolName, $options: 'i' } }
+-       ]
+-     
+-     if optimizedName and optimizedName isnt schoolName.toLowerCase()
+-       query.$or.push { nm: { $regex: optimizedName, $options: 'i' } }
+-     # 在sch_rm_merged中查找匹配的学校
+-     matchedSchool = await SchoolMergedCol.findOne(query, { projection: { _id:
 1, nm: 1} })
+-     
++     matchedSchool = await findMatchedSchool(schoolName)
+      
+      # 如果找到匹配的学校,则处理和保存数据
+```
+
+```diff
+          ivyRank = mergeArrayByYearmark(ivyRank, [topSchoolsData])
+  
+- 
+        # 如果是试运行模式,则只打印数据而不更新数据库
+        if dryrun
+```
+
+---
+
+我已经按照`update_adj_factors.coffee`的处理方式更新了代码，主要做了以下改动：
+
+1. 添加了私立学校集合的初始化：
+```coffeescript
+PrivateSchoolCol = COLLECTION 'vow', 'private_schools'  # 添加私立学校集合
+```
+
+2. 创建了新的`findMatchedSchool`函数，实现了私立学校优先的查找逻辑：
+- 首先检查学校是否在`PrivateSchoolNameMap`中
+- 如果是私立学校，则在`private_schools`集合中查找
+- 如果找到私立学校，返回带有`isPrivate: true`标记的结果
+- 如果不是私立学校或未找到，则继续查找公立学校
+
+3. 修改了`processSchoolData`函数，使用新的`findMatchedSchool`函数替换原来的查找逻辑
+
+这样处理的优点是：
+1. 优先处理私立学校，避免与公立学校名称混淆
+2. 使用专门的私立学校集合，保持数据的清晰性
+3. 通过`isPrivate`标记清楚地区分学校类型
+4. 保持了原有的名称映射和模糊匹配功能
+
+这个改动让学校匹配的逻辑更加完整和准确，特别是在处理私立学校时。同时也保持了代码的可维护性和可读性。
+
+---
+
diff --git a/docs/Change_requirements/20250514_update_school_university.md b/doc
s/Change_requirements/202505
14_update_school_university.md
                            new file mode 100644
index 00000000..3d579bac
--- /dev/null
+++ b/docs/Change_requirements/20250514_update_school_university.md
@@ -0,0 +1,31 @@
+# 需求 [update_school_university]
+
+## 反馈
+
+1.新增了20条升学排名
+
+## 需求提出人:    Karli
+
+## 修改人：      liurui
+
+## 提出日期:     2025-05-01
+
+## 原因
+以前未提供
+
+## 解决办法
+
+1. 跑batch添加数据
+2. 增加私校处理
+
+## 确认日期:    2025-05-1
+
+
+
+## Online step
+
+1.更新School-University-data-summary.xlsx文件，添加top100数据
+最新数据文件已更新至/rmlfs/xlsx文件夹下
+```
+sh start.sh -t batch -n update_top100_school_rank -cmd "lib/batchBase.coffee ba
tch/school_rank/update_top10
0_school_rank.coffee -f /rmlfs/xlsx/2024School-University-data-summary.xlsx"
                            +```
diff --git a/src/apps/80_sites/AppRM/school/schools.coffee b/src/apps/80_sites/A
ppRM/school/schools.coffee
index d02349d7..906b7f2b 100644
--- a/src/apps/80_sites/AppRM/school/schools.coffee
+++ b/src/apps/80_sites/AppRM/school/schools.coffee
@@ -669,7 +669,7 @@ GET 'schoolList',(req,resp)->
  * - 如果已兑换,则保留完整数据
 ###
 checkSchoolToken = (school,uid)->
-  return school unless ((school.rmRankKeyFacts and Object.keys(school.rmRankKey
Facts).length > 0) or (schoo
l.censusChartData?.length > 1) or school.rankMap?.adjSummary)#school.censusChart
Data 有一个默认的key: 'summary'，所以判断length>1
                                             +  return school unless ((school.rm
RankKeyFacts and Object.keys(school.rmRankKeyFacts).length > 0) or (schoo
l.censusChartData?.length > 1) or school.rankMap?.adjSummary or school.rankMap?.
basic)#school.censusChartData 有一个默认的key: 'summary'，所以判断length>1
                                                school.canExchange = 1
   # token兑换学校通用模块，public school使用sourceId，private school使用_id
   tokenId = (school.sourceId or school._id) + (school.rmRankLastYear or 'Census
')
diff --git a/src/batch/school_rank/update_top100_school_rank.coffee b/src/batch/
school_rank/update_top100_sc
hool_rank.coffee
                            index 94e0c19c..dbd67ae3 100644
--- a/src/batch/school_rank/update_top100_school_rank.coffee
+++ b/src/batch/school_rank/update_top100_school_rank.coffee
@@ -13,9 +13,8 @@
  * <AUTHOR>
  * @date 2025-04-03
  * 
- * @usage sh start.sh -t batch -n update_top100_school_rank -cmd "lib/batchBase
.coffee batch/school_rank/up
date_top100_school_rank.coffee -f 文件名 -y 年份"
                       + * @usage sh start.sh -t batch -n update_top100_school_r
ank -cmd "lib/batchBase.coffee batch/school_rank/up
date_top100_school_rank.coffee -f 文件名"
                           * @param {string} fileName - 数据文件路径
- * @param {number} year - 数据年份,例如:2024(代表2023-2024学年)
  * @param {boolean} [dryrun] - 是否试运行模式
 
  导入时的注意事项：
@@ -77,7 +76,6 @@ yargs.option('dryrun', { alias: 'n', description: '试运行模式'})
 yargs.option('fileName', { alias: 'f', description: 'File Name'})
 
 # 获取参数值
-yearmark = (yargs.argv.year - 1) + '-' + yargs.argv.year
 dryrun = yargs.argv.dryrun
 fileName = yargs.argv.fileName
 
@@ -103,11 +101,30 @@ speedMeter = speed.createSpeedMeter {
     debug.info speedMeter.toString()
 }
 
+# 初始化数据库集合
 SchoolMergedCol = COLLECTION 'vow', 'sch_rm_merged'
 SchoolRankCol = COLLECTION 'vow', 'sch_rm_rank'
+PrivateSchoolCol = COLLECTION 'vow', 'private_schools'  # 添加私立学校集合
 
 {mergeArrayByYearmark,generateSchoolName,delSchoolCommWord} = INCLUDE 'libapp.s
choolHelper'
 
+schoolNameMap = {
+  'St Michael\'s Choir S': 'St. Michael\'s Choir School',
+  'St Robert Catholic HS': 'St. Robert Catholic High School',
+  'White Oaks SS': 'White Oaks Secondary School',
+  'Garth Webb Secondary School': 'Garth Webb',
+  'Bishop Paul Francis Reding SS': 'Bishop P. F. Reding Secondary School',
+  'Cardinal Carter SS': 'Cardinal Carter Catholic High School',
+  'St. John Henry Newman Catholic Secondary School': 'St. John Henry Newman',
+  'Danforth Collegiate and Technical Institute': 'Danforth Collegiate And Techn
ical Institute',
+  'St Aloysius Gonzaga Secondary School': 'St. Aloysius Gonzaga Secondary Schoo
l'
+}
+
+PrivateSchoolNameMap = {
+  'Tanenbaum Chat Wallenberg Campus':'Tanenbaum Community Hebrew Academy of Tor
onto',
+  'Crescent School':'Crescent School',
+  'Hamilton District Christian High School':'Hamilton District Christian High'
+}
 
 ###*
  * 将百分比转换为整数(放大10000倍)
@@ -196,13 +213,63 @@ optimizeSchoolName = (name) ->
   
   return name
 
+###*
+ * 查找匹配的学校信息
+ * @param {string} schoolName - 学校名称
+ * @returns {Promise<Object>} - 匹配的学校信息或null
+###
+findMatchedSchool = (schoolName) ->
+  # 首先检查是否为私立学校
+  if privateSchoolName = PrivateSchoolNameMap[schoolName]
+    try
+      privateSchool = await PrivateSchoolCol.findOne(
+        { nm: { $regex: privateSchoolName, $options: 'i' } },
+        { projection: { _id: 1, nm: 1 } }
+      )
+      
+      if privateSchool
+        return {
+          _id: privateSchool._id
+          nm: privateSchool.nm
+          isPrivate: true
+        }
+    catch err
+      debug.error "查询私立学校 #{privateSchoolName} 时出错:", err
+
+  # 如果不是私立学校或私立学校未找到，则查询公立学校
+  # 检查是否需要映射学校名称
+  if mappedName = schoolNameMap[schoolName]
+    schoolName = mappedName
+
+  optimizedName = optimizeSchoolName(schoolName)
+  
+  query = 
+    $or: [
+      { nm: schoolName },
+      { nm: { $regex: schoolName, $options: 'i' } }
+    ]
+  
+  if optimizedName and optimizedName isnt schoolName.toLowerCase()
+    query.$or.push { nm: { $regex: optimizedName, $options: 'i' } }
+
+  # 在sch_rm_merged中查找匹配的学校
+  matchedSchool = await SchoolMergedCol.findOne(query, { projection: { _id: 1,
nm: 1} })
+  
+  if matchedSchool
+    return {
+      _id: matchedSchool._id
+      nm: matchedSchool.nm
+      isPrivate: false
+    }
+  
+  return null
+
 ###*
  * 处理学校数据
  * @param {Object} majorData - 包含学校数据的对象
- * @param {string} yearmark - 学年标记,如"2023-2024"
  * @returns {Promise<void>}
 ###
-processSchoolData = (majorData, yearmark) ->
+processSchoolData = (majorData) ->
   # 遍历所有学校数据
   for school in majorData.data
     # 跳过无效数据
@@ -210,19 +277,9 @@ processSchoolData = (majorData, yearmark) ->
 
     speedMeter.check {processSchool:1}
     schoolName = school['school name']
-    optimizedName = optimizeSchoolName(schoolName)
     
     # 查询匹配学校
-    query = 
-      $or: [
-        { nm: schoolName }
-      ]
-    
-    if optimizedName and optimizedName isnt schoolName.toLowerCase()
-      query.$or.push { nm: { $regex: optimizedName, $options: 'i' } }
-    # 在sch_rm_merged中查找匹配的学校
-    matchedSchool = await SchoolMergedCol.findOne(query, { projection: { _id: 1
, nm: 1} })
-    
+    matchedSchool = await findMatchedSchool(schoolName)
     
     # 如果找到匹配的学校,则处理和保存数据
     if matchedSchool
@@ -234,7 +291,6 @@ processSchoolData = (majorData, yearmark) ->
 
       # 准备要保存的基础数据
       topSchoolsData =
-        yearmark: yearmark
         total: parseInt(school.total)
         smplSz: parseInt(school['sample amount'])
         smplPct: convertPct(school['sample%'])
@@ -245,6 +301,8 @@ processSchoolData = (majorData, yearmark) ->
         ivyOld: parseInt(school['Ivy old']) or 0
         ivyNew: parseInt(school['Ivy new']) or 0
         ivyTotal: parseInt(school['Ivy total']) or 0
+      # 处理年份,年份为毕业年份
+      topSchoolsData.yearmark = (topSchoolsData.gradYr - 1) + '-' + topSchoolsD
ata.gradYr
 
       # 只保存百分比大于0的成绩数据
       for score in ['90+', '85+', '80+']
@@ -266,7 +324,7 @@ processSchoolData = (majorData, yearmark) ->
         debug.info JSON.stringify(topSchoolsData, null, 2)
       else
         # 更新学校排名数据
-        await SchoolRankCol.updateOne {_id: matchedSchool._id},{$set: {nm: matc
hedSchool.nm,ivyRank: ivyRan
k}},{upsert: true}
                            +        await SchoolRankCol.updateOne {_id: matched
School._id},{$set: {nm: matchedSchool.nm,ivyRank: ivyRan
k,top100: 1}},{upsert: true}
                                     
         speedMeter.check {updateSchoolRank:1}
         debug.info "Updated school: #{school['school name']}"
@@ -283,14 +341,13 @@ main = ->
   try
     debug.info "Starting school rank update process..."
     debug.info "File: #{fileName}"
-    debug.info "Year: #{yearmark}"
     debug.info "Dry run: #{dryrun}"
 
     # 读取Excel文件
     majorData = await readExcel(fileName)
 
     # 处理数据
-    await processSchoolData(majorData, yearmark)
+    await processSchoolData(majorData)
 
     # 计算并显示处理时间和结果
     processTs = (new Date().getTime() - startTs.getTime())/(1000*60)
diff --git a/src/libapp/schoolHelper.coffee b/src/libapp/schoolHelper.coffee
index 7d5b373a..59920ede 100644
--- a/src/libapp/schoolHelper.coffee
+++ b/src/libapp/schoolHelper.coffee
@@ -777,8 +777,8 @@ exports.formatRmRankPub = (school)->
         
         if showOrder.filter((item) -> item not in ['0']).length is 0
           continue
-        #处理G9年级特殊情况，当学校是G9年级且Score值为0时，将所有显示值设置为N/A
-        if (grade is 'g9') and not rm["#{key}Score"]
+        #当Score值为0时，将所有显示值设置为N/A
+        if not rm["#{key}Score"]
           showOrder = showOrder.map -> 'N/A'
         ###*
         # 处理法语浸润学校的G3年级特殊情况
@@ -799,7 +799,7 @@ exports.formatRmRankPub = (school)->
 # @param {Object} school - 学校对象
 # @returns {void} - 直接修改school对象,添加rmRankLastYear和rmRankKeyFacts字段
 ###
-exports.formatRmRankKeyFacts = (school)->
+exports.formatRmRankKeyFacts = (school,type)->
   return unless ((school.rmRank?.length > 0) and (school.prov_en is 'ON')) or (
(school.ivyRank?.length > 0)
 and (school.private))
                               if school.private
     keys = school.ivyRank?[0]
@@ -807,11 +807,27 @@ exports.formatRmRankKeyFacts = (school)->
     keys = school.rmRank?[0]
   school.rmRankLastYear = keys?.yearmark?.slice(5)
   rmRank = {}
+  pubReg = /(total|rmRanking|rmScore|rmRankChange)$/
+  priReg = /(rmRanking|rmScore)$/
+  reg = if type is 'private' then priReg else pubReg
+  nodateGread = []
   for k,v of keys
-    if /(total|rmRanking|rmScore|rmRankChange)$/.test k
+    if reg.test k
       if /rmScore/i.test k
         v = format2Fixed(v/100,1)
+        if v is 0
+          nodateGread.push k.slice(0,2)
       rmRank[k] = v
+  # 没有分数的年级，排名和分数都显示N/A
+  if nodateGread.length > 0
+    nodateGread.forEach (rm) ->
+      rmRank[rm + 'rmScore'] = 'N/A'
+      rmRank[rm + 'rmRanking'] = 'N/A'
+  
+  # 法语浸润学校g3年级，如果阅读和写作的Lv3和Lv4都为0，则排名显示N/A
+  if rmRank['g3rmRanking'] and (school.fi is 1) and (keys.g3rmReadingLv3 <= 0)
and (keys.g3rmWritingLv3 <= 
0) and (keys.g3rmReadingLv4 <= 0) and (keys.g3rmWritingLv4 <= 0)
                            +    rmRank['g3rmRanking'] = 'N/A'
+  
   delete school.rmRank
   delete school.ivyRank
   school.rmRankKeyFacts = rmRank
@@ -877,20 +893,25 @@ exports.formatRmRankScore = (school)->
       showOrder = []
       # 添加score数据,需要除以100并保留2位小数
       score = rm["#{grade}rmScore"]
-      if score?
-        score = format2Fixed(score/100, 2)
-      showOrder.push(score or '-')
+      if not score?
+        continue
+      score = format2Fixed(score/100, 2)
+      showOrder.push(score)
 
       # 添加ranking数据
       ranking = rm["#{grade}rmRanking"]
-      showOrder.push(ranking or '-')
-      
-      # 如果有效数据,添加到subMap
-      if showOrder.filter((item) -> item not in ['-']).length is 0
-        continue
+      showOrder.push(ranking)
+
+      # 没有分数的年级，排名和分数都显示N/A
+      if score is 0
+        showOrder = ['N/A', 'N/A']
       # 如果是g9且rmMathScore为0，则显示N/A
-      if (grade is 'g9') and not rm["#{grade}rmMathScore"]
+      if (grade is 'g9') and (rm["#{grade}rmMathScore"] <= 0)
         showOrder = showOrder.map -> 'N/A'
+
+      # 法语浸润学校g3年级，如果阅读和写作的Lv3和Lv4都为0，则排名显示N/A
+      if (grade is 'g3') and (school.fi is 1) and (rm.g3rmReadingLv3 <= 0) and
(rm.g3rmWritingLv3 <= 0) and
 (rm.g3rmReadingLv4 <= 0) and (rm.g3rmWritingLv4 <= 0)
                            +        showOrder[1] = 'N/A'
       subMap[rm.yearmark] = showOrder
     return subMap if Object.keys(subMap).length
     return null
@@ -1048,8 +1069,8 @@ exports.formatIvyRank = (school)->
   
   # 将处理后的数据添加到学校对象中
   rankMap.basic = basic
-  rankMap.universities = universities
-  rankMap.majors = majors
+  rankMap.universities = universities if Object.keys(universities).length > 0
+  rankMap.majors = majors if Object.keys(majors).length > 0
   rankMap.years = years
 
   delete school.ivyRank
diff --git a/src/model/school.coffee b/src/model/school.coffee
index a3be1f35..51c2cc6e 100644
--- a/src/model/school.coffee
+++ b/src/model/school.coffee
@@ -787,7 +787,8 @@ buildSchRankLookupStages = ->
             $project: {
               ivyRank: 1,
               adjSummary: 1,
-              adjDetail: 1
+              adjDetail: 1,
+              top100: 1
             }
           }
         ],
@@ -797,7 +798,8 @@ buildSchRankLookupStages = ->
       $addFields:
         ivyRank: { $arrayElemAt: ['$rmRankData.ivyRank', 0] },
         adjSummary: { $arrayElemAt: ['$rmRankData.adjSummary', 0] },
-        adjDetail: { $arrayElemAt: ['$rmRankData.adjDetail', 0] }
+        adjDetail: { $arrayElemAt: ['$rmRankData.adjDetail', 0] },
+        top100: { $arrayElemAt: ['$rmRankData.top100', 0] },
         hasAdj: {
           $or: [
             { $gt: [{ $size: { $ifNull: ['$rmRankData.ivyRank', []] } }, 0] },
@@ -1890,7 +1892,7 @@ class School
           s.loc = [s.lat,s.lng]
         translateSch s,locale
         addTags s,locale
-        formatRmRankKeyFacts s
+        formatRmRankKeyFacts s, 'private'
         s.canExchange = (s.rmRankKeyFacts && (Object.keys(s.rmRankKeyFacts).len
gth > 0)) or s.hasAdj
         if showOnMap
           s.fnm = s.nm
@@ -2093,7 +2095,8 @@ class School
       ...projection,
       ivyRank: 1,
       adjSummary: 1,
-      adjDetail: 1
+      adjDetail: 1,
+      top100: 1
     } })
 
     # 执行聚合查询
@@ -2108,9 +2111,13 @@ class School
     if canExchange and (sch.ivyRank or sch.adjSummary)
       # formatChart sch
       formatRmRankScore sch
-      formatRmRankPir sch
+      if sch.top100
+        sch.rankMap = {}
+        formatIvyRank sch
+      else
+        formatRmRankPir sch
       formatAdjRank sch
-    formatRmRankKeyFacts sch
+    formatRmRankKeyFacts sch, 'private'
     return sch
 
   ###*

```

---

根据代码审查，我发现以下几个主要的修改点：

1. 学校名称映射的改进：
```coffeescript
schoolNameMap = {
  'St Michael\'s Choir S': 'St. Michael\'s Choir School',
  'St Robert Catholic HS': 'St. Robert Catholic High School',
  'White Oaks SS': 'White Oaks Secondary School',
  // ... 其他映射
}
```
这个映射表帮助处理不同格式的学校名称。

2. 私立学校处理：
```coffeescript
PrivateSchoolNameMap = {
  'Tanenbaum Chat Wallenberg Campus': 'Tanenbaum Community Hebrew Academy of Toronto',
  'Crescent School': 'Crescent School',
  'Hamilton District Christian High School': 'Hamilton District Christian High'
}
```
新增了私立学校的专门处理。

3. 学校查找逻辑优化：
```coffeescript
findMatchedSchool = (schoolName) ->
  # 首先检查是否为私立学校
  if privateSchoolName = PrivateSchoolNameMap[schoolName]
    // ... 私立学校处理逻辑
  // ... 公立学校处理逻辑
```
优化了学校匹配的逻辑，优先处理私立学校。

4. 数据格式处理改进：
```coffeescript
exports.formatRmRankKeyFacts = (school,type)->
  // ... 处理逻辑
  if nodateGread.length > 0
    nodateGread.forEach (rm) ->
      rmRank[rm + 'rmScore'] = 'N/A'
      rmRank[rm + 'rmRanking'] = 'N/A'
```
改进了数据格式处理，特别是对缺失数据的处理。

5. 排名显示逻辑优化：
```coffeescript
if (grade is 'g3') and (school.fi is 1) and (rm.g3rmReadingLv3 <= 0) and (rm.g3rmWritingLv3 <= 0) and (rm.g3rmReadingLv4 <= 0) and (rm.g3rmWritingLv4 <= 0)
  showOrder[1] = 'N/A'
```
优化了法语浸润学校的排名显示逻辑。

建议的改进：

1. 错误处理：
```coffeescript
try
  privateSchool = await PrivateSchoolCol.findOne(...)
catch err
  debug.error "查询私立学校 #{privateSchoolName} 时出错:", err
```
建议添加更详细的错误处理和日志记录。

2. 性能优化：
```coffeescript
query = 
  $or: [
    { nm: schoolName },
    { nm: { $regex: schoolName, $options: 'i' } }
  ]
```
建议添加索引以优化查询性能。

3. 代码结构：
建议将学校名称映射和私立学校映射移到单独的配置文件中，便于维护。

4. 数据验证：
建议添加更多的数据验证，确保数据的完整性和正确性。

5. 文档完善：
建议为新增的函数和逻辑添加更详细的文档注释。

这些修改看起来是合理的，主要改进了：
1. 学校名称匹配的准确性
2. 私立学校的专门处理
3. 数据格式的一致性
4. 特殊情况的处理逻辑

没有发现明显的问题或潜在的风险。建议在测试环境中充分测试这些修改，特别是：
1. 私立学校的匹配逻辑
2. 法语浸润学校的特殊处理
3. 数据格式转换的准确性
4. 性能影响

---

