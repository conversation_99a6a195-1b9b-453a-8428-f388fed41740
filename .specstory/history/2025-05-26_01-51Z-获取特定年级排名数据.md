<!-- Generated by SpecStory -->

# 获取特定年级排名数据 (2025-05-26 01:51Z)

_**User**_

不同年级拥有的排名数量不一致，只获取有"#{grade}rmRanking"的数据

---

_**Assistant**_

<details>
            <summary>Searched codebase "只获取有"#{grade}rmRanking"的数据" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/libapp/schoolHelper.coffee` | L832-872 |
| `appweb/src/libapp/schoolHelper.coffee` | L753-802 |
| `appweb/src/libapp/schoolHelper.coffee` | L872-930 |
| `appweb/src/libapp/schoolHelper.coffee` | L622-690 |
| `appweb/src/libapp/schoolHelper.coffee` | L690-753 |
| `appweb/src/libapp/schoolHelper.coffee` | L802-832 |
| `appweb/src/libapp/schoolHelper.coffee` | L990-1063 |
| `appweb/src/libapp/schoolHelper.coffee` | L930-990 |
| `appweb/src/libapp/schoolHelper.coffee` | L1202-1281 |
| `appweb/src/libapp/propElasticSearch.coffee` | L1746-1801 |
| `appweb/src/libapp/schoolHelper.coffee` | L1106-1148 |
| `appweb/src/libapp/schoolHelper.coffee` | L1148-1202 |
| `appweb/src/libapp/schoolHelper.coffee` | L417-467 |
| `appweb/src/libapp/schoolHelper.coffee` | L64-105 |
| `appweb/src/libapp/cmtyTagHelper.coffee` | L348-378 |
| `appweb/src/libapp/cmtyTagHelper.coffee` | L179-257 |
| `appweb/src/libapp/schoolHelper.coffee` | L561-622 |
| `appweb/src/libapp/floorPlan.coffee` | L153-192 |
| `appweb/src/libapp/cmtyTagHelper.coffee` | L378-430 |
| `appweb/src/libapp/findschool_helpers.coffee` | L84-161 |
| `appweb/src/libapp/propertiesTranslate.coffee` | L1373-1443 |
| `appweb/src/libapp/propertyTagHelper.coffee` | L57-121 |
| `appweb/src/libapp/schoolHelper.coffee` | L105-161 |
| `appweb/src/libapp/schoolHelper.coffee` | L161-206 |
| `appweb/src/libapp/rmlisting.coffee` | L1-21 |

</details>

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

<details>
            <summary>Grep search for "exports.formatRmRankScore" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/libapp/schoolHelper.coffee` | L873 | `exports.formatRmRankScore = (school)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/libapp/schoolHelper.coffee` | L873 | `exports.formatRmRankScore = (school)->` |

</details>

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

```diff
  # @param {Object} school - 学校对象
  # @return {Object} rankScoreMap - 包含各年级ranking和score数据的映射对象
+ # @description 只保留有'#{grade}rmRanking'的年度数据
  ###
  exports.formatRmRankScore = (school)->
```

```diff
      subMap = {}
      for rm in rmrank
+       # 只处理有'#{grade}rmRanking'的年度
+       continue unless rm["#{grade}rmRanking"]?
        showOrder = []
        # 添加score数据,需要除以100并保留2位小数
```

---

已将 `formatRmRankScore` 函数修改为：**只保留有“#{grade}rmRanking”字段的年度数据**，其余年度将被自动跳过。这样每个年级只会输出有排名的年份，数据结构更为准确。

如需进一步调整或有其他需求，请继续告知！

---

