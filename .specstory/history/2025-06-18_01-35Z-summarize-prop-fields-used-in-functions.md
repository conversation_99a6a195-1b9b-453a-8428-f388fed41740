<!-- Generated by SpecStory -->

# Summarize prop fields used in functions (2025-06-18 01:35Z)

_**User**_

总结 getStatusLongName和getSaleTpTagAndTagColorAndLstStr function用到的所有prop字段

---

_**Assistant**_

根据你提供的 `getStatusLongName` 和 `getSaleTpTagAndTagColorAndLstStr` 两个函数的完整代码，下面是它们用到的所有 prop 字段总结（含直接和间接访问的字段）：

---

### getStatusLongName 用到的 prop 字段

- `prop.sp`  
  用于判断是否 Sold Over Asking

- `prop.lp`  
  用于判断是否 Sold Over Asking

- `prop.sldDom`  
  用于判断是否 SOLD IN DAYS

- `prop.saletp`  
  用于判断是 Sale 还是 Lease（影响 NEW/LEASE 状态）

- `prop.prevIDs`  
  用于判断是否 Relisted

---

### getSaleTpTagAndTagColorAndLstStr 用到的 prop 字段

- `prop.MlsStatus`  
  用于 MLS 状态映射

- `prop.src`  
  用于 MLS 状态映射

- `prop.status_en`  
  用于判断状态

- `prop.status`  
  用于判断状态

- `prop.saletp_en`  
  用于判断 Sale/Lease

- `prop.saletp`  
  用于判断 Sale/Lease

- `prop.sp`  
  用于判断 Sold/Leased

- `prop.lst`  
  用于判断 Sold/Leased/Delisted 及特殊状态

- `prop.historyRecord`  
  传递给 getStatusLongName 用于获取状态描述（实际传递的是 `{s:prop.lst}`）

---

### 详细字段列表（去重合并）

- MlsStatus
- src
- status_en
- status
- saletp_en
- saletp
- sp
- lp
- sldDom
- lst
- prevIDs
- historyRecord（实际为 lst 字段的包装）

---

#### 说明

- `getSaleTpTagAndTagColorAndLstStr` 还会间接调用 `getStatusLongName`，并传递 `{prop, historyRecord: {s:prop.lst}}`，所以 `getStatusLongName` 里用到的字段也会被用到。
- 其中 `mapping` 相关的字段是通过 `libImpCommon.statusLstMapping[mlsStatus]?[prop.src]` 获取的，不属于 prop 本身。
- 其它如 `tagColor`、`saleTpTag`、`lstStr` 等为函数内部变量或返回值，不属于 prop 字段。

---

#### 总结表

| 字段名         | 用途说明                         | 用于函数                |
|----------------|----------------------------------|-------------------------|
| MlsStatus      | MLS 状态映射                     | getSaleTpTagAndTagColorAndLstStr |
| src            | MLS 状态映射                     | getSaleTpTagAndTagColorAndLstStr |
| status_en      | 判断 Sale/Lease/Active           | getSaleTpTagAndTagColorAndLstStr |
| status         | 判断 Sale/Lease/Active           | 两者均用                |
| saletp_en      | 判断 Sale/Lease                  | 两者均用                |
| saletp         | 判断 Sale/Lease                  | 两者均用                |
| sp             | 判断 Sold/Leased/OverAsking      | 两者均用                |
| lp             | 判断 Sold Over Asking            | getStatusLongName       |
| sldDom         | 判断 SOLD IN DAYS                | getStatusLongName       |
| lst            | 判断 Sold/Leased/Delisted/特殊状态 | 两者均用                |
| prevIDs        | 判断 Relisted                    | getStatusLongName       |

如有需要可以进一步细化每个字段的具体用法。

---

