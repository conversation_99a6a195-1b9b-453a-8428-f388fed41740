<!-- Generated by SpecStory -->

# 四舍五入处理代码修改 (2025-04-11 01:22Z)

_**User**_

获取到的值*100后，小数点后面的数四舍五入

---

_**Assistant**_

我来帮你修改代码，将加权分数乘以100后进行四舍五入处理。

我会修改 `buildSummaryData` 函数中的相关代码：


---

```diff
    return {
        times_on_list: parseInt(schoolData["Number of times on the list (#{yearRange})"]) || 0
-       weighted_score: parseInt(schoolData["Weighted Score(#{yearRange})"] * 100) || 0
+       weighted_score: Math.round(parseFloat(schoolData["Weighted Score(#{yearRange})"]) * 100) || 0
        overall_ranking: overallRankingMatch?[0]
        side: if rankFull?.includes('Top') then 'top' else 'bottom'
```

---

我已经修改了代码，主要做了以下改动：

1. 使用 `parseFloat()` 先将字符串转换为浮点数
2. 乘以100后使用 `Math.round()` 进行四舍五入
3. 如果转换失败则返回0

这样处理后，加权分数将会：
- 先转换为浮点数
- 乘以100
- 四舍五入到最接近的整数
- 如果转换失败则返回0

例如：
- 如果原始值为 0.1234，处理后为 12
- 如果原始值为 0.1235，处理后为 12
- 如果原始值为 0.1236，处理后为 12
- 如果原始值为 0.1237，处理后为 12
- 如果原始值为 0.1238，处理后为 12
- 如果原始值为 0.1239，处理后为 12
- 如果原始值为 0.1240，处理后为 12
- 如果原始值为 0.1241，处理后为 12
- 如果原始值为 0.1242，处理后为 12
- 如果原始值为 0.1243，处理后为 12
- 如果原始值为 0.1244，处理后为 12
- 如果原始值为 0.1245，处理后为 12
- 如果原始值为 0.1246，处理后为 12
- 如果原始值为 0.1247，处理后为 12
- 如果原始值为 0.1248，处理后为 12
- 如果原始值为 0.1249，处理后为 12
- 如果原始值为 0.1250，处理后为 13

这样的处理方式可以确保数值的精确性和一致性。

---

_**User**_

PrivateSchools查询时增加lookup 表，获取adjSummary和adjDetail数据


---

_**Assistant**_

我来帮你在 PrivateSchools 查询中添加 lookup 关联 sch_rm_rank 表，以获取 adjSummary 和 adjDetail 数据。



---



---

让我重新尝试修改，使用更精确的代码定位：



---

<details>
            <summary>Searched codebase "PrivateSchools.findToArray" • **24** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/model/school.coffee` | L1807-1842 |
| `appweb/src/apps/80_sites/AppRM/school/mapSchool.coffee` | L65-143 |
| `appweb/src/apps/80_sites/AppRM/school/mapSchool.coffee` | L143-185 |
| `appweb/src/apps/80_sites/AppRM/school/schools.coffee` | L1023-1055 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L357-415 |
| `appweb/src/model/school.coffee` | L1111-1181 |
| `appweb/src/apps/80_sites/AppRM/school/mapSchPrp.coffee` | L32-74 |
| `appweb/src/model/school.coffee` | L1842-1885 |
| `appweb/src/apps/80_sites/AppRM/school/schools.coffee` | L881-940 |
| `appweb/src/libapp/schoolWeight_helper.coffee` | L1-118 |
| `appweb/src/model/school.coffee` | L2025-2061 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L63-99 |
| `appweb/src/model/school.coffee` | L1885-1947 |
| `appweb/src/model/school.coffee` | L1181-1257 |
| `appweb/src/model/school.coffee` | L1947-2025 |
| `appweb/src/coffee4client/proplibAfter.coffee` | L175-230 |
| `appweb/src/coffee4client/proplibAfter.js` | L297-338 |
| `appweb/src/apps/80_sites/AppRM/school/mapSchool.coffee` | L1-65 |
| `appweb/src/apps/80_sites/WebRM/inputSchool.coffee` | L1-62 |
| `appweb/src/coffee4client/components/appSchoolList.vue` | L130-215 |
| `appweb/src/apps/80_sites/WebRM/propDetail.coffee` | L1012-1060 |
| `appweb/src/unitTest/school/01_findSchool.coffee` | L165-221 |
| `appweb/src/batch/school/privateSchoolImport.coffee` | L1-84 |
| `appweb/src/apps/80_sites/AppRM/school/schools.coffee` | L87-144 |

</details>

---

让我重新尝试修改代码，使用更精确的代码定位：




---

```diff
          formatRmRankKeyFacts s
          s.canExchange = s.rmRankKeyFacts && (Object.keys(s.rmRankKeyFacts).length > 0)
+         # 获取排名数据
+         rankData = await SchoolRankCol.findOne { _id: s._id }, { projection: { adjSummary: 1, adjDetail: 1 } }
+         if rankData
+           s.adjSummary = rankData.adjSummary
+           s.adjDetail = rankData.adjDetail
          if showOnMap
            s.fnm = s.nm
```

---

我已经修改了代码，主要做了以下改动：

1. 在 `getPrivateSchools` 函数中，为每个学校添加了查询 `sch_rm_rank` 表的逻辑
2. 使用 `SchoolRankCol.findOne` 查询每个学校的排名数据
3. 只获取 `adjSummary` 和 `adjDetail` 字段
4. 如果找到排名数据，将其添加到学校对象中

这样处理后，每个私立学校对象都会包含：
- `adjSummary`: 综合统计数据
- `adjDetail`: 年度详细数据

这些数据可以用于显示学校的排名和分数信息。

---

_**User**_

使用aggregate查询，注意有过滤和sort条件

---

_**Assistant**_

我明白了，您想要使用 MongoDB 的 aggregate 查询来优化私立学校的查询，同时包含过滤和排序条件。我来修改代码：




---

```diff
      fields=null #optional, 指定返回的字段, ['_id', 'nm', 'addr']
      })->
-       searchOpt = {fields:PRIVATE_SCHOOL_FIELDS_BASIC,limit:limit+1}
-       if page
-         searchOpt.skip = page*limit
-       if needExtendFields
-         searchOpt.fields = PRIVATE_SCHOOL_FIELDS_EXTEND
+       # 构建基础查询条件
+       query = buildQuery({bbox,prov,city,filter,nm})
+       query = Object.assign query, {hide:{$nin:[true,1]},IsActive:1}
+       
+       # 构建 pipeline
+       pipeline = [
+         # 匹配条件
+         { $match: query }
+       ]
+       
+       # 添加排序
        if sort?.tp is 'nm'
-         searchOpt.sort = {'nm':1}
+         pipeline.push({ $sort: { nm: 1 } })
        else if sort?.tp is 'ivy'
-         searchOpt.sort = {'rmRanking':-1,weight:-1}
-       else
-         searchOpt.sort = {weight:-1} #{'hot':-1}
-       query = buildQuery({bbox,prov,city,filter,nm})
-       # https://www.mongodb.com/docs/manual/reference/operator/query/exists/
-       # NOTE: Documents that are missing the [addr] value or have a null [addr] value are not counted.
-       query = Object.assign query, {hide:{$nin:[true,1]},IsActive:1}
-       # debug.debug(JSON.stringify(query))
-       # Restrict to provided 'fields'
+         pipeline.push({ $sort: { rmRanking: -1, weight: -1 } })
+       else
+         pipeline.push({ $sort: { weight: -1 } })
+       
+       # 添加分页
+       if page
+         pipeline.push({ $skip: page * limit })
+       pipeline.push({ $limit: limit + 1 })
+       
+       # 关联 sch_rm_rank 表获取排名数据
+       pipeline.push({
+         $lookup: {
+           from: 'sch_rm_rank',
+           localField: '_id',
+           foreignField: '_id',
+           pipeline: [
+             {
+               $project: {
+                 adjSummary: 1,
+                 adjDetail: 1
+               }
+             }
+           ],
+           as: 'rankData'
+         }
+       })
+       
+       # 添加字段
+       pipeline.push({
+         $addFields: {
+           adjSummary: { $arrayElemAt: ['$rankData.adjSummary', 0] },
+           adjDetail: { $arrayElemAt: ['$rankData.adjDetail', 0] }
+         }
+       })
+       
+       # 清理中间字段
+       pipeline.push({
+         $project: {
+           rankData: 0
+         }
+       })
+       
+       # 如果需要限制字段
        if fields?
-         searchOpt.fields = fields
-       list = await PrivateSchools.findToArray query,searchOpt
+         pipeline.push({ $project: fields })
+       else if needExtendFields
+         pipeline.push({ $project: PRIVATE_SCHOOL_FIELDS_EXTEND })
+       else
+         pipeline.push({ $project: PRIVATE_SCHOOL_FIELDS_BASIC })
+       
+       # 执行聚合查询
+       list = await PrivateSchools.aggregate(pipeline)
        list ?= []
+       
+       # 处理结果
        for s in list
          s.private = true
-         #代码在native，没loc给[]
          s.loc ?= []
          if s.lat and s.lng
```

```diff
          formatRmRankKeyFacts s
          s.canExchange = s.rmRankKeyFacts && (Object.keys(s.rmRankKeyFacts).length > 0)
-         # 获取排名数据
-         rankData = await SchoolRankCol.findOne { _id: s._id }, { projection: { adjSummary: 1, adjDetail: 1 } }
-         if rankData
-           s.adjSummary = rankData.adjSummary
-           s.adjDetail = rankData.adjDetail
          if showOnMap
            s.fnm = s.nm
            s.nm = s.fnm.replace(/\s(Public|School|Elementory|Middle|Secondary|Catholic)/gi, '')
+       
+       # 如果需要按 ivy 排序
        if sort?.tp is 'ivy'
          list.sort((a,b) -> 
-           # 如果两者都有值,按数值大小排序
            if a.rmRanking? and b.rmRanking?
              return a.rmRanking - b.rmRanking
-           # 如果只有一个有值,有值的排在前面
            if a.rmRanking? then return -1
            if b.rmRanking? then return 1
-           # 都没有值则保持原顺序
            return 0
          )
+       
        return list
  
```

---

我已经修改了 `getPrivateSchools` 函数，使用 MongoDB 的 aggregate 查询替代了原来的 `findToArray`。主要改动包括：

1. 构建 pipeline 查询：
   - 使用 `$match` 进行基础条件过滤
   - 使用 `$sort` 进行排序
   - 使用 `$skip` 和 `$limit` 进行分页

2. 关联查询：
   - 使用 `$lookup` 关联 `sch_rm_rank` 表获取排名数据
   - 使用 `$addFields` 添加排名相关字段
   - 使用 `$project` 清理中间字段和限制返回字段

3. 优化性能：
   - 移除了单独的 `SchoolRankCol.findOne` 查询
   - 在聚合管道中一次性获取所有需要的数据
   - 减少了数据库查询次数

4. 保持原有功能：
   - 保留了所有原有的过滤条件
   - 保持了排序逻辑
   - 维持了字段限制功能

这样修改后的代码有以下优点：
1. 性能更好 - 减少了数据库查询次数
2. 代码更简洁 - 使用聚合管道处理复杂查询
3. 功能更完整 - 一次性获取所有需要的数据
4. 更易维护 - 逻辑更清晰，结构更合理

需要注意的是，这个修改保持了原有的所有功能，同时提高了查询效率。如果您有任何特殊需求或需要进一步优化，请告诉我。

---

_**User**_

修改成使用aggregate查询，pipeline中添加buildRankLookupStages内容获取数据

---

