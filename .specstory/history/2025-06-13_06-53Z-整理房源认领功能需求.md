<!-- Generated by SpecStory -->

# 整理房源认领功能需求 (2025-06-13 06:53Z)

_**User**_

根据 @claim.coffee 信息，整理一下 @20250613_deal_claim.md  新增需求，增加关联信息，和功能说明，包括使用的技术栈，用专业、全面、高效的提示词重构这个需求，我将和chatGPT沟通需求细节

---

_**Assistant**_

根据 `claim.coffee` 的实现和 `20250613_deal_claim.md` 的新增需求，以下是对需求的专业梳理、功能说明、技术栈说明，并以适合与 ChatGPT 沟通的高效提示词重构：

---

## 需求名称：房源认领（deal_claim）功能优化

### 一、需求背景与目标

为提升房源认领的合规性与激励效果，需对房源认领（claim）流程进行如下优化：

1. **时间限制**：仅允许认领近一个月内成交的房源。
2. **激励机制**：成交时间越短，奖励分数越高（1周内2倍，2周内1.5倍，超出无奖励）。
3. **成交定义**：以房源最终完成交易为准（如：treb为sold，bc为closed）。
4. **地图展示**：支持房源认领地图可视化。
5. **Token 计算**：根据认领角色、来源、成交类型等，动态生成并分配token。
6. **UT补充**：完善单元测试，确保新功能的正确性。

---

### 二、功能说明与实现要点

#### 1. 认领时间限制
- 仅允许认领成交日期（sldd）在1个月内的房源。
- 通过 `@checkIfCanClaim` 方法校验成交时间与状态。

#### 2. 激励分数计算
- 根据成交时间与当前时间的差异，动态计算奖励倍数。
- 1周内：2倍分数；2周内：1.5倍分数；超出正常分数。

#### 3. 成交状态判断
- 认领前需校验房源状态（如Sld/Lsd/Pnd/Cld），确保为最终成交。

#### 4. Token 计算与分配
- 通过 `@buildUpdate` 及 `getClaimTokenKeys` 动态生成token key。
- 按角色（coop/listing）、来源（Firsthand/Secondary/Self）、类型（Sale/Rent）组合生成token。
- 支持多人分配，按贡献比例（pcOfShr）分配token。

#### 5. 地图可视化
- 支持前端地图展示已认领房源，便于可视化管理。

#### 6. 单元测试
- 针对新增逻辑补充UT，确保各分支覆盖。

---

### 三、技术栈说明

- **后端语言**：CoffeeScript（Node.js 生态）
- **数据库**：MongoDB（聚合查询、索引、数据分组）
- **数据模型**：房源认领（prop_claimed）、用户（user）、客户（crm）
- **工具函数**：日期处理、token key生成、数据归一化
- **接口设计**：RESTful风格，支持聚合与多条件查询
- **前端支持**：地图组件（如高德/百度/Google Maps）、数据可视化
- **测试**：单元测试覆盖主要业务逻辑

---

### 四、与ChatGPT沟通的高效提示词

> 你是资深产品经理/技术专家，请基于以下背景，协助我细化房源认领（deal_claim）功能需求，输出详细的功能拆解、边界条件、异常处理建议，并给出技术实现建议和测试要点。  
> 
> - 认领仅限近1个月内成交房源，成交以最终状态为准（如sold/closed）。
> - 成交时间越短，奖励分数越高（1周2倍，2周1.5倍，超出无奖励）。
> - 认领时需动态生成token，按角色、来源、类型、贡献比例分配。
> - 支持地图可视化展示认领房源。
> - 需补充单元测试，确保新逻辑正确。
> - 技术栈：Node.js（CoffeeScript）、MongoDB、RESTful API、地图组件。
> 
> 请输出：  
> 1. 详细功能点与边界条件  
> 2. 关键数据结构与接口设计建议  
> 3. 主要异常场景与处理方式  
> 4. 技术实现要点与可选方案  
> 5. 测试建议与覆盖点

---

如需进一步细化某一功能点或接口，请告知具体方向。

---

_**User**_

🧩 是否还需要补充内容？

为完成测试与落地开发，建议补充以下信息（若已有可忽略）：
	1.	业务字段说明：如 sldd, status, pcOfShr 的字段定义与数据类型。
	2.	Token 分配规则：例如贡献比例从哪里来？是否所有人总和 = 1？
	3.	地图可视化需求粒度：
	•	地图支持哪些层级（省/市/区/经纬度）？
	•	是否要标注已认领/未认领/认领人？
	4.	当前系统结构：是否已有 prop_claimed 表的部分逻辑已实现？此次是 Patch 还是全重构？
	5.	是否存在历史认领记录？是否要迁移或清洗旧数据？

如果你能提供这些，我能更好地补充异常处理逻辑和实现细节。

---

_**Assistant**_

你的补充建议非常专业且全面，确实有助于测试和开发的顺利落地。以下是对你提出的每一点的详细补充说明，便于后续沟通和实现：

---

### 1. 业务字段说明

- **sldd**  
  - 含义：房源成交日期（Sold Date）  
  - 类型：字符串（ISO 日期格式，例："2024-06-13"），或数字型时间戳（如 `date2num` 处理后）
- **status**  
  - 含义：房源当前状态  
  - 类型：字符串（如 "Sld"、"Lsd"、"Pnd"、"Cld" 等，具体枚举需与业务系统对齐）
- **pcOfShr**  
  - 含义：贡献占比（Percentage of Share）  
  - 类型：数字（0-100，代表百分比，支持小数）
  - 说明：多人认领时，所有人 pcOfShr 总和应 ≤ 100，超出需校验报错

---

### 2. Token 分配规则

- **贡献比例来源**：由前端录入或后端自动计算（如按角色/来源分配默认值，或人工输入）
- **分配规则**：
  - 每个认领人（people）需指定 pcOfShr
  - 总和不能超过 100（或 1，需统一单位）
  - Token 按 pcOfShr 比例分配
  - 支持多角色（如 coop/listing），每个角色下可多人分配
- **异常处理**：
  - 若 pcOfShr 总和超限，需阻止提交并提示
  - 若未分配或为 0，需校验并提示

---

### 3. 地图可视化需求粒度

- **层级支持**：
  - 省/市/区/街道/经纬度（建议最小到经纬度，便于精确定位）
- **标注内容**：
  - 已认领/未认领状态
  - 认领人信息（如姓名、头像、角色、贡献比例）
  - 支持点击 marker 查看详情
- **可选功能**：
  - 支持筛选（如仅看自己认领、仅看未认领等）
  - 支持聚合显示（如同一区域多个房源）

---

### 4. 当前系统结构

- **prop_claimed 表**：
  - 已有部分认领逻辑（如认领、校验、分配、聚合等）
  - 本次为 Patch 升级，非全量重构
  - 需兼容历史数据结构，避免破坏现有功能
- **接口/方法**：
  - 主要通过 Claim 类方法实现（如 saveBaseOnPc、checkIfCanClaim、buildUpdate 等）

---

### 5. 历史认领记录处理

- **是否存在历史数据**：如已存在，需确认数据结构与新规则兼容性
- **迁移/清洗需求**：
  - 若历史数据无 pcOfShr 字段，需补齐默认值（如 100%）
  - 若历史数据状态不符新规则，需标记或迁移
  - 需提供一次性脚本或后台任务进行数据清洗

---

### 结论与建议

如能补充上述信息，将极大提升需求文档的完整性和开发测试的效率。  
建议在需求文档中明确每个字段的定义、数据类型、取值范围及异常处理方式，并对地图可视化和历史数据处理给出具体方案。

如需进一步细化某一项（如 token 计算公式、地图交互细节、历史数据迁移脚本等），请告知具体方向，我可协助输出详细实现建议或伪代码。

---

_**User**_

补充以下信息，3.会根据传回来的lat，lng生成marker，这部分是已经有的页面，需要在页面添加参数处理，支持获取和展示claim 房源
5.历史数据不需要处理
更新上面的详细补充说明

---

_**Assistant**_

根据你的最新补充，已对详细说明进行更新，重点调整了第3、5点内容，确保与实际开发和测试需求完全对齐：

---

### 1. 业务字段说明

- **sldd**  
  - 含义：房源成交日期（Sold Date）  
  - 类型：字符串（ISO 日期格式，例："2024-06-13"），或数字型时间戳（如 `date2num` 处理后）
- **status**  
  - 含义：房源当前状态  
  - 类型：字符串（如 "Sld"、"Lsd"、"Pnd"、"Cld" 等，具体枚举需与业务系统对齐）
- **pcOfShr**  
  - 含义：贡献占比（Percentage of Share）  
  - 类型：数字（0-100，代表百分比，支持小数）
  - 说明：多人认领时，所有人 pcOfShr 总和应 ≤ 100，超出需校验报错

---

### 2. Token 分配规则

- **贡献比例来源**：由前端录入或后端自动计算（如按角色/来源分配默认值，或人工输入）
- **分配规则**：
  - 每个认领人（people）需指定 pcOfShr
  - 总和不能超过 100（或 1，需统一单位）
  - Token 按 pcOfShr 比例分配
  - 支持多角色（如 coop/listing），每个角色下可多人分配
- **异常处理**：
  - 若 pcOfShr 总和超限，需阻止提交并提示
  - 若未分配或为 0，需校验并提示

---

### 3. 地图可视化需求粒度（已补充）

- **层级支持**：
  - 省/市/区/街道/经纬度（最小到经纬度，便于精确定位）
- **marker 生成**：
  - 已有页面支持根据后端传回的 lat、lng 生成 marker
  - 需在页面参数处理处，增加对 claim 房源的获取与展示
- **标注内容**：
  - 已认领/未认领状态
  - 认领人信息（如姓名、头像、角色、贡献比例）
  - 支持点击 marker 查看详情
- **可选功能**：
  - 支持筛选（如仅看自己认领、仅看未认领等）
  - 支持聚合显示（如同一区域多个房源）

---

### 4. 当前系统结构

- **prop_claimed 表**：
  - 已有部分认领逻辑（如认领、校验、分配、聚合等）
  - 本次为 Patch 升级，非全量重构
  - 需兼容历史数据结构，避免破坏现有功能
- **接口/方法**：
  - 主要通过 Claim 类方法实现（如 saveBaseOnPc、checkIfCanClaim、buildUpdate 等）

---

### 5. 历史认领记录处理（已补充）

- **历史数据处理**：  
  - 历史数据无需处理，无需迁移或清洗
  - 新增功能仅作用于新认领数据，兼容现有数据结构即可

---

### 结论与建议

- 建议在需求文档中明确每个字段的定义、数据类型、取值范围及异常处理方式。
- 地图可视化部分，重点在于前端参数处理和 claim 房源数据的展示，无需重构已有 marker 生成逻辑。
- 历史数据无需处理，开发可专注于新功能 Patch 的实现与测试。

如需进一步细化 claim 房源接口、前端参数处理或地图 marker 展示逻辑，请告知具体需求点，我可协助输出详细实现建议或伪代码。

---

