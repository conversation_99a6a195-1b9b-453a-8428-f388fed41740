<!-- Generated by SpecStory -->

# 细化修改内容和代码位置建议 (2025-06-16 16:19Z)

_**User**_

参考 @20250613_deal_claim.md 文件的 解决办法 部分，找出对应的修改位置，细化修改内容

---

_**Assistant**_

根据你提供的 `20250613_deal_claim.md` 文件的“解决办法”部分，下面是每一条修改建议的细化说明和对应的代码位置建议：

---

### 1. claim.coffee 修改 gClaimRange 限制为 30 天

**细化内容：**
- 找到 `claim.coffee` 文件中定义 gClaimRange 的地方（通常是一个常量或配置项）。
- 将其值设置为 30（天）。
- 检查所有用到 gClaimRange 的逻辑，确保限制生效。

**代码位置建议：**
- 可能在 `appweb/batch/claim.coffee` 或 `src/batch/claim.coffee`，具体路径需确认。
- 搜索 `gClaimRange` 关键字定位。

---

### 2. claim.coffee checkIfCanClaim 增加判断 saleTypeTag_en == 'Sold'

**细化内容：**
- 找到 `claim.coffee` 文件中的 `checkIfCanClaim` 方法或函数。
- 在判断逻辑中增加一条：只有当 `saleTypeTag_en` 字段为 `'Sold'` 时，才允许认领。
- 如果是 BC 地区，可能是 `Closed`，需根据地区适配。

**代码位置建议：**
- 依然在 `claim.coffee` 文件。
- 搜索 `checkIfCanClaim` 关键字定位。

---

### 3. /1.5/mapSearch?appMapMode=fav&mode=map 支持 appMapMode = claim 参数

**细化内容：**
- 找到 `/1.5/mapSearch` 接口的实现文件（可能在 `appweb/src/appDev3/mapSearch.js` 或类似文件）。
- 在参数解析处，支持 `appMapMode=claim`。
- 当为 `claim` 时，调用 `post '/propClaim/list'` 接口，获取已认领房源，并在地图上高亮显示。
- 需要前端和后端配合，前端负责传参和展示，后端负责数据返回。

**代码位置建议：**
- 后端接口实现文件 `/1.5/mapSearch`。
- 前端地图页面相关文件，可能在 `appweb/src/appDev3/saves/properties.js`、`appweb/src/appDev3/saves/deals.js`。

---

### 4. get '/1.5/saves/deals' 右上角可以点击 MAP 跳转到 mapsearch 页面

**细化内容：**
- 在 `/1.5/saves/deals` 页面右上角增加一个 MAP 按钮。
- 点击后跳转到 `/1.5/mapSearch?appMapMode=claim` 页面。
- 参考 `appweb/src/appDev3/saves/properties.js` 的实现，修改 `appweb/src/appDev3/saves/deals.js`。

**代码位置建议：**
- 前端页面 `appweb/src/appDev3/saves/deals.js`。
- 参考 `appweb/src/appDev3/saves/properties.js` 的 MAP 跳转逻辑。

---

### 5. 当 appMapMode = claim，通过 post '/propClaim/list' 获取已经 claim 的房源显示在地图上

**细化内容：**
- 在地图页面逻辑中，判断 `appMapMode=claim` 时，调用 `post '/propClaim/list'`。
- 获取已认领房源数据后，在地图上进行特殊标记或高亮显示。

**代码位置建议：**
- 地图页面的前端逻辑文件（如 `appweb/src/appDev3/mapSearch.js` 或相关组件）。
- 后端 `/propClaim/list` 接口实现文件。

---

### 总结

#### 主要涉及文件
- `appweb/batch/claim.coffee` 或 `src/batch/claim.coffee`
- `appweb/src/appDev3/saves/deals.js`
- `appweb/src/appDev3/saves/properties.js`（参考）
- `appweb/src/appDev3/mapSearch.js`（或地图相关文件）
- `/propClaim/list` 和 `/1.5/mapSearch`、`/1.5/saves/deals` 的接口实现

#### 主要修改点
1. gClaimRange 限制为 30 天
2. checkIfCanClaim 增加 saleTypeTag_en == 'Sold' 判断
3. 地图支持 appMapMode=claim，调用 propClaim/list
4. deals 页面增加 MAP 跳转
5. 地图上高亮已认领房源

如需进一步细化到每个函数/代码块级别，请告知具体 claim.coffee、mapSearch.js 等文件的路径，我可以帮你定位和生成详细注释代码。

---

