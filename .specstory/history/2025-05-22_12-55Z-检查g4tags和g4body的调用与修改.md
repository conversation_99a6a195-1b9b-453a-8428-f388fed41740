<!-- Generated by SpecStory -->

# 检查g4tags和g4body的调用与修改 (2025-05-22 12:55Z)

_**User**_

根据 解决办法 检索coffee中的g4tags和g4body的调用地方，根据文件格式修改代码

---

_**Assistant**_

<details>
            <summary>Grep search for "g4tags" • **8** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/lib/ckup.coffee` | L191 | `g4tags = (tags,config)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/WebRM/10_layout.coffee` | L60 | `g4tags js:new Date()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L690 | `g4tags js:new Date()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/user/socket.coffee` | L151 | `g4tags js:new Date()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/adPage.coffee` | L16 | `g4tags js:new Date()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/0_common/l10n.coffee` | L130 | `g4tags js:new Date()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/09_JiaJia_unused/index.coffee` | L32 | `g4tags js:new Date()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/0_common/l10n.coffee` | L130 | `g4tags js:new Date()` |

</details>

---

<details>
            <summary>Grep search for "g4body" • **8** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/lib/ckup.coffee` | L219 | `g4body = (config)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/WebRM/10_layout.coffee` | L131 | `g4body()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/user/socket.coffee` | L163 | `g4body()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L726 | `g4body()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/0_common/l10n.coffee` | L135 | `g4body()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/09_JiaJia_unused/index.coffee` | L41 | `g4body()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/adRelated/adPage.coffee` | L25 | `g4body()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/apps/80_sites/AppRM/0_common/l10n.coffee` | L135 | `g4body()` |

</details>

---

Read file: appweb/src/apps/80_sites/WebRM/10_layout.coffee

---

Read file: appweb/src/apps/80_sites/AppRM/x_index/index.coffee

---

Read file: appweb/src/apps/80_sites/AppRM/user/socket.coffee

---

Read file: appweb/src/apps/80_sites/AppRM/adRelated/adPage.coffee

---

Read file: appweb/src/apps/80_sites/AppRM/0_common/l10n.coffee

---

Read file: appweb/src/apps/80_sites/09_JiaJia_unused/index.coffee

---

我们已经找到了所有涉及 `g4tags` 和 `g4body` 的 CoffeeScript 文件及其调用位置。接下来，我们需要根据需求文档中的解决方案，在调用 `g4tags` 和 `g4body` 之前添加对国内 IP 的判断逻辑。具体来说，我们需要在每个调用 `g4tags` 和 `g4body` 的地方，添加一个条件判断 `if req.isChinaIP()`，以避免在国内 IP 的情况下请求 Google。

以下是需要修改的文件和行号：

1. `appweb/src/apps/80_sites/WebRM/10_layout.coffee`
   - `g4tags` 在第 60 行
   - `g4body` 在第 131 行

2. `appweb/src/apps/80_sites/AppRM/x_index/index.coffee`
   - `g4tags` 在第 690 行
   - `g4body` 在第 726 行

3. `appweb/src/apps/80_sites/AppRM/user/socket.coffee`
   - `g4tags` 在第 151 行
   - `g4body` 在第 163 行

4. `appweb/src/apps/80_sites/AppRM/adRelated/adPage.coffee`
   - `g4tags` 在第 16 行
   - `g4body` 在第 25 行

5. `appweb/src/apps/80_sites/AppRM/0_common/l10n.coffee`
   - `g4tags` 在第 130 行
   - `g4body` 在第 135 行

6. `appweb/src/apps/80_sites/09_JiaJia_unused/index.coffee`
   - `g4tags` 在第 32 行
   - `g4body` 在第 41 行

接下来，我将对这些文件进行修改。

---

```diff
        meta name:'robots', content:'noodp' unless @noindex
        meta name:'robots', content:'noindex' if @noindex
-       g4tags js:new Date()
+       if not @req.isChinaIP()
+         g4tags js:new Date()
        meta itemprop:'description', content: @vctx.description if @vctx.description
        meta itemprop:'inLanguage', content: @vctx.lang if @vctx.lang
```

```diff
      block 'body', ->
        body ->
-         g4body()
+         if not @req.isChinaIP()
+           g4body()
          user = @req.session.get('user')
          if @side
```

---

```diff
        # meta name:"format-detection", content="telephone=yes"
        meta name:"Access-Control-Allow-Origin", value:"*" if @cors
-       g4tags js:new Date()
+       if not @req.isChinaIP()
+         g4tags js:new Date()
        headTitle = @vctx.title or @req.setting?.meta?.name or _('RealMaster')
        title -> headTitle
```

```diff
          bodyStyle = @bodyStyle
        body style:bodyStyle,->
-         g4body()
+         if not @req.isChinaIP()
+           g4body()
          text @content()
          # text ckup 'userModal' unless @noUserModal or (@req.getDevType() isnt 'app')
```

---

```diff
        meta name:'apple-mobile-web-app-status-bar-style',content:'black-translucent'
        meta name:'format-detection',content:'telephone=no'
-       g4tags js:new Date()
+       if not @req.isChinaIP()
+         g4tags js:new Date()
        title -> @vctx.title or _ 'RealMaster'
        css '/css/ratchet.min.css?v=1.8' unless @noRatchet
```

```diff
        text ckup 'cordovaJs',{wxcfg:@wxcfg} unless @noCordova
      body ->
-       g4body()
+       if not @req.isChinaIP()
+         g4body()
        text @content()
        #img src:@req.getLogParams(),id:'imgNotUsed',onload:'var _e$=document.getElementById('imgNotUsed');_e$.parentNode.removeChild(_e$);'
```

---

```diff
        if @jump
          meta httpEquiv:"refresh", content:"1;URL=#{@url}"
-       g4tags js:new Date()
+       if not @req.isChinaIP()
+         g4tags js:new Date()
        title -> _ "RealMaster"
  
```

```diff
      position: absolute;
      margin: 0;',->
-         g4body()
+         if not @req.isChinaIP()
+           g4body()
          text @content()
  
```

---

```diff
        meta name:"format-detection",content:"telephone=no"
        meta name:'googlebot',content:'noindex,nofollow' if @noIndex
-       g4tags js:new Date()
+       if not @req.isChinaIP()
+         g4tags js:new Date()
        title -> _ "RealMaster"
        js '/js/jquery-1.10.1.min.js'
        js '/js/angular.min.js'
      body ->
-       g4body()
+       if not @req.isChinaIP()
+         g4body()
        div -> a href:'/1.5/index',-> _ "Back"
        div class:'rootbox',-> text @content()
```

---

```diff
- 
  APP 'spevent'
  VIEW 'cordovaJs', ->
```

```diff
        meta name:"format-detection", content:"telephone=no"
        meta name:"referrer", content:"no-referrer" if @noref
-       g4tags js:new Date()
+       if not @req.isChinaIP()
+         g4tags js:new Date()
        title -> @vctx.title or _ "RealMaster"
        css '/css/ratchet.min.css'
```

```diff
      block 'body', ->
        body ->
-         g4body()
+         if not @req.isChinaIP()
+           g4body()
          text @content()
          #img src:@req.getLogParams(),id:'imgNotUsed',onload:"var _e$=document.getElementById('imgNotUsed');_e$.parentNode.removeChild(_e$);"
```

---

所有涉及 `g4tags` 和 `g4body` 的 CoffeeScript 文件已经根据需求文档中的解决方案进行了修改。在每个调用 `g4tags` 和 `g4body` 的地方，添加了一个条件判断 `if not @req.isChinaIP()`，以避免在国内 IP 的情况下请求 Google。

如果还有其他需要帮助的地方，请随时告诉我！

---

_**User**_

检查html中，调用 gtmjs 的地方，根据文件格式修改代码

---

_**Assistant**_

<details>
            <summary>Searched codebase "gtmjs" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/appDev3/map/mapbox-map.js` | L257-262 |
| `appweb/src/coffee4client/rmapp.coffee` | L547-625 |
| `appweb/src/lib/ckup.coffee` | L192-239 |
| `appweb/src/theme_unused/homePage.html` | L112-119 |
| `appweb/src/webroot/public/web/login.html` | L25-53 |
| `appweb/src/themes/website/layout.html` | L1-49 |
| `appweb/docs/Dev_tracking/google.md` | L1-24 |
| `appweb/src/webroot/public/web/register.html` | L23-51 |
| `appweb/src/webroot/public/html/propDetailPage.html` | L1-36 |
| `appweb/src/themes/rmApp/homePageMls.html` | L138-168 |
| `appweb/src/webroot/public/html/projDetailPage.html` | L1-35 |
| `appweb/src/apps/80_sites/WebRM/login.coffee` | L183-216 |
| `appweb/src/themes/rmApp/homePageRm.html` | L108-160 |
| `appweb/src/coffee4client/diagnose_test.js` | L1-74 |
| `appweb/src/webroot/public/js/hammer.min.js` | L1-6 |
| `appweb/src/webroot/public/js/jma/jquery-mobile-angular-adapter-standalone.js` | L28296-28357 |
| `appweb/src/webroot/public/errorPage/error.html` | L1-43 |
| `appweb/src/webroot/public/js/measure.js` | L1-60 |
| `appweb/src/webroot/public/js/entry/appTransitHome.js` | L85-99 |
| `appweb/src/coffee4client/rmapp.coffee` | L427-467 |
| `appweb/src/coffee4client/components/appMapMixView.vue` | L118-162 |
| `appweb/src/webroot/public/js/moment-timezone-with-data.min.js` | L1-6 |
| `appweb/src/webroot/public/js/google/markerclusterer.js` | L2-73 |
| `appweb/src/webroot/public/js/qrcode/gif.js` | L1-44 |
| `appweb/src/webroot/public/js/vue.js` | L3571-3595 |

</details>

---

```diff
  {{?}}
  <script>
-   if(window.gtmjs){
+   if(window.gtmjs && !it.isChinaIP){
      gtmjs({tags:{js:new Date()}})
    }
```

---

```diff
      <script src="/js/entry/propDetailPage.js"></script>
      <script>
-       if(window.gtmjs){
+       // 仅在非中国IP时调用gtmjs
+       if(window.gtmjs && !(window.dispVar && window.dispVar.isCip)){
          gtmjs({tags:{js:new Date()}})
        }
```

---

```diff
      <script src="/js/entry/projDetailPage.js"></script>
      <script>
-       if(window.gtmjs){
+       // 仅在非中国IP时调用gtmjs
+       if(window.gtmjs && !(window.dispVar && window.dispVar.isCip)){
          gtmjs({tags:{js:new Date()}})
        }
```

---

```diff
     </script>
      <script>
-       if(window.gtmjs){
+       // 仅在非中国IP时调用gtmjs
+       if(window.gtmjs && !(window.dispVar && window.dispVar.isCip)){
          gtmjs({tags:{js:new Date()}})
        }
```

---

```diff
     <script src="/web/packs/loginPage.js"></script>
      <script>
-       if(window.gtmjs){
+       // 仅在非中国IP时调用gtmjs
+       if(window.gtmjs && !(window.dispVar && window.dispVar.isCip)){
          gtmjs({tags:{js:new Date()}})
        }
```

---

```diff
        <script src="/js/rmapp.min.js"></script>
        <script>
-          if(window.gtmjs){
-            gtmjs({tags:{js:new Date()}})
-          }
+         // 仅在非中国IP时调用gtmjs
+         if(window.gtmjs && !(window.dispVar && window.dispVar.isCip)){
+           gtmjs({tags:{js:new Date()}})
+         }
        </script>
        <script src="/js/ratchet.mi.js"></script>
```

---

```diff
    <script type="text/javascript">
      loadLazyImg();
-     if(window.gtmjs){
+     if(window.gtmjs && !(window.dispVar && window.dispVar.isCip)){
        gtmjs({tags:{js:new Date()}})
      }
```

---

```diff
    <script type="text/javascript">
      loadLazyImg();
-     if(window.gtmjs){
+     if(window.gtmjs && !(window.dispVar && window.dispVar.isCip)){
        gtmjs({tags:{js:new Date()}})
      }
```

---

已完成以下修改，确保所有 HTML 页面在调用 `gtmjs` 前，均会判断是否为中国IP，仅在非中国IP时才执行 Google Tag Manager 相关代码：

- 模板类页面（如 `layout.html`）使用 `it.isChinaIP` 判断。
- 纯前端页面（如 `propDetailPage.html`、`projDetailPage.html`、`login.html`、`register.html`、`error.html`、`homePageMls.html`、`homePageRm.html`）使用 `window.dispVar?.isCip` 判断。

**实现逻辑：**
```js
// 仅在非中国IP时调用gtmjs
if(window.gtmjs && !(window.dispVar && window.dispVar.isCip)){
  gtmjs({tags:{js:new Date()}})
}
```
或
```js
if(window.gtmjs && !it.isChinaIP){
  gtmjs({tags:{js:new Date()}})
}
```

如需适配更多页面或有特殊判断需求，请告知！

---

