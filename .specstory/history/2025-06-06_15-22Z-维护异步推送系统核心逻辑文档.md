<!-- Generated by SpecStory -->

# 维护异步推送系统核心逻辑文档 (2025-06-06 15:22Z)

_**User**_

帮我维护这份异步推送系统的核心逻辑文档。系统基于任务队列驱动推送流程，从任务创建、分组处理、并发推送到 badge 更新与错误处理等都有清晰的生命周期管理。我会不断补充细节，请根据已有结构继续维护该技术文档，确保格式统一、重点突出、结构清晰，适合协同开发和后续维护使用。

当前系统核心概览包括：
核心流程图：展示了任务的创建、轮询、处理、推送、更新与清理。

数据库结构：

tasks: 推送任务

users: 用户信息

pn_errors: 记录推送失败日志

调度与批次控制：

Scheduler: 周期性调度拉取优先级最高任务（findOneAndDelete 保证唯一消费）

Push Worker: 查询用户、按平台/语言/Badge分组、并发推送

分组规则：

iOS: 分组格式为 {locale: {pnBadge: [token]}}

Android: 分组格式为 {locale: [token]}，无需 badge

推送逻辑：

使用 p-limit 控制最大并发数

推送成功后，批量更新用户 pnBadge

推送失败记录进 pn_errors

配置项（在顶层定义）：

BATCH_INTERVAL_MINUTES

MAX_PN_PER_TASK

MAX_CONCURRENT_PUSHES

特别要求：
代码示意可保留 CoffeeScript（可后续转 JS/TS）

所有流程应图 + 文 + 示例代码清晰表达

保持逻辑一致性、适合后续多人维护


根据上面的信息，你能理解我的需求吗？整理一下文字描述，帮我理解需求

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要我帮助维护一份异步推送系统的核心逻辑文档。让我先理解一下他的需求：

1. 这是一个基于任务队列的推送系统
2. 包含完整的生命周期管理：任务创建、分组处理、并发推送、badge更新、错误处理等
3. 用户会不断补充细节，需要我维护技术文档
4. 要求格式统一、重点突出、结构清晰，适合协同开发和维护

从描述中可以看出系统的核心组件：
- 核心流程图
- 数据库结构（tasks、users、pn_errors）
- 调度与批次控制（Scheduler、Push Worker）
- 分组规则（iOS和Android不同）
- 推送逻辑（并发控制、成功/失败处理）
- 配置项

用户希望我：
1. 理解当前需求
2. 整理文字描述
3. 后续帮助维护这个技术文档，保持逻辑一致性

我需要用中文回复，并且理解他想要建立一个完整的技术文档体系。</details></think>

---

我完全理解您的需求！让我整理一下您要维护的异步推送系统技术文档的核心要求：

## 📋 需求概述

您希望建立并维护一份**异步推送系统的核心逻辑技术文档**，该文档需要：

### 🎯 文档目标
- **协同开发友好**：多人团队可以快速理解系统架构
- **维护导向**：便于后续功能迭代和问题排查
- **结构清晰**：格式统一、重点突出、逻辑一致

### 🏗️ 系统架构概览
您的推送系统是一个**基于任务队列驱动的分布式推送架构**：

```
任务创建 → 优先级队列 → 调度器轮询 → 分组处理 → 并发推送 → Badge更新 → 错误处理
```

### 📚 文档核心模块

1. **🔄 核心流程图**
   - 任务生命周期可视化
   - 从创建到清理的完整流程

2. **🗄️ 数据库设计**
   - `tasks`: 推送任务表
   - `users`: 用户信息表  
   - `pn_errors`: 推送失败日志表

3. **⚙️ 调度机制**
   - **Scheduler**: 周期性任务拉取（`findOneAndDelete`保证唯一消费）
   - **Push Worker**: 用户查询 + 平台分组 + 并发推送

4. **📱 平台分组策略**
   - **iOS**: `{locale: {pnBadge: [token]}}`（支持Badge）
   - **Android**: `{locale: [token]}`（无Badge需求）

5. **🚀 推送执行逻辑**
   - `p-limit` 并发控制
   - 成功：批量更新用户Badge
   - 失败：记录至`pn_errors`

6. **⚙️ 系统配置**
   - `BATCH_INTERVAL_MINUTES`: 批次间隔
   - `MAX_PN_PER_TASK`: 单任务最大推送数
   - `MAX_CONCURRENT_PUSHES`: 最大并发数

### 📝 文档维护要求
- 保留**CoffeeScript**代码示例（可后续迁移JS/TS）
- **图 + 文 + 代码**三位一体的表达方式
- 持续迭代，保持技术细节的完整性

**您希望我作为文档维护助手，帮助您持续完善这份技术文档，对吗？**

我准备好协助您建立一个完整、清晰、易维护的推送系统技术文档体系！

---

