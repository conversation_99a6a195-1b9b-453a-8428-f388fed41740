<!-- Generated by SpecStory -->

# 增加console.log信息以检查状态 (2025-06-12 11:53Z)

_**User**_

给文件增加console。log信息，用来检查执行状态

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要在 `watchTasks.coffee` 文件中增加 console.log 信息来检查执行状态。我需要仔细查看代码，找到关键的执行点并添加 console.log 语句来帮助调试和监控程序的执行状态。

从代码来看，这是一个推送通知的批处理器，主要有以下几个关键流程：
1. 主函数启动
2. 任务调度器运行
3. 推送工作器处理任务
4. 处理推送结果
5. 错误处理和状态更新

我需要在这些关键点添加 console.log，同时保持现有的 debug 语句。我会使用中文来添加这些 console.log 信息。

关键地方需要添加 console.log：
1. 主函数开始和结束
2. 任务调度器开始和关键状态变化
3. 推送工作器处理任务的关键步骤
4. 推送成功/失败的结果
5. 错误发生时的信息
6. 进程状态更新</details></think>

---

我来为这个推送通知批处理器文件添加 console.log 信息，以便更好地检查执行状态。我会在关键的执行点添加日志输出。

---

```diff
    Run frequency: 持续运行，定时轮询
  ###
+ 
+ console.log '[watchTasks] 开始初始化推送通知批处理器...'
  
  helpersFunction = INCLUDE 'lib.helpers_function'
```

```diff
  PushNotifyModel = MODEL 'PushNotify'
  ProcessStatusCol = COLLECTION('vow','processStatus')
+ 
+ console.log '[watchTasks] 依赖库和模型加载完成'
  
  # 创建推送通知实例
  config = CONFIG(['apn','serverBase','fcmv1_config'])
  pushNotifyInstance = pushNotifyV2.createInstance config
+ 
+ console.log '[watchTasks] 推送通知实例创建完成'
  
  # 创建速度统计器
```

```diff
  PROCESS_STATUS_ID = 'watchTasks'
  MAX_CONSECUTIVE_ERRORS = 10  # 最大连续错误次数
+ 
+ console.log "[watchTasks] 配置参数设置完成 - 轮询间隔: #{BATCH_INTERVAL_MINUTES}分钟, 最大并发: #{MAX_CONCURRENT_PUSHES}"
  
  # 如果是测试环境，设置更短的轮询间隔
  if process.argv.includes('test')
    BATCH_INTERVAL_MINUTES = 0.5
+   console.log '[watchTasks] 检测到测试环境，设置轮询间隔为0.5分钟'
  
  # 添加退出标志和监控指标
```

```diff
    consecutiveErrors: 0
  }
+ 
+ console.log '[watchTasks] 监控指标初始化完成'
  
  {
```

```diff
  
  debug.info 'BATCH_FILE_NAME', BATCH_FILE_NAME
+ console.log "[watchTasks] 进程处理器初始化完成, BATCH_FILE_NAME: #{BATCH_FILE_NAME}"
  
  gracefulExit = (error) ->
    shouldExit = true
+   console.log '[watchTasks] 开始优雅退出...'
    debug.error 'gracefulExit error', error if error
    debug.info '最终监控报告:', JSON.stringify(monitorMetrics)
+   console.log "[watchTasks] 最终监控报告: #{JSON.stringify(monitorMetrics)}"
    handleExit {error, exitFn: EXIT}
  
  processKillSignal PROCESS_STATUS_ID, gracefulExit
  
- process.on 'uncaughtException', gracefulExit
+ process.on 'uncaughtException', (error) ->
+   console.log '[watchTasks] 捕获到未处理异常:', error.message
+   gracefulExit(error)
  
  # 并发限制器
  limit = pLimit(MAX_CONCURRENT_PUSHES)
+ 
+ console.log '[watchTasks] 并发限制器创建完成'
  
  ###*
```

```diff
  ###
  processTask = (task) ->
+   console.log "[processTask] 开始处理任务: #{task._id}"
    debug.info '开始处理任务:', task._id, JSON.stringify(task.targetUser)
    
    unless task.targetUser
+     console.log "[processTask] 警告：任务没有目标用户配置: #{task._id}"
      debug.warn '任务没有目标用户配置:', task._id
      speedMeter.check {noTargetUser: 1}
```

```diff
    # 1. 根据targetUser配置查询用户，使用Forum.getTargetUserListPn
    try
+     console.log "[processTask] 开始查询目标用户..."
      users = await ForumModel.getTargetUserListPn task.targetUser, 'zh'
+     console.log "[processTask] 查询到用户数量: #{users?.length}"
      debug.info '查询到用户数量:', users?.length
      speedMeter.check {queryUserCount: users?.length or 0}
    catch error
+     console.log "[processTask] 查询用户失败: #{error.message}"
      debug.error '查询用户失败:', error
      speedMeter.check {queryUserFailed: 1}
      return {errorList: [error.message], failedList: [], successList: []}
    
    unless users?.length
+     console.log '[processTask] 警告：未找到有效用户'
      debug.warn '未找到有效用户'
      speedMeter.check {noValidUsers: 1}
      return {errorList: [], failedList: [], successList: []}
    
    # 2. 用户分组 - 按设备类型和语言区域分组
+   console.log '[processTask] 开始用户设备分组...'
    iosGroups = {}  # {locale: {pnBadge: []}}
    andGroups = {}  # {locale: []}
```

```diff
      androidCount += tokens.length
    
+   console.log "[processTask] 设备分组完成 - iOS: #{iosCount}, Android: #{androidCount}"
    debug.info "设备分组完成 - iOS: #{iosCount}, Android: #{androidCount}"
    speedMeter.check {iosDeviceCount: iosCount, androidDeviceCount: androidCount}
```

```diff
    }
    
+   console.log '[processTask] 开始并发推送处理...'
  
    # 处理 iOS 推送
    if iosCount > 0
+     console.log "[processTask] 开始处理 iOS 推送，设备数量: #{iosCount}"
      iosPromises = []
      for locale, badgeGroups of iosGroups
```

```diff
          limit () ->
            try
+             console.log "[processTask] 发送 iOS 推送 (#{currentLocale})..."
              result = await pushNotifyInstance.sendToIOSDevices(currentNotice, currentBadgeGroups, MAX_PN_PER_TASK)
+             console.log "[processTask] iOS推送完成 (#{currentLocale}) - 成功: #{result.successList?.length}, 失败: #{result.failedList?.length}"
              debug.info "iOS推送完成 (#{currentLocale}) - 成功: #{result.successList?.length}, 失败: #{result.failedList?.length}"
              speedMeter.check {iosPushSuccess: result.successList?.length or 0, iosPushFailed: result.failedList?.length or 0}
              return result
            catch error
+             console.log "[processTask] iOS推送失败 (#{currentLocale}): #{error.message}"
              debug.error "iOS推送失败 (#{currentLocale}):", error
              speedMeter.check {iosPushError: 1}
```

```diff
      
      # 等待所有 iOS 推送完成
+     console.log '[processTask] 等待所有 iOS 推送完成...'
      iosResults = await Promise.all(iosPromises)
      for result in iosResults
```

```diff
    # 处理 Android 推送
    if androidCount > 0
+     console.log "[processTask] 开始处理 Android 推送，设备数量: #{androidCount}"
      androidPromises = []
      for locale, tokens of andGroups
```

```diff
            
            try
+             console.log "[processTask] 发送 Android 推送 (#{currentLocale})..."
              result = await pushNotifyInstance.sendToAndroidFcmDevices(currentNotice, androidDevices, MAX_PN_PER_TASK)
+             console.log "[processTask] Android推送完成 (#{currentLocale}) - 成功: #{result.successList?.length}, 失败: #{result.failedList?.length}"
              debug.info "Android推送完成 (#{currentLocale}) - 成功: #{result.successList?.length}, 失败: #{result.failedList?.length}"
              speedMeter.check {androidPushSuccess: result.successList?.length or 0, androidPushFailed: result.failedList?.length or 0}
              return result
            catch error
+             console.log "[processTask] Android推送失败 (#{currentLocale}): #{error.message}"
              debug.error "Android推送失败 (#{currentLocale}):", error
              speedMeter.check {androidPushError: 1}
```

```diff
      
      # 等待所有 Android 推送完成
+     console.log '[processTask] 等待所有 Android 推送完成...'
      androidResults = await Promise.all(androidPromises)
      for result in androidResults
```

```diff
        allResults.successList = allResults.successList.concat(result.successList or [])
    
+   console.log "[processTask] 任务推送完成: #{task._id}, 总成功: #{allResults.successList.length}, 总失败: #{allResults.failedList.length}"
    debug.info "任务推送完成:", task._id,
    speedMeter.check { totalPushSuccess: allResults.successList.length, totalPushFailed: allResults.failedList.length}
```

```diff
  ###
  pushWorker = (task) ->
+   console.log "[pushWorker] 推送工作器开始处理任务: #{task._id}"
    debug.info '推送工作器开始处理任务:', task._id
    return unless task
```

```diff
    isTaskProcessing = true
    taskStartTime = Date.now()
+   console.log "[pushWorker] 任务处理开始，时间戳: #{taskStartTime}"
    
    try
      # 1. 处理任务推送
+     console.log "[pushWorker] 步骤1: 开始执行任务推送..."
      pushResult = await processTask(task)
      
      # 2. 后续处理
+     console.log "[pushWorker] 步骤2: 开始后续处理..."
      # 2.1 处理成功的推送
      if pushResult.successList?.length > 0
        try
+         console.log "[pushWorker] 步骤2.1: 清理成功推送的错误记录，数量: #{pushResult.successList.length}"
          await PushNotifyModel.removeErrorPn pushResult.successList
          debug.info "已清理成功推送的错误记录，数量: #{pushResult.successList.length}"
          speedMeter.check {removedErrorPn: pushResult.successList.length}
        catch error
+         console.log "[pushWorker] 清理成功推送错误记录失败: #{error.message}"
          debug.error '清理成功推送错误记录失败:', error
          speedMeter.check {removeErrorPnFailed: 1}
```

```diff
      if pushResult.failedList?.length > 0
        try
+         console.log "[pushWorker] 步骤2.2: 处理失败推送记录，数量: #{pushResult.failedList.length}"
          await PushNotifyModel.removeFailedPn pushResult.failedList
          debug.info "已处理失败推送记录，数量: #{pushResult.failedList.length}"
          speedMeter.check {removedFailedPn: pushResult.failedList.length}
        catch error
+         console.log "[pushWorker] 处理失败推送记录失败: #{error.message}"
          debug.error '处理失败推送记录失败:', error
          speedMeter.check {removeFailedPnFailed: 1}
```

```diff
      if iosSuccessTokens?.length > 0
        try
+         console.log "[pushWorker] 步骤2.3: 更新iOS用户badge，数量: #{iosSuccessTokens.length}"
          await PushNotifyModel.updateBadgeCount iosSuccessTokens
          debug.info "已更新iOS用户badge，数量: #{iosSuccessTokens.length}"
          speedMeter.check {updatedBadgeCount: iosSuccessTokens.length}
        catch error
+         console.log "[pushWorker] 更新iOS用户badge失败: #{error.message}"
          debug.error '更新iOS用户badge失败:', error
          speedMeter.check {updateBadgeFailed: 1}
```

```diff
      try
        totalSuccessCount = pushResult.successList?.length || 0
+       console.log "[pushWorker] 步骤3: 标记任务完成，成功推送用户数: #{totalSuccessCount}"
        await TaskModel.markTaskAsDone task._id, speedMeter.values
  
- 
+       console.log "[pushWorker] 任务已标记为完成: #{task._id}"
        debug.info '任务已标记为完成:', task._id, "成功推送用户数: #{totalSuccessCount}"
        speedMeter.check {taskCompleted: 1}
      catch error
+       console.log "[pushWorker] 标记任务完成失败: #{error.message}, 任务ID: #{task._id}"
        debug.error '标记任务完成失败:', error, task._id
        speedMeter.check {markTaskDoneFailed: 1}
```

```diff
      monitorMetrics.consecutiveErrors = 0  # 重置连续错误计数
      
+     console.log "[pushWorker] 任务 #{task._id} 处理完成，耗时: #{taskProcessTime}s"
      debug.info "任务 #{task._id} 处理完成，耗时: #{taskProcessTime}s, 统计信息: #{speedMeter.toString()}"
      
      # 定期输出监控报告
      if monitorMetrics.totalTasksProcessed % 10 == 0
+       console.log "[pushWorker] 监控报告: #{JSON.stringify(monitorMetrics)}"
        debug.info "监控报告:", JSON.stringify(monitorMetrics)
      
```

```diff
      
    catch error
+     console.log "[pushWorker] 推送工作器处理失败: #{error.message}"
      debug.error '推送工作器处理失败:', error
      speedMeter.check {workerFailed: 1}
```

```diff
      # 打印错误统计信息并清除统计
      taskProcessTime = (Date.now() - taskStartTime) / 1000
+     console.log "[pushWorker] 任务 #{task._id} 处理失败，耗时: #{taskProcessTime}s"
+     console.log "[pushWorker] 错误监控指标: #{JSON.stringify(monitorMetrics)}"
      debug.error "任务 #{task._id} 处理失败，耗时: #{taskProcessTime}s, 统计信息: #{speedMeter.toString()}"
      debug.error "错误监控指标:", JSON.stringify(monitorMetrics)
```

```diff
      # 标记任务处理完成
      isTaskProcessing = false
+     console.log "[pushWorker] 任务处理状态已重置为空闲"
  
  ###*
```

```diff
  ###
  taskScheduler = ->
+   console.log '[taskScheduler] 任务调度器开始运行'
    debug.info '任务调度器开始运行'
    
```

```diff
        now = Date.now()
        if now - lastStatusUpdate > UPDATE_PROCESS_STATE_INTERNAL
+         console.log '[taskScheduler] 更新进程状态...'
          await updateProcessStatusForTasks(new Date())
          lastStatusUpdate = now
+         console.log '[taskScheduler] 进程状态更新完成'
          debug.info '已更新进程状态'
        
        # 2. 检查连续错误次数
        if monitorMetrics.consecutiveErrors >= MAX_CONSECUTIVE_ERRORS
+         console.log "[taskScheduler] 错误：连续错误次数过多: #{monitorMetrics.consecutiveErrors}, 退出程序"
          debug.error "连续错误次数过多: #{monitorMetrics.consecutiveErrors}, 退出程序"
          break
        
        # 3. 检查是否有任务正在处理
        if isTaskProcessing
+         console.log '[taskScheduler] 有任务正在处理中，跳过查找新任务'
          debug.debug '有任务正在处理中，跳过查找新任务'
        else
          # 查询并更新优先级最高的任务状态
+         console.log '[taskScheduler] 查找待处理任务...'
          task = await TaskModel.findOneAndUpdateHighestPriority()
          
          if task
+           console.log "[taskScheduler] 找到待处理任务: #{task._id}, 优先级: #{task.priority}"
            debug.info '找到待处理任务:', task._id, task.priority
            
```

```diff
            
          else
+           console.log '[taskScheduler] 没有待处理任务'
            debug.debug '没有待处理任务'
        
        # 5. 等待下一次轮询
+       console.log "[taskScheduler] 等待 #{BATCH_INTERVAL_MINUTES} 分钟后进行下一次轮询..."
        await helpersFunction.sleep(BATCH_INTERVAL_MINUTES * 60 * 1000)
        
      catch error
        monitorMetrics.totalErrors++
        monitorMetrics.consecutiveErrors++
+       console.log "[taskScheduler] 任务调度器错误 (连续第#{monitorMetrics.consecutiveErrors}次): #{error.message}"
        debug.error "任务调度器错误 (连续第#{monitorMetrics.consecutiveErrors}次):", error
        
        # 指数退避，最大60秒
        retryDelay = Math.min(10000 * monitorMetrics.consecutiveErrors, 60000)
+       console.log "[taskScheduler] 将在 #{retryDelay}ms 后重试"
        debug.info "将在 #{retryDelay}ms 后重试"
        await helpersFunction.sleep(retryDelay)
        continue
    
+   console.log '[taskScheduler] 任务调度器退出'
    debug.info '任务调度器退出'
  
```

```diff
  ###
  updateProcessStatusForTasks = (startTs) ->
+   console.log '[updateProcessStatusForTasks] 开始更新进程状态'
    debug.info '更新进程状态'
    try
```

```diff
        status: RUNNING_NORMAL
        nextTs: Date.now() + UPDATE_PROCESS_STATE_INTERNAL
+     console.log '[updateProcessStatusForTasks] 进程状态更新成功'
    catch error
+     console.log "[updateProcessStatusForTasks] 进程状态更新失败: #{error.message}"
      debug.error 'updateProcessStatusForTasks', error
  
```

```diff
  ###
  main = ->
+   console.log '[main] 主函数开始执行'
    isForce = AVGS.indexOf('force') >= 0
+   console.log "[main] 强制执行模式: #{isForce}"
+   
    try
      # 检查运行进程
+     console.log '[main] 检查运行进程...'
      hasRunningProcess = await checkRunningProcess()
+     console.log "[main] 检查运行进程结果: #{hasRunningProcess}"
+     
      if (not isForce) and hasRunningProcess
+       console.log '[main] 检测到已有运行进程且非强制模式，退出'
        return gracefulExit HAS_RUNNING_PROCESS
      
      startTs = new Date()
+     console.log "[main] 启动时间: #{startTs}"
      await updateProcessStatusForTasks(startTs)
      
+     console.log '[main] 任务监控批处理器启动成功'
+     console.log "[main] 配置参数 - 轮询间隔: #{BATCH_INTERVAL_MINUTES}分钟, 最大并发: #{MAX_CONCURRENT_PUSHES}, 单任务最大用户: #{MAX_PN_PER_TASK}"
      debug.info '任务监控批处理器启动成功'
      debug.info "配置参数 - 轮询间隔: #{BATCH_INTERVAL_MINUTES}分钟, 最大并发: #{MAX_CONCURRENT_PUSHES}, 单任务最大用户: #{MAX_PN_PER_TASK}"
      
      # 启动任务调度器
+     console.log '[main] 启动任务调度器...'
      await taskScheduler()
      
    catch error
+     console.log "[main] 主函数执行失败: #{error.message}"
      debug.error '主函数执行失败:', error
      return gracefulExit error
  
+ console.log '[watchTasks] 准备启动主函数'
  # 启动主函数
  main()
```

---

我已经成功为 `watchTasks.coffee` 文件添加了详细的 console.log 信息，用于检查执行状态。这些日志信息包含了以下关键监控点：

## 主要添加的 console.log 监控点：

### 1. **初始化阶段监控**
- 依赖库和模型加载状态
- 推送通知实例创建
- 配置参数设置
- 监控指标初始化
- 进程处理器初始化
- 并发限制器创建

### 2. **主函数执行监控**
- 主函数开始执行
- 强制执行模式检查
- 运行进程检查
- 启动时间记录
- 任务调度器启动

### 3. **任务调度器监控**
- 调度器启动和退出
- 进程状态更新
- 连续错误监控
- 任务查找状态
- 轮询间隔等待

### 4. **推送工作器监控**
- 任务处理开始和完成
- 处理步骤详细跟踪
- 成功/失败记录处理
- iOS badge更新
- 任务标记完成状态
- 处理耗时统计

### 5. **任务处理监控**
- 目标用户查询
- 设备分组统计
- iOS/Android推送过程
- 推送结果统计
- 错误处理跟踪

### 6. **错误和异常监控**
- 未处理异常捕获
- 连续错误计数
- 重试机制监控
- 优雅退出流程

## 日志格式特点：
- 使用中文描述，便于理解
- 采用 `[模块名] 描述` 的格式便于识别
- 包含关键数据（如任务ID、用户数量、耗时等）
- 区分不同严重级别（信息、警告、错误）

这些 console.log 信息将帮助你：
1. **实时监控**程序执行状态
2. **快速定位**问题所在
3. **统计分析**处理性能
4. **调试验证**业务逻辑
5. **监控系统**健康状态

现在你可以通过查看控制台输出来全面了解推送通知批处理器的运行状态了。

---

