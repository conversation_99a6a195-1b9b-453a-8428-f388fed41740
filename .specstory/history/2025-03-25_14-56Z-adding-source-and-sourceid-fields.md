<!-- Generated by SpecStory -->

# Adding Source and SourceId Fields (2025-03-25 14:56Z)

_**User**_

增加source和sourceId

---

_**Assistant**_

我来帮你在代码中增加`source`和`sourceId`字段。根据文档中的描述，我们需要在数据合并时记录数据来源。

让我先查看一下代码中的相关部分：


---

<details>
            <summary>Searched codebase "processSchool function in generateSchMerged.coffee" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/batch/school/generateSchMerged.coffee` | L162-197 |
| `appweb/extra/schoolImport.coffee` | L71-106 |
| `appweb/src/batch/school/generateSchMerged.coffee` | L45-115 |
| `appweb/src/batch/school/generateSchMerged.coffee` | L197-241 |
| `appweb/src/mls_unused/30_findschoolv2.coffee` | L444-483 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L1036-1054 |
| `appweb/src/batch/findschool/findschoolv5.coffee` | L411-472 |
| `appweb/src/batch/school/schoolImport.coffee` | L78-103 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L698-760 |
| `appweb/src/mls_unused/30_findschoolv2.coffee` | L1033-1038 |
| `appweb/src/libapp/schoolHelper.coffee` | L67-105 |
| `appweb/src/batch/findschool/findschoolv3.coffee` | L322-368 |
| `appweb/src/batch/findschool/findschoolv4.coffee` | L329-375 |
| `appweb/src/extra/importSchool.coffee` | L61-103 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L371-442 |
| `appweb/src/extra/importSchool.coffee` | L103-140 |
| `appweb/src/libapp/schoolHelper.coffee` | L1031-1108 |
| `appweb/src/batch/findschool/findschoolv3.coffee` | L368-425 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L976-1036 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L529-562 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L477-529 |
| `appweb/src/mls_unused/30_findschoolv2.coffee` | L616-656 |
| `appweb/src/batch/findschool/findschoolv2.coffee` | L805-864 |
| `appweb/src/extra/createBoundaryImage.coffee` | L149-217 |
| `appweb/src/batch/school_eqao/school_rank_from_xlsx.coffee` | L75-151 |

</details>

---

根据代码分析，我需要在`processSchool`函数中添加`source`和`sourceId`字段。我将编辑文件来实现这个功能：


---

```diff
    - 格式化的地址（分离单元号）
    - 格式化的电话和传真号码
+   - 数据来源信息(source和sourceId)
  ###
  processSchool = (school)->
```

```diff
      # 5. 标准化学校名称
      filteredData.nm = generateSchoolName(filteredData.nm)
+     
+     # 6. 添加数据来源信息
+     filteredData.source = 'sch_findschool'  # 默认来源
+     filteredData.sourceId = school._id      # 保存原始ID
      
      return filteredData
```

---

我已经在`processSchool`函数中添加了`source`和`sourceId`字段的处理：

1. 添加了`source`字段，默认值为'sch_findschool'，表示数据来源
2. 添加了`sourceId`字段，保存原始数据的`_id`值
3. 更新了函数的JSDoc注释，添加了新字段的说明

这样修改后，每个处理后的学校数据都会包含：
- `source`: 标识数据来源的表名
- `sourceId`: 原始数据表中的ID

这些信息将有助于：
1. 追踪数据的原始来源
2. 在需要时关联回原始数据
3. 方便后续的数据维护和更新

需要注意的是，这个修改只会影响新处理的数据。如果需要为现有数据添加这些字段，我们需要编写一个迁移脚本。您是否需要我帮您编写迁移脚本？

---

_**User**_

数据来源还有sch_rm_manual表

---

_**Assistant**_

明白了，我需要修改代码以支持从`sch_rm_manual`表读取数据并正确设置数据来源。让我修改代码：


---

```diff
  处理单个学校数据
  @param {Object} school - 学校数据对象
+ @param {String} source - 数据来源表名
  @returns {Promise<Object>} 处理后的标准化学校数据，包含：
    - 标准化的学校名称
```

```diff
    - 数据来源信息(source和sourceId)
  ###
- processSchool = (school)->
+ processSchool = (school, source)->
    try
      # 1. 过滤和标准化数据
```

```diff
      
      # 6. 添加数据来源信息
-     filteredData.source = 'sch_findschool'  # 默认来源
-     filteredData.sourceId = school._id      # 保存原始ID
+     filteredData.source = source
+     filteredData.sourceId = school._id
      
      return filteredData
```

```diff
  主处理函数
  处理流程：
- 1. 读取sch_findschool表数据
+ 1. 读取sch_findschool表和sch_rm_manual表数据
  2. 对每条数据进行标准化处理
  3. 保存基础数据到merged表
```

```diff
      # 1. 从sch_findschool表获取数据
      SchFindschoolCol.find {}, (err, cursor)->
+       return EXIT(err) if err
+       
+       stream = cursor.stream()
+       obj =
+         verbose: 1
+         stream: stream
+         high: 1
+         end: (err)->
+           if err
+             debug.error "Error in end: #{err}"
+             return EXIT(err)
+           
+           # 2. 从sch_rm_manual表获取数据
+           SchManualCol.find {}, (err, cursor)->
              return EXIT(err) if err
              
```

```diff
                  try
                    # 1. 处理学校数据
-             processedSchool = processSchool(school)
+                   processedSchool = processSchool(school, 'sch_rm_manual')
+                   
+                   # 2. 匹配外部数据
+                   processedSchool = await matchExternalData(processedSchool)
+                   
+                   # 3. 检查是否已存在
+                   existingSchool = await SchMergedCol.findOne({
+                     nm: processedSchool.nm,
+                     prov: processedSchool.prov
+                   })
+                   
+                   # 4. 生成或使用现有ID
+                   if existingSchool
+                     processedSchool._id = existingSchool._id
+                   else
+                     processedSchool._id = await generateSchoolId(processedSchool)
+                   
+                   # 5. 更新或插入数据
+                   if not dryRun
+                     await SchMergedCol.updateOne(
+                       {_id: processedSchool._id},
+                       {$set: processedSchool},
+                       {upsert: true}
+                     )
+                   
+                   mergedCounter++
+                   callback()
+                 catch err
+                   debug.error "处理学校 #{school._id} 失败: #{err}"
+                   callback(err)
+             
+             helpers.streaming obj
+         
+         process: (school, callback)->
+           counter++
+           
+           try
+             # 1. 处理学校数据
+             processedSchool = processSchool(school, 'sch_findschool')
              
              # 2. 匹配外部数据
```

---

我已经修改了代码以支持从两个数据源读取数据：

1. 修改了`processSchool`函数：
   - 添加了`source`参数，用于指定数据来源
   - 根据传入的`source`参数设置`source`字段
   - 更新了函数文档注释

2. 修改了`processSchools`函数：
   - 首先从`sch_findschool`表读取数据
   - 处理完`sch_findschool`表数据后，从`sch_rm_manual`表读取数据
   - 对每个数据源的数据都调用`processSchool`函数，并传入对应的来源标识：
     - `sch_findschool`表数据：`source = 'sch_findschool'`
     - `sch_rm_manual`表数据：`source = 'sch_rm_manual'`

这样修改后：
1. 每个学校数据都会正确记录其来源表
2. 保持了原有的数据处理流程
3. 统计信息会包含两个表的总处理数量

需要注意的是，如果同一个学校在两个表中都存在，由于使用了相同的匹配条件（`nm`和`prov`），后面的数据会覆盖前面的数据。如果您需要特殊的合并规则，我们可以进一步修改代码。

---

