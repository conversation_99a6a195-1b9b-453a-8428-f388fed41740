<!-- Generated by SpecStory -->

# Prevent doSearch call in mounted (2025-06-17 17:25Z)

_**User**_

检查文件运行逻辑，我在mounted中添加了vars.appMapMode == 'claim'，不希望之后再调用 dosearch

---

_**Assistant**_

你的需求是：在 `mounted` 钩子中，如果 `vars.appMapMode == 'claim'`，只执行 `getClaimedProps()`，**后续不要再自动调用 `doSearch`**。  
目前你的 `mounted` 逻辑如下：

- 如果 `vars.appMapMode == 'claim'`，会执行 `getClaimedProps()`。
- 但**后面还有很多代码**，比如：
  - `if (!this.scrollElement) { ... }`
  - `if (vars.saletp == 'lease') { ... }`
  - 还有一大段关于 `vars.mapmode`、`vars.city`、`filterFromUrl`、`parseSerializedFilter` 等的处理。
  - 这些代码**可能会触发 `doSearch`**，比如 `if (vars.mode == 'list')`、`if (vars.mode == 'map')` 等。

### 你的问题

**你不希望在 `vars.appMapMode == 'claim'` 时，后续再自动调用 `doSearch`。**

---

## 解决建议

你需要在 `mounted` 里**提前 return**，让后续逻辑不再执行。  
比如：

```js
if (vars.appMapMode == 'claim') {
  this.viewMode = 'map';
  this.getClaimedProps();
  return; // 这里提前 return，后续代码不会再执行
}
```

这样，只有 `getClaimedProps()` 会被调用，后续不会再自动触发 `doSearch`。

---

## 代码位置建议

在 `mounted()` 里，找到如下代码段：

```js
if (vars.appMapMode == 'claim') {
  this.viewMode = 'map';
  this.getClaimedProps();
}
```

**修改为：**

```js
if (vars.appMapMode == 'claim') {
  this.viewMode = 'map';
  this.getClaimedProps();
  return; // 防止后续再自动 doSearch
}
```

---

## 结论

只需在 `vars.appMapMode == 'claim'` 逻辑后加 `return`，即可保证 mounted 后续不会再自动调用 `doSearch`。  
如需我帮你直接修改代码，请告知！

---

_**User**_

这个时候地图加载出来了吗

---

_**Assistant**_

你的问题是：  
**在 `mounted` 里，如果 `vars.appMapMode == 'claim'`，直接 return，地图（mapbox/mapObj）会加载出来吗？**

---

### 代码分析

在 `mounted` 钩子里，地图相关的初始化逻辑主要有：

```js
if (vars.src !== 'nativeMap') {
  this.mapObj = this.getMapObj();
  if (vars.mode!=='list') {
    this.loadMapBoxScript();
    this.initGmap();
  }
  if (vars.mode == 'list') {
    setTimeout(()=>{
      self.doSearch({noClear:true});
    },100)
  }
}
```

- 这段代码是在 `mounted` 的**后半部分**。
- 如果你在 `vars.appMapMode == 'claim'` 时直接 `return`，**这段代码不会被执行**。

---

### 结论

**不会加载地图。**
- 因为你提前 return，`this.mapObj = this.getMapObj()`、`this.loadMapBoxScript()`、`this.initGmap()` 都不会执行。
- 这样页面不会初始化地图对象，也不会显示地图。

---

### 解决建议

如果你希望在 `claim` 模式下**依然加载地图**，但不自动 doSearch，可以这样调整：

1. **先初始化地图相关内容**
2. **再判断是否 claim 并 return**

例如：

```js
if (vars.src !== 'nativeMap') {
  this.mapObj = this.getMapObj();
  if (vars.mode!=='list') {
    this.loadMapBoxScript();
    this.initGmap();
  }
  if (vars.mode == 'list') {
    setTimeout(()=>{
      self.doSearch({noClear:true});
    },100)
  }
}

if (vars.appMapMode == 'claim') {
  this.viewMode = 'map';
  this.getClaimedProps();
  return;
}
```

这样，地图会被加载，但不会自动 doSearch。

---

#### 总结

- **提前 return 会导致地图不加载。**
- 如果你要地图加载但不 doSearch，return 要放在地图初始化之后。

如需我帮你调整代码，请告知！

---

_**User**_

调整代码

---

_**Assistant**_

<details>
            <summary>Searched codebase "vars.appMapMode == 'claim'" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L261-317 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L1812-1840 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L317-362 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L2009-2036 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L2982-3032 |
| `appweb/src/coffee4client/components/appEvaluationPage.vue` | L123-186 |
| `appweb/src/coffee4client/components/frac/PropDetail.vue` | L4037-4088 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L1931-1960 |
| `appweb/src/coffee4client/components/appPropDetailPage.vue` | L401-460 |
| `appweb/src/coffee4client/components/evaluate_mixins.js` | L145-159 |
| `appweb/src/coffee4client/components/frac/PropDetail.vue` | L2295-2366 |
| `appweb/src/coffee4client/components/directionSearch-mapbox.vue` | L165-221 |
| `appweb/src/coffee4client/components/appPropPredictionPage.vue` | L69-153 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L2859-2921 |
| `appweb/src/coffee4client/components/mixin/mapBasic.js` | L42-96 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L1098-1147 |
| `appweb/src/coffee4client/components/showing/showingDetail.vue` | L286-323 |
| `appweb/src/coffee4client/components/appMapMixView.vue` | L118-162 |
| `appweb/src/coffee4client/components/frac/PropDetail.vue` | L1358-1406 |
| `appweb/src/coffee4client/components/appPropPredictionPage.vue` | L153-222 |
| `appweb/src/coffee4client/components/directionSearch.vue` | L161-218 |
| `appweb/src/coffee4client/components/appPropDetailPage.vue` | L86-172 |
| `appweb/src/coffee4client/components/project/appProjManage.vue` | L499-579 |
| `appweb/src/coffee4client/components/webMap.vue` | L470-551 |
| `appweb/src/coffee4client/components/mapSearchLogic_mixins.js` | L175-224 |

</details>

---

```diff
        this.appMapMode = vars.appMapMode;
      }
+     // 地图初始化逻辑提前
+     if (vars.src !== 'nativeMap') {
+       this.mapObj = this.getMapObj();
+       if (vars.mode!=='list') {
+         this.loadMapBoxScript();
+         this.initGmap();
+       }
+       if (vars.mode == 'list') {
+         setTimeout(()=>{
+           self.doSearch({noClear:true});
+         },100)
+       }
+     }
+     // 处理 appMapMode=claim 的情况，提前 return，防止后续 doSearch
+     if (vars.appMapMode == 'claim') {
+       this.viewMode = 'map';
+       this.getClaimedProps();
+       return;
+     }
      if (vars.grp != null) {
        this.grp = parseInt(vars.grp) || 0;
```

```diff
        this.getFavs();
      }
-     // 处理 appMapMode=claim 的情况
-     if (vars.appMapMode == 'claim') {
-       this.viewMode = 'map';
-       this.getClaimedProps();
-     }
      if (!this.scrollElement) {
        this.waiting = false
```

---

已完成代码调整：  
现在无论什么模式，都会**先初始化地图**（mapObj、loadMapBoxScript、initGmap），  
然后再判断 `vars.appMapMode == 'claim'`，如果是，则只执行 `getClaimedProps()` 并 return，  
**后续不会再自动 doSearch**，地图也会正常显示。

如需进一步测试或有其他需求，请继续告知！

---

