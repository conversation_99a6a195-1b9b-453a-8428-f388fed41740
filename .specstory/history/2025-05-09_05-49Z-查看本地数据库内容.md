<!-- Generated by SpecStory -->

# 查看本地数据库内容 (2025-05-09 05:49Z)

_**User**_

查看一下我的本地数据库

---

_**Assistant**_

让我帮您查看一下本地数据库的内容。

首先，我需要了解当前工作目录的结构。


---

<details>
            <summary>Listed current directory • **8** results</summary>
        
| Name |
|-------|
| 📁 `appweb` |
| 📁 `.specstory` |
| 📄 `.cursorindexingignore` |
| 📁 `rmconfig` |
| 📄 `.DS_Store` |
| 📁 `.cursor` |
| 📄 `MlsStatusCSV.csv` |
| 📄 `check.js` |

</details>

---

我看到有几个可能包含数据库相关信息的目录和文件。让我们进一步查看一下 `appweb` 目录，因为它看起来是主要的应用目录。


---

<details>
            <summary>Listed directory appweb • **10** results</summary>
        
| Name |
|-------|
| 📁 `.git` |
| 📁 `src` |
| 📁 `extra` |
| 📁 `docs` |
| 📄 `.cursorrules` |
| 📄 `.DS_Store` |
| 📁 `n5` |
| 📄 `package-lock.json` |
| 📁 `fileStorage` |
| 📁 `batch` |

</details>

---

让我们搜索一下与数据库相关的文件：


---

<details>
            <summary>Searched codebase "database configuration or connection settings" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `rmconfig/config/_sqldbs.ini` | L1-19 |
| `appweb/src/lib/mysql.coffee` | L1-88 |
| `appweb/docs/Dev_dbRelated/postgresql_setup.txt` | L73-103 |
| `appweb/src/lib/postgreSql.coffee` | L1-76 |
| `appweb/src/lib/sqlDBs.coffee` | L1-53 |
| `appweb/docs/Dev_dbRelated/postgresql15_setup.txt` | L68-110 |
| `appweb/src/lib/appBase.coffee` | L540-601 |
| `rmconfig/config/_sqldbs_test.ini` | L1-20 |
| `appweb/src/lib/coffeemate3.coffee` | L587-650 |
| `appweb/src/lib/mongo_db.coffee` | L309-368 |
| `appweb/src/lib/arangodb.coffee` | L1-52 |
| `rmconfig/config/_db_tmp.ini` | L1-5 |
| `rmconfig/config/_db_vow.ini` | L1-5 |
| `appweb/src/lib/coffeemate3.coffee` | L508-544 |
| `appweb/src/import/01_base.coffee` | L155-232 |
| `appweb/src/lib/mongo_db.coffee` | L142-164 |
| `appweb/src/apps/80_sites/01_lib/10_config.coffee` | L175-232 |
| `appweb/extra/MgoVsMql/perform.coffee` | L1-25 |
| `appweb/docs/Dev_dbRelated/postgresql_setup.txt` | L1-73 |
| `rmconfig/tpls/local/local.test.ini` | L1-95 |
| `appweb/src/lib/mongoDB.coffee` | L57-113 |
| `appweb/src/lib/mongoDB.coffee` | L1-57 |
| `appweb/src/apps/80_sites/01_lib/10_config.coffee` | L1-96 |
| `rmconfig/config/_db_rni.ini` | L1-10 |
| `rmconfig/tpls/d1/local.test.ini` | L1-88 |

</details>

---

根据搜索结果，我可以看到您的系统使用了多个数据库：

1. PostgreSQL 数据库：
- 数据库名：`listing`
- 主机：localhost
- 端口：5432
- 用户：listing
- 密码：listing

2. MongoDB 数据库：
- 主要数据库：
  - `vow` 数据库
  - `chome` 数据库
  - `rni` 数据库
  - `tmp` 数据库

3. ArangoDB 数据库：
- 系统也支持 ArangoDB，但配置信息较少

4. MySQL 数据库：
- 系统也支持 MySQL，但配置信息较少

主要的数据库配置信息存储在以下位置：
1. `rmconfig/config/_sqldbs.ini` - PostgreSQL 配置
2. `rmconfig/config/_db_*.ini` - MongoDB 配置
3. `appweb/src/lib/` 目录下的各种数据库连接文件

您想了解哪个数据库的具体信息？我可以帮您：
1. 查看具体的数据库连接配置
2. 检查数据库表结构
3. 查看数据库使用情况
4. 或者其他您感兴趣的方面

请告诉我您想了解哪方面的信息。

---

