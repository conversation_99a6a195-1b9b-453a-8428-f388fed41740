<!-- Generated by SpecStory -->

# Implement post endpoint with permission checks (2025-06-17 16:55Z)

_**User**_

参考 @propClaim.coffee 168-191行的权限判断，创建 post ‘map'， @claim.coffee 中添加function 参考 getlist 生成query条件，获取对应数据的pid字段，使用await formatProp {ids,req} 找到proplist并返回 ，返回的数据格式为 {ok:1,item:[proplist]}

---

_**Assistant**_

```diff
- 
  config = CONFIG(['serverBase','share'])
  debug = DEBUG()
```

```diff
      catch err
        debug.error err
-       return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),category:MSG_STRINGS.DB_ERROR,resp}
+       return respError {clientMsg:req.l10n(MSG_STRINGS.DB_ERROR), category:MSG_STRINGS.DB_ERROR,resp}
      if (not req.isAllowed 'claimAdmin') and (not isRealGroup)
        debug.error {msg:"editClaim:#{MSG_STRINGS.NO_AUTHORIZED}",uid:uid,eml:user.eml}
```

```diff
      return resp.send {ok:1,list:returnList}
  
+ ###
+ # 根据参数获取claim相关房源的proplist
+ # @body: 查询参数，参考getList
+ # @return: {ok:1,item:[proplist]}
+ ###
+ POST 'map', (req, resp) ->
+   UserModel.appAuth {req, resp}, (user) ->
+     return respError {category: MSG_STRINGS.NEED_LOGIN, errorCode: 0, resp} unless user
+     uid = user._id
+     try
+       isRealGroup = await GroupModel.isInGroup {uid, groupName: ':inReal'}
+     catch err
+       debug.error err
+       return respError {clientMsg: req.l10n(MSG_STRINGS.DB_ERROR), category: MSG_STRINGS.DB_ERROR, resp}
+     isNoteAdmin = req.isAllowed 'claimAdmin'
+     body = req.body
+     if (not isNoteAdmin) and ((not isRealGroup) or body?.agent)
+       debug.error {msg: "get Claim map:#{MSG_STRINGS.NO_AUTHORIZED}", uid, eml: user.eml}
+       return respError {clientMsg: req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
+     try
+       ids = await ClaimModel.getMapPids {uid, body, isNoteAdmin}
+       proplist = await formatProp {ids, req}
+     catch err
+       debug.error err
+       return respError {clientMsg: req.l10n(MSG_STRINGS.DB_ERROR), sysErr: err, resp}
+     return resp.send {ok: 1, item: Object.values(proplist)}
  
  formatProp = ({ids,req})->
```

---

```diff
      ])
  
+ ###
+   # 获取claim相关的所有pid，返回pid数组
+   # @param {object} {uid,body,isNoteAdmin} - 查询参数，参考getList
+   # @return [pid]
+ ###
+   @getMapPids:({uid,body,isNoteAdmin})->
+     body.sort ?= 'tsNum'
+     query = buildSearchQuery body
+     if isNoteAdmin
+       delete query.uid
+       if body.agent
+         query.uid = new ObjectId(body.agent)
+     else
+       query.uid = new ObjectId(uid)
+     // 只查pid字段
+     docs = await ClaimCol.find(query, {projection: {pid: 1}}).toArray()
+     pids = docs.map((d) -> d.pid)
+     # 去重
+     Array.from(new Set(pids))
+ 
  ###
    # 获取用户claim百分比信息
```

---

```diff
      else
        query.uid = new ObjectId(uid)
-     // 只查pid字段
+     # 只查pid字段
      docs = await ClaimCol.find(query, {projection: {pid: 1}}).toArray()
      pids = docs.map((d) -> d.pid)
```

---

已完成以下功能开发：

1. 在 `propClaim.coffee` 中新增了 POST 'map' 接口，权限校验与 list 保持一致，调用 `ClaimModel.getMapPids` 获取 pid 列表，并用 `formatProp` 返回房源详情，格式为 `{ok:1, item:[proplist]}`。
2. 在 `claim.coffee` 中新增了静态方法 `getMapPids`，复用 getList 的 query 逻辑，仅返回所有匹配数据的唯一 pid 数组。
3. 修复了注释风格导致的 linter 错误。

如需接口注释或进一步测试/联调，请告知！

---

