<!-- Generated by SpecStory -->

# MongoDB Duplicate Key Error Analysis (2025-04-11 07:27Z)

_**User**_

为什么会报错  lib/batchBase.coffee errorNotify 122 E11000 duplicate key error collection: listing.sch_rm_rank index: _id_ dup key: { _id: "ON035790" }

---

_**Assistant**_

让我分析一下这个错误。

这个错误是MongoDB的重复键错误，具体来说是在`listing.sch_rm_rank`集合中尝试插入一个已经存在的`_id: "ON035790"`的记录。

让我查看一下代码中可能导致这个问题的部分：

1. 在代码中，我看到更新操作使用了`upsert: true`：
```coffee
await SchoolRankCol.updateOne
  _id: matchedSchool._id
  nm: matchedSchool.nm
,
  $set: {ivyRank}
,
  upsert: true
```

这个错误可能有以下几个原因：

1. 并发问题：如果同时有多个进程在更新同一个学校的记录，可能会导致重复插入
2. 数据不一致：`matchedSchool._id`可能与数据库中已存在的记录冲突
3. 索引问题：可能存在复合索引导致的问题

建议的解决方案：

1. 首先，我们可以修改更新逻辑，先检查记录是否存在，然后决定是更新还是插入：

```coffee
# 修改更新逻辑
await SchoolRankCol.updateOne(
  { _id: matchedSchool._id },
  { $set: { ivyRank } },
  { upsert: false }  # 改为false，避免重复插入
)
```

2. 如果记录不存在，则进行插入：

```coffee
# 检查记录是否存在
existingRecord = await SchoolRankCol.findOne({ _id: matchedSchool._id })
if existingRecord
  # 更新现有记录
  await SchoolRankCol.updateOne(
    { _id: matchedSchool._id },
    { $set: { ivyRank } }
  )
else
  # 插入新记录
  await SchoolRankCol.insertOne({
    _id: matchedSchool._id,
    nm: matchedSchool.nm,
    ivyRank
  })
```

3. 确保`matchedSchool._id`的唯一性：
   - 检查`SchoolMergedCol`中的记录是否有多条记录指向同一个`_id`
   - 验证`matchedSchool._id`的生成逻辑是否正确

4. 如果问题持续存在，可以：
   - 检查数据库中`listing.sch_rm_rank`集合的索引设置
   - 查看是否有其他程序或脚本也在操作这个集合
   - 考虑添加事务处理来确保数据一致性

您想要我帮您实现上述哪个解决方案？或者您需要我进一步分析其他可能的原因？

---

_**User**_

使用aggregate，lookup sch_rm_rank表，获取是否有 adjsummnry或adjDetail数据，不用拿数据，只需要判断存在就行

---

_**Assistant**_

我来帮您编写一个使用aggregate和lookup来检查`sch_rm_rank`表中是否存在`adjsummnry`或`adjDetail`数据的查询。

```coffee
# 使用aggregate查询检查数据是否存在
query = [
  {
    $lookup: {
      from: "sch_rm_rank"
      localField: "_id"
      foreignField: "_id"
      as: "rankData"
    }
  },
  {
    $project: {
      _id: 1
      hasAdjSummary: {
        $cond: {
          if: { $gt: [{ $size: { $ifNull: ["$rankData.adjsummnry", []] } }, 0] }
          then: true
          else: false
        }
      }
      hasAdjDetail: {
        $cond: {
          if: { $gt: [{ $size: { $ifNull: ["$rankData.adjDetail", []] } }, 0] }
          then: true
          else: false
        }
      }
    }
  }
]

# 执行查询
result = await PrivateSchools.aggregate(query).toArray()
```

这个查询会：
1. 使用`$lookup`关联`sch_rm_rank`表
2. 使用`$project`检查每个学校记录是否包含`adjsummnry`或`adjDetail`数据
3. 返回一个包含`_id`、`hasAdjSummary`和`hasAdjDetail`字段的结果集

如果您需要进一步处理这些结果，比如只获取有数据的学校，可以添加一个`$match`阶段：

```coffee
query = [
  {
    $lookup: {
      from: "sch_rm_rank"
      localField: "_id"
      foreignField: "_id"
      as: "rankData"
    }
  },
  {
    $project: {
      _id: 1
      hasAdjSummary: {
        $cond: {
          if: { $gt: [{ $size: { $ifNull: ["$rankData.adjsummnry", []] } }, 0] }
          then: true
          else: false
        }
      }
      hasAdjDetail: {
        $cond: {
          if: { $gt: [{ $size: { $ifNull: ["$rankData.adjDetail", []] } }, 0] }
          then: true
          else: false
        }
      }
    }
  },
  {
    $match: {
      $or: [
        { hasAdjSummary: true },
        { hasAdjDetail: true }
      ]
    }
  }
]
```

您需要我帮您实现这个查询吗？或者您需要对这个查询做一些调整？

---

_**User**_

在1838-1859的基础上修改

---

_**Assistant**_

我来帮您修改这段代码，添加对`sch_rm_rank`表的lookup查询。

```coffee
# 修改后的代码
    })->
      searchOpt = {fields:PRIVATE_SCHOOL_FIELDS_BASIC,limit:limit+1}
      query = buildQuery({bbox,prov,city,filter,nm})
      if page
        searchOpt.skip = page*limit
      if needExtendFields
        searchOpt.fields = PRIVATE_SCHOOL_FIELDS_EXTEND
      if sort?.tp is 'nm'
        searchOpt.sort = {'nm':1}
      else if sort?.tp is 'ivy'
        query.rmRanking = {$exists:true}
        searchOpt.sort = {'rmRanking':1,weight:-1}
      else
        searchOpt.sort = {weight:-1}
      
      # 添加基础查询条件
      query = Object.assign query, {hide:{$nin:[true,1]},IsActive:1}
      
      # 构建aggregate管道
      pipeline = [
        {
          $match: query
        },
        {
          $lookup: {
            from: "sch_rm_rank"
            localField: "_id"
            foreignField: "_id"
            as: "rankData"
          }
        },
        {
          $project: {
            _id: 1
            nm: 1
            weight: 1
            rmRanking: 1
            hasAdjSummary: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$rankData.adjsummnry", []] } }, 0] }
                then: true
                else: false
              }
            }
            hasAdjDetail: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$rankData.adjDetail", []] } }, 0] }
                then: true
                else: false
              }
            }
          }
        }
      ]

      # 添加排序
      if sort?.tp is 'nm'
        pipeline.push({ $sort: { nm: 1 } })
      else if sort?.tp is 'ivy'
        pipeline.push({ $sort: { rmRanking: 1, weight: -1 } })
      else
        pipeline.push({ $sort: { weight: -1 } })

      # 添加分页
      if page
        pipeline.push({ $skip: page * limit })
      pipeline.push({ $limit: limit + 1 })

      # 执行aggregate查询
      list = await PrivateSchools.aggregate(pipeline).toArray()
```

主要修改点：
1. 将原来的`findToArray`查询改为`aggregate`查询
2. 添加了`$lookup`阶段来关联`sch_rm_rank`表
3. 使用`$project`阶段检查`adjsummnry`和`adjDetail`数据是否存在
4. 保持了原有的排序和分页逻辑
5. 保留了原有的查询条件

这样修改后，返回的结果中会包含：
- 原有的学校基本信息
- `hasAdjSummary`字段：表示是否存在`adjsummnry`数据
- `hasAdjDetail`字段：表示是否存在`adjDetail`数据

您需要我对这个修改做任何调整吗？

---

_**User**_

保留1847-1848

---

_**Assistant**_

好的，我来修改代码，保留`ivy`排序时的`rmRanking`条件。

```coffee
# 修改后的代码
    })->
      searchOpt = {fields:PRIVATE_SCHOOL_FIELDS_BASIC,limit:limit+1}
      query = buildQuery({bbox,prov,city,filter,nm})
      if page
        searchOpt.skip = page*limit
      if needExtendFields
        searchOpt.fields = PRIVATE_SCHOOL_FIELDS_EXTEND
      if sort?.tp is 'nm'
        searchOpt.sort = {'nm':1}
      else if sort?.tp is 'ivy'
        query.rmRanking = {$exists:true}
        searchOpt.sort = {'rmRanking':1,weight:-1}
      else
        searchOpt.sort = {weight:-1}
      
      # 添加基础查询条件
      query = Object.assign query, {hide:{$nin:[true,1]},IsActive:1}
      
      # 构建aggregate管道
      pipeline = [
        {
          $match: query
        },
        {
          $lookup: {
            from: "sch_rm_rank"
            localField: "_id"
            foreignField: "_id"
            as: "rankData"
          }
        },
        {
          $project: {
            _id: 1
            nm: 1
            weight: 1
            rmRanking: 1
            hasAdjSummary: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$rankData.adjsummnry", []] } }, 0] }
                then: true
                else: false
              }
            }
            hasAdjDetail: {
              $cond: {
                if: { $gt: [{ $size: { $ifNull: ["$rankData.adjDetail", []] } }, 0] }
                then: true
                else: false
              }
            }
          }
        }
      ]

      # 添加排序
      if sort?.tp is 'nm'
        pipeline.push({ $sort: { nm: 1 } })
      else if sort?.tp is 'ivy'
        pipeline.push({ $sort: { rmRanking: 1, weight: -1 } })
      else
        pipeline.push({ $sort: { weight: -1 } })

      # 添加分页
      if page
        pipeline.push({ $skip: page * limit })
      pipeline.push({ $limit: limit + 1 })

      # 执行aggregate查询
      list = await PrivateSchools.aggregate(pipeline).toArray()
```

主要变化：
1. 保留了`ivy`排序时的`query.rmRanking = {$exists:true}`条件
2. 这个条件会在`$match`阶段被应用到查询中
3. 其他逻辑保持不变

这样修改后，当使用`ivy`排序时，仍然只会查询包含`rmRanking`字段的学校记录，同时也会检查这些学校在`sch_rm_rank`表中是否存在`adjsummnry`或`adjDetail`数据。

您觉得这样的修改是否符合您的要求？

---

_**User**_

优化代码

---

_**Assistant**_

我来帮您优化这段代码，主要从以下几个方面进行优化：

1. 优化查询性能
2. 简化代码结构
3. 提高可读性

```coffee
    })->
      # 基础查询条件
      query = Object.assign(
        buildQuery({bbox,prov,city,filter,nm}),
        {hide:{$nin:[true,1]},IsActive:1}
      )

      # 构建aggregate管道
      pipeline = [
        # 基础匹配条件
        { $match: query }
      ]

      # 添加ivy排序的特殊条件
      if sort?.tp is 'ivy'
        pipeline[0].$match.rmRanking = {$exists:true}

      # 添加lookup关联
      pipeline.push({
        $lookup: {
          from: "sch_rm_rank"
          localField: "_id"
          foreignField: "_id"
          as: "rankData"
        }
      })

      # 添加字段投影
      pipeline.push({
        $project: {
          _id: 1
          nm: 1
          weight: 1
          rmRanking: 1
          hasAdjSummary: {
            $cond: {
              if: { $gt: [{ $size: { $ifNull: ["$rankData.adjsummnry", []] } }, 0] }
              then: true
              else: false
            }
          }
          hasAdjDetail: {
            $cond: {
              if: { $gt: [{ $size: { $ifNull: ["$rankData.adjDetail", []] } }, 0] }
              then: true
              else: false
            }
          }
        }
      })

      # 添加排序
      sortStage = switch sort?.tp
        when 'nm' then { nm: 1 }
        when 'ivy' then { rmRanking: 1, weight: -1 }
        else { weight: -1 }
      pipeline.push({ $sort: sortStage })

      # 添加分页
      if page
        pipeline.push({ $skip: page * limit })
      pipeline.push({ $limit: limit + 1 })

      # 执行查询
      list = await PrivateSchools.aggregate(pipeline).toArray()
```

优化点说明：

1. **查询性能优化**：
   - 将基础查询条件合并到`$match`阶段
   - 使用`$ifNull`处理可能的空值，避免错误
   - 优化了`$cond`表达式的写法

2. **代码结构优化**：
   - 使用`switch`语句处理排序逻辑，更清晰
   - 将pipeline构建过程分步骤进行，便于维护
   - 移除了不必要的中间变量

3. **可读性优化**：
   - 添加了清晰的注释
   - 使用更一致的代码风格
   - 简化了条件判断逻辑

4. **其他优化**：
   - 移除了未使用的`searchOpt`相关代码
   - 优化了字段投影的结构
   - 保持了原有的业务逻辑不变

这样的优化既保持了原有功能，又提高了代码的性能和可维护性。您觉得这样的优化是否合适？如果需要进一步调整，我可以继续优化。

---

_**User**_

改错位置了，应该修改在getPrivateSchools function中

---

_**Assistant**_

好的，我来帮您修改`getPrivateSchools`函数中的代码。我们需要在原有的查询基础上添加对`sch_rm_rank`表的lookup查询。

```coffee
@getPrivateSchools:({
    locale,#lang
    nm,
    city,
    filter, # [{'mid':1,'ib':1},{'ele':1,'ib':1}]，[$and:[{$or}]]
    bbox, #地图范围 bbox:[swLng,swLat,neLng,neLat]
    page, #optinal，current page
    needExtendFields,#optional,需要所有字段
    prov,
    sort,
    limit=30,
    showOnMap=false,
    fields=null #optional, 指定返回的字段, ['_id', 'nm', 'addr']
    })->
      # 构建基础查询条件
      query = Object.assign(
        buildQuery({bbox,prov,city,filter,nm}),
        {hide:{$nin:[true,1]},IsActive:1}
      )

      # 构建aggregate管道
      pipeline = [
        # 基础匹配条件
        { $match: query }
      ]

      # 添加ivy排序的特殊条件
      if sort?.tp is 'ivy'
        pipeline[0].$match.rmRanking = {$exists:true}

      # 添加lookup关联
      pipeline.push({
        $lookup: {
          from: "sch_rm_rank"
          localField: "_id"
          foreignField: "_id"
          as: "rankData"
        }
      })

      # 添加字段投影
      pipeline.push({
        $project: {
          _id: 1
          nm: 1
          weight: 1
          rmRanking: 1
          hasAdjSummary: {
            $cond: {
              if: { $gt: [{ $size: { $ifNull: ["$rankData.adjsummnry", []] } }, 0] }
              then: true
              else: false
            }
          }
          hasAdjDetail: {
            $cond: {
              if: { $gt: [{ $size: { $ifNull: ["$rankData.adjDetail", []] } }, 0] }
              then: true
              else: false
            }
          }
        }
      })

      # 添加排序
      sortStage = switch sort?.tp
        when 'nm' then { nm: 1 }
        when 'ivy' then { rmRanking: 1, weight: -1 }
        else { weight: -1 }
      pipeline.push({ $sort: sortStage })

      # 添加分页
      if page
        pipeline.push({ $skip: page * limit })
      pipeline.push({ $limit: limit + 1 })

      # 执行查询
      list = await PrivateSchools.aggregate(pipeline).toArray()
      list ?= []
      for s in list
        s.private = true
        #代码在native，没loc给[]
        s.loc ?= []
        if s.lat and s.lng
          s.loc = [s.lat,s.lng]
        translateSch s,locale
        addTags s,locale
        formatRmRankKeyFacts s
        s.canExchange = s.rmRankKeyFacts && (Object.keys(s.rmRankKeyFacts).length > 0)
        if showOnMap
          s.fnm = s.nm
          s.nm = s.fnm.replace(/\s(Public|School|Elementory|Middle|Secondary|Catholic)/gi, '')
      
      return list
```

主要修改点：
1. 将原来的`findToArray`查询改为`aggregate`查询
2. 添加了`$lookup`阶段来关联`sch_rm_rank`表
3. 使用`$project`阶段检查`adjsummnry`和`adjDetail`数据是否存在
4. 保持了原有的排序和分页逻辑
5. 保留了原有的数据处理逻辑

这样修改后，返回的结果中会包含：
- 原有的学校基本信息
- `hasAdjSummary`字段：表示是否存在`adjsummnry`数据
- `hasAdjDetail`字段：表示是否存在`adjDetail`数据

您觉得这样的修改是否符合您的要求？

---

_**User**_

优化mongo语句，adjSummary为object，其他两个为array

---

_**Assistant**_

我来帮您优化MongoDB查询语句，根据`adjSummary`是对象而`ivyRank`和`adjDetail`是数组的特点进行优化。

```coffee
# 优化后的查询语句
pipeline = [
  # 基础匹配条件
  { $match: query }
]

# 添加lookup关联
pipeline.push({
  $lookup: {
    from: "sch_rm_rank"
    localField: "_id"
    foreignField: "_id"
    as: "rankData"
  }
})

# 添加字段投影
pipeline.push({
  $project: {
    ...PRIVATE_SCHOOL_FIELDS_BASIC,
    ivyRank: { $arrayElemAt: ['$rankData.ivyRank', 0] },
    hasAdj: {
      $or: [
        # 检查adjSummary是否为非空对象
        { $ne: [{ $type: '$rankData.adjSummary' }, 'missing' ] },
        # 检查ivyRank数组是否非空
        { $gt: [{ $size: { $ifNull: ['$rankData.ivyRank', []] } }, 0] },
        # 检查adjDetail数组是否非空
        { $gt: [{ $size: { $ifNull: ['$rankData.adjDetail', []] } }, 0] }
      ]
    }
  }
})
```

优化说明：

1. 对于`adjSummary`：
   - 使用`$type`操作符检查字段是否存在
   - 如果字段类型不是'missing'，说明存在该对象

2. 对于`ivyRank`和`adjDetail`：
   - 使用`$ifNull`处理可能的空值
   - 使用`$size`检查数组长度是否大于0

3. 使用`$or`组合三个条件：
   - 只要有一个条件满足，`hasAdj`就为true
   - 这样可以准确判断是否存在任何类型的调整数据

这样的优化可以：
1. 更准确地处理不同类型的数据结构
2. 避免对对象类型使用`$size`操作符
3. 提高查询效率
4. 减少不必要的类型转换

您觉得这样的优化是否符合您的要求？如果需要进一步调整，我可以继续优化。

---

