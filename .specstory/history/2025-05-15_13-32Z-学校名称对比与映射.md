<!-- Generated by SpecStory -->

# 学校名称对比与映射 (2025-05-15 13:32Z)

_**User**_

将两部分学校名字进行对比，有差异的放在schoolNameMap中，key是现在schoolNameMap里面的内容，value是schoolNameMap外面的内容

---

_**Assistant**_

我来帮您对比两部分学校名字的差异，并生成相应的映射关系。我会仔细分析每个学校名称，找出需要映射的部分。

让我先解释一下我要做什么：我会对比第一部分(schoolNameMap中的内容)和第二部分(外部内容)的学校名称，找出那些名称不完全相同但明显指代同一所学校的情况。

分析后，我发现以下学校名称需要进行映射：

---

```diff
  
  schoolNameMap = {
-   St Michael's Choir S
- St. Augustine Catholic High School
- St Robert Catholic HS
- St. Theresa Of Lisieux Catholic High School
- École Secondaire Toronto Ouest
- Collège Français Secondaire
- Ursula Franklin Academy
- Bayview Secondary School
- Iroquois Ridge High School
- <PERSON> Trudeau High School
- Abbey Park High School
- White Oaks SS
- Markville Secondary School
- Bur Oak Secondary School
- Unionville High School
- A Y Jackson Secondary School
- Cardinal Carter Academy For The Arts
- Bloor Collegiate Institute
- Oakville Trafalgar High School
- York Mills Collegiate Institute
- John Fraser Secondary School
- Richmond Hill High School
- Riverdale Collegiate Institute
- The Woodlands Secondary School
- Ésc Saint-Frère-André
- Earl Haig Secondary School
- Ésc Renaissance
- Leaside High School
- Ésc Sainte-Trinité
- Milliken Mills High School
- Humberside Collegiate Institute
- Malvern Collegiate Institute
- London Central Secondary School
- William Lyon Mackenzie Collegiate Institute
- Bishop Macdonell Catholic Secondary School
- Earl Of March Secondary School
- Nelson High School
- Sir John A Macdonald Secondary School
- Markham District High School
- Lawrence Park Collegiate Institute
- Agincourt Collegiate Institute
- Thornhill Secondary School
- Dr Norman Bethune Collegiate Institute
- Richview Collegiate Institute
- Our Lady Queen Of The World Catholic Academy
- Father Michael Mcgivney Catholic Academy
- Dr. G. W. Williams Secondary School
- North Toronto Collegiate Institute
- Thornlea Secondary School
- École Secondaire Catholique Béatrice-Desloges
- Lo-Ellen Park Secondary School
- Colonel By Secondary School
- École Secondaire Catholique Jeanne-Lajoie, Pavillo
- Garth Webb Secondary School
- École Secondaire Norval-Morrisseau
- École Secondaire Catholique De Casselman
- Northern Secondary School
- Richmond Green Secondary School
- Father John Redmond Catholic Secondary School And Regional Arts Centre
- Bishop Allen Academy
- Corpus Christi Secondary School
- Hodan Nalayeh Secondary School
- Alexander Mackenzie High School
- Merivale High School
- École Secondaire Publique Maurice-Lapointe
- École Secondaire Étienne-Brûlé
- Thomas A. Blakelock High School
- St. Brother Andre Catholic High School
- Etobicoke Collegiate Institute
- Tecumseh Vista Academy - Secondary
- Centennial Collegiate And Vocational Institute
- Bill Crothers Secondary School
- Bishop Paul Francis Reding SS
- Aurora High School
- St. Francis Xavier Secondary School
- Ésc Père-René-De-Galinée
- Stephen Lewis Secondary School
- Cameron Heights Collegiate Institute
- Anderson Collegiate And Vocational Institute
- Saint Paul Catholic High School
- St. Thomas Aquinas Secondary School
- Belle River District High School
- Ésc Saint-Charles-Garnier
- St David Catholic Secondary School
- Cardinal Carter SS
- Monarch Park Collegiate Institute
- Donald A Wilson Secondary School
- École Secondaire Le Caron
- Sacred Heart High School
- Bill Hogarth Secondary School
- Newmarket High School
- Satec @ W A Porter Collegiate Institute
- St. Joseph Secondary School
- Sydenham High School
- École Secondaire Catholique Le Relais
- West Carleton Secondary School
- Nepean High School
- St. John Henry Newman Catholic Secondary School
- Cardinal Carter Catholic
- Pickering High School
- }
- St. Michael's Choir School
- St. Augustine Catholic High School
- St. Robert Catholic High School
- St. Theresa Of Lisieux Catholic High School
- École Secondaire Toronto Ouest
- Collège Français Secondaire
- Ursula Franklin Academy
- Bayview Secondary School
- Iroquois Ridge High School
- Pierre Elliott Trudeau High School
- Abbey Park High School
- White Oaks Secondary School(south Campus)
- Markville Secondary School
- Bur Oak Secondary School
- Unionville High School
- A Y Jackson Secondary School
- Cardinal Carter Academy For The Arts
- Bloor Collegiate Institute
- Oakville Trafalgar High School
- York Mills Collegiate Institute
- John Fraser Secondary School
- Richmond Hill High School
- Riverdale Collegiate Institute
- The Woodlands Secondary School
- Ésc Saint-Frère-André
- Earl Haig Secondary School
- Ésc Renaissance
- Leaside High School
- Ésc Sainte-Trinité
- Milliken Mills High School
- Humberside Collegiate Institute
- Malvern Collegiate Institute
- London Central Secondary School
- William Lyon Mackenzie Collegiate Institute
- Bishop Macdonell Catholic Secondary School
- Earl Of March Secondary School
- Nelson High School
- Sir John A Macdonald Secondary School
- Markham District High School
- Lawrence Park Collegiate Institute
- Agincourt Collegiate Institute
- Thornhill Secondary School
- Dr Norman Bethune Collegiate Institute
- Richview Collegiate Institute
- Our Lady Queen Of The World Catholic Academy
- Father Michael Mcgivney Catholic Academy
- Dr. G. W. Williams Secondary School
- North Toronto Collegiate Institute
- Thornlea Secondary School
- École Secondaire Catholique Béatrice-Desloges
- Lo-Ellen Park Secondary School
- Colonel By Secondary School
- École Secondaire Catholique Jeanne-Lajoie, Pavillo
- Garth Webb
- École Secondaire Norval-Morrisseau
- École Secondaire Catholique De Casselman
- Northern Secondary School
- Richmond Green Secondary School
- Father John Redmond Catholic Secondary School And Regional Arts Centre
- Bishop Allen Academy
- Corpus Christi Secondary School
- Hodan Nalayeh Secondary School
- Alexander Mackenzie High School
- Merivale High School
- École Secondaire Publique Maurice-Lapointe
- École Secondaire Étienne-Brûlé
- Thomas A. Blakelock High School
- St. Brother Andre Catholic High School
- Etobicoke Collegiate Institute
- Tecumseh Vista Academy - Secondary
- Centennial Collegiate And Vocational Institute
- Bill Crothers Secondary School
- Bishop P. F. Reding Secondary School
- Aurora High School
- St. Francis Xavier Secondary School
- Ésc Père-René-De-Galinée
- Stephen Lewis Secondary School
- Cameron Heights Collegiate Institute
- Anderson Collegiate And Vocational Institute
- Saint Paul Catholic High School
- St. Thomas Aquinas Secondary School
- Belle River District High School
- Ésc Saint-Charles-Garnier
- St David Catholic Secondary School
- Cardinal Carter Catholic High School
- Monarch Park Collegiate Institute
- Donald A Wilson Secondary School
- École Secondaire Le Caron
- Sacred Heart High School
- Bill Hogarth Secondary School
- Newmarket High School
- Satec @ W A Porter Collegiate Institute
- St. Joseph Secondary School
- Sydenham High School
- École Secondaire Catholique Le Relais
- West Carleton Secondary School
- Nepean High School
- St. John Henry Newman
- Cardinal Carter Catholic
- Pickering High School
+   'St Michael\'s Choir S': 'St. Michael\'s Choir School',
+   'St Robert Catholic HS': 'St. Robert Catholic High School',
+   'White Oaks SS': 'White Oaks Secondary School(south Campus)',
+   'Garth Webb Secondary School': 'Garth Webb',
+   'Bishop Paul Francis Reding SS': 'Bishop P. F. Reding Secondary School',
+   'Cardinal Carter SS': 'Cardinal Carter Catholic High School',
+   'St. John Henry Newman Catholic Secondary School': 'St. John Henry Newman'
+ }
  
  ###*
```

---

我已经创建了一个包含所有需要映射的学校名称的对象。让我解释一下每个映射：

1. `'St Michael's Choir S'` -> `'St. Michael's Choir School'`
   - 将简写名称映射到完整名称

2. `'St Robert Catholic HS'` -> `'St. Robert Catholic High School'`
   - 将 "HS" 简写映射到完整的 "High School"

3. `'White Oaks SS'` -> `'White Oaks Secondary School(south Campus)'`
   - 将 "SS" 简写映射到完整的校区名称

4. `'Garth Webb Secondary School'` -> `'Garth Webb'`
   - 将完整名称映射到简短版本

5. `'Bishop Paul Francis Reding SS'` -> `'Bishop P. F. Reding Secondary School'`
   - 将带有完整名字的版本映射到使用缩写的版本

6. `'Cardinal Carter SS'` -> `'Cardinal Carter Catholic High School'`
   - 将简写版本映射到完整的官方名称

7. `'St. John Henry Newman Catholic Secondary School'` -> `'St. John Henry Newman'`
   - 将完整正式名称映射到简短版本

这些映射关系将帮助系统在处理学校名称时正确匹配不同版本的学校名称。其他学校名称都是完全匹配的，所以不需要包含在映射中。

---

_**User**_

参考 @update_adj_factors.coffee 的处理，先检查schoolName是否在PrivateSchoolNameMap中，查找private表，并更新

---

_**Assistant**_

我明白了，我需要参考`update_adj_factors.coffee`的处理方式，先检查私立学校，然后再处理公立学校。我来修改代码。

---

```diff
  }
  
+ # 初始化数据库集合
  SchoolMergedCol = COLLECTION 'vow', 'sch_rm_merged'
  SchoolRankCol = COLLECTION 'vow', 'sch_rm_rank'
+ PrivateSchoolCol = COLLECTION 'vow', 'private_schools'  # 添加私立学校集合
  
  {mergeArrayByYearmark,generateSchoolName,delSchoolCommWord} = INCLUDE 'libapp.schoolHelper'
```

```diff
    'Bishop Paul Francis Reding SS': 'Bishop P. F. Reding Secondary School',
    'Cardinal Carter SS': 'Cardinal Carter Catholic High School',
-   'St. John Henry Newman Catholic Secondary School': 'St. John Henry Newman'
+   'St. John Henry Newman Catholic Secondary School': 'St. John Henry Newman',
    'Danforth Collegiate and Technical Institute': 'Danforth Collegiate And Technical Institute',
-   'St Aloysius Gonzaga Secondary School': 'St. Aloysius Gonzaga Secondary School',
+   'St Aloysius Gonzaga Secondary School': 'St. Aloysius Gonzaga Secondary School'
  }
- 
  
  PrivateSchoolNameMap = {
```

```diff
    'Hamilton District Christian High School':'Hamilton District Christian High'
  }
+ 
  ###*
   * 将百分比转换为整数(放大10000倍)
```

```diff
    return name
  
+ ###*
+  * 查找匹配的学校信息
+  * @param {string} schoolName - 学校名称
+  * @returns {Promise<Object>} - 匹配的学校信息或null
+ ###
+ findMatchedSchool = (schoolName) ->
+   # 首先检查是否为私立学校
+   if privateSchoolName = PrivateSchoolNameMap[schoolName]
+     try
+       privateSchool = await PrivateSchoolCol.findOne(
+         { nm: { $regex: privateSchoolName, $options: 'i' } },
+         { projection: { _id: 1, nm: 1 } }
+       )
+       
+       if privateSchool
+         return {
+           _id: privateSchool._id
+           nm: privateSchool.nm
+           isPrivate: true
+         }
+     catch err
+       debug.error "查询私立学校 #{privateSchoolName} 时出错:", err
+ 
+   # 如果不是私立学校或私立学校未找到，则查询公立学校
+   # 检查是否需要映射学校名称
+   if mappedName = schoolNameMap[schoolName]
+     schoolName = mappedName
+ 
+   optimizedName = optimizeSchoolName(schoolName)
+   
+   query = 
+     $or: [
+       { nm: schoolName },
+       { nm: { $regex: schoolName, $options: 'i' } }
+     ]
+   
+   if optimizedName and optimizedName isnt schoolName.toLowerCase()
+     query.$or.push { nm: { $regex: optimizedName, $options: 'i' } }
+ 
+   # 在sch_rm_merged中查找匹配的学校
+   matchedSchool = await SchoolMergedCol.findOne(query, { projection: { _id: 1, nm: 1} })
+   
+   if matchedSchool
+     return {
+       _id: matchedSchool._id
+       nm: matchedSchool.nm
+       isPrivate: false
+     }
+   
+   return null
+ 
  ###*
   * 处理学校数据
```

```diff
      speedMeter.check {processSchool:1}
      schoolName = school['school name']
-     optimizedName = optimizeSchoolName(schoolName)
      
      # 查询匹配学校
-     query = 
-       $or: [
-         { nm: schoolName },
-         { nm: { $regex: schoolName, $options: 'i' } }
-       ]
-     
-     if optimizedName and optimizedName isnt schoolName.toLowerCase()
-       query.$or.push { nm: { $regex: optimizedName, $options: 'i' } }
-     # 在sch_rm_merged中查找匹配的学校
-     matchedSchool = await SchoolMergedCol.findOne(query, { projection: { _id: 1, nm: 1} })
-     
+     matchedSchool = await findMatchedSchool(schoolName)
      
      # 如果找到匹配的学校,则处理和保存数据
```

```diff
          ivyRank = mergeArrayByYearmark(ivyRank, [topSchoolsData])
  
- 
        # 如果是试运行模式,则只打印数据而不更新数据库
        if dryrun
```

---

我已经按照`update_adj_factors.coffee`的处理方式更新了代码，主要做了以下改动：

1. 添加了私立学校集合的初始化：
```coffeescript
PrivateSchoolCol = COLLECTION 'vow', 'private_schools'  # 添加私立学校集合
```

2. 创建了新的`findMatchedSchool`函数，实现了私立学校优先的查找逻辑：
- 首先检查学校是否在`PrivateSchoolNameMap`中
- 如果是私立学校，则在`private_schools`集合中查找
- 如果找到私立学校，返回带有`isPrivate: true`标记的结果
- 如果不是私立学校或未找到，则继续查找公立学校

3. 修改了`processSchoolData`函数，使用新的`findMatchedSchool`函数替换原来的查找逻辑

这样处理的优点是：
1. 优先处理私立学校，避免与公立学校名称混淆
2. 使用专门的私立学校集合，保持数据的清晰性
3. 通过`isPrivate`标记清楚地区分学校类型
4. 保持了原有的名称映射和模糊匹配功能

这个改动让学校匹配的逻辑更加完整和准确，特别是在处理私立学校时。同时也保持了代码的可维护性和可读性。

---

